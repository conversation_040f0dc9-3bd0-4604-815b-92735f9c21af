using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Net;
using Microsoft.AspNetCore.Components;

namespace HuaLingErpApp.Shared;

public class ApiService(HttpClient http, NavigationManager nav)
{
    private string BuildApiUrl(string relativePath)
    {
        var baseUrl = http.BaseAddress?.ToString().TrimEnd('/') ?? nav.BaseUri.TrimEnd('/');
        if (!relativePath.StartsWith("/")) relativePath = "/" + relativePath;
        return baseUrl + relativePath;
    }

    private async Task<bool> HandleAuthenticationError(HttpResponseMessage response)
    {
        if (response.StatusCode == HttpStatusCode.Unauthorized)
        {
            try
            {
                nav.NavigateTo("/login");
                return true;
            }
            catch (NavigationException)
            {
                // 如果是服务器端渲染模式，使用强制加载
                nav.NavigateTo("/login", forceLoad: true);
                return true;
                
            }
        }

        return false;
    }

    public async Task<T?> GetAsync<T>(string relativePath)
    {
        try
        {
            var url = BuildApiUrl(relativePath);
            var response = await http.GetAsync(url);

            // Check for authentication errors
            if (await HandleAuthenticationError(response))
            {
                return default(T);
            }

            // Ensure success status code
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<T>();
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("401"))
        {
            nav.NavigateTo("/login", true);
            return default(T);
        }
        catch (JsonException)
        {
            // If JSON parsing fails, it might be because we got HTML instead of JSON
            // This could indicate an authentication issue
            nav.NavigateTo("/login", true);
            return default(T);
        }
    }

    public async Task<HttpResponseMessage> PostAsync<T>(string relativePath, T data)
    {
        try
        {
            var url = BuildApiUrl(relativePath);
            var response = await http.PostAsJsonAsync(url, data);

            // Check for authentication errors
            if (await HandleAuthenticationError(response))
            {
                // Return a dummy response to avoid null reference
                return new HttpResponseMessage(HttpStatusCode.Unauthorized);
            }

            return response;
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("401"))
        {
            nav.NavigateTo("/login", true);
            return new HttpResponseMessage(HttpStatusCode.Unauthorized);
        }
    }

    public async Task<HttpResponseMessage> PutAsync<T>(string relativePath, T data)
    {
        try
        {
            var url = BuildApiUrl(relativePath);
            var response = await http.PutAsJsonAsync(url, data);

            // Check for authentication errors
            if (await HandleAuthenticationError(response))
            {
                return new HttpResponseMessage(HttpStatusCode.Unauthorized);
            }

            return response;
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("401"))
        {
            nav.NavigateTo("/login", true);
            return new HttpResponseMessage(HttpStatusCode.Unauthorized);
        }
    }

    public async Task<HttpResponseMessage> DeleteAsync(string relativePath)
    {
        try
        {
            var url = BuildApiUrl(relativePath);
            var response = await http.DeleteAsync(url);

            // Check for authentication errors
            if (await HandleAuthenticationError(response))
            {
                return new HttpResponseMessage(HttpStatusCode.Unauthorized);
            }

            return response;
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("401"))
        {
            nav.NavigateTo("/login", true);
            return new HttpResponseMessage(HttpStatusCode.Unauthorized);
        }
    }

    public async Task<HttpResponseMessage> DeleteAsJsonAsync<T>(string relativePath, T data)
    {
        var url = BuildApiUrl(relativePath);
        return await http.DeleteAsJsonAsync(url, data);
    }

    /// <summary>
    /// 上传文件到指定的API端点
    /// </summary>
    /// <param name="relativePath">API相对路径</param>
    /// <param name="content">MultipartFormDataContent内容</param>
    /// <returns>HTTP响应</returns>
    public async Task<HttpResponseMessage> PostFileAsync(string relativePath, MultipartFormDataContent content)
    {
        try
        {
            var url = BuildApiUrl(relativePath);
            var response = await http.PostAsync(url, content);

            // Check for authentication errors
            if (await HandleAuthenticationError(response))
            {
                return new HttpResponseMessage(HttpStatusCode.Unauthorized);
            }

            return response;
        }
        catch (HttpRequestException ex) when (ex.Message.Contains("401"))
        {
            nav.NavigateTo("/login", true);
            return new HttpResponseMessage(HttpStatusCode.Unauthorized);
        }
    }
}

public static class HttpClientExtensions
{
    public static async Task<HttpResponseMessage> DeleteAsJsonAsync<T>(this HttpClient httpClient, string requestUri, T data)
    {
        var request = new HttpRequestMessage(HttpMethod.Delete, requestUri)
        {
            Content = new StringContent(JsonSerializer.Serialize(data), Encoding.UTF8, "application/json")
        };
        return await httpClient.SendAsync(request);
    }
}