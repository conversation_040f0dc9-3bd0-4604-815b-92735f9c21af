{"ConnectionStrings": {"Default": "Server=************\\OA;Database=0412;User ID=sa;Password=*********;TrustServerCertificate=True; Encrypt=True;MultipleActiveResultSets=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Hangfire": "Warning"}}, "AllowedHosts": "*", "BootstrapBlazorOptions": {"ToastDelay": 4000, "MessageDelay": 4000, "SwalDelay": 4000, "EnableErrorLogger": true, "FallbackCulture": "en", "SupportedCultures": ["zh-CN", "en-US"], "DefaultCultureInfo": "zh-CN", "TableSettings": {"CheckboxColumnWidth": 36}, "IgnoreLocalizerMissing": true, "StepSettings": {"Short": 1, "Int": 1, "Long": 1, "Float": "0.1", "Double": "0.0001", "Decimal": "0.01"}}}