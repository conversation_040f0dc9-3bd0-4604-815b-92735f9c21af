2025-07-21 08:50:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:50:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:50:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 successfully announced in 63.6212 ms
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 successfully announced in 63.8377 ms
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 all the dispatchers started
2025-07-21 08:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 all the dispatchers started
2025-07-21 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 caught stopping signal...
2025-07-21 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 caught stopping signal...
2025-07-21 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 caught stopped signal...
2025-07-21 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 caught stopped signal...
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 All dispatchers stopped
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 All dispatchers stopped
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 successfully reported itself as stopped in 2.3852 ms
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:17fc2133 has been stopped in total 868.7253 ms
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 successfully reported itself as stopped in 1.784 ms
2025-07-21 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8088:9aea6872 has been stopped in total 868.5054 ms
2025-07-21 08:51:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:51:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:51:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:51:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a successfully announced in 61.4433 ms
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 successfully announced in 61.2824 ms
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a all the dispatchers started
2025-07-21 08:51:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 all the dispatchers started
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a caught stopping signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 caught stopping signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 All dispatchers stopped
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 successfully reported itself as stopped in 2.7326 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:e3937e81 has been stopped in total 24.624 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a All dispatchers stopped
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a successfully reported itself as stopped in 1.1779 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13120:b687c83a has been stopped in total 27.4687 ms
2025-07-21 08:52:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:52:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:52:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:52:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:52:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb successfully announced in 60.727 ms
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 successfully announced in 60.9446 ms
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb all the dispatchers started
2025-07-21 08:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 all the dispatchers started
2025-07-21 08:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb caught stopping signal...
2025-07-21 08:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 caught stopping signal...
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 caught stopped signal...
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb caught stopped signal...
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 All dispatchers stopped
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb All dispatchers stopped
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 successfully reported itself as stopped in 1.9624 ms
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:b39e7970 has been stopped in total 762.9936 ms
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb successfully reported itself as stopped in 19.31 ms
2025-07-21 08:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18576:79feecfb has been stopped in total 779.936 ms
2025-07-21 08:53:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:53:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:54:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:54:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:54:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:54:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:54:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 successfully announced in 68.5866 ms
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 all the dispatchers started
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb successfully announced in 88.7372 ms
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:54:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb all the dispatchers started
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb caught stopping signal...
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 caught stopping signal...
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb caught stopped signal...
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 caught stopped signal...
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb All dispatchers stopped
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb successfully reported itself as stopped in 1.8334 ms
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:75e0ccbb has been stopped in total 777.8335 ms
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 All dispatchers stopped
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 successfully reported itself as stopped in 1.0215 ms
2025-07-21 08:57:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31944:64862f92 has been stopped in total 822.4239 ms
2025-07-21 08:59:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:59:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:59:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df successfully announced in 78.1955 ms
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e successfully announced in 78.0145 ms
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df all the dispatchers started
2025-07-21 08:59:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e all the dispatchers started
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e caught stopping signal...
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df caught stopping signal...
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df caught stopped signal...
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e caught stopped signal...
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e All dispatchers stopped
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e successfully reported itself as stopped in 2.1137 ms
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:b2db6f9e has been stopped in total 603.7383 ms
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df All dispatchers stopped
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df successfully reported itself as stopped in 0.717 ms
2025-07-21 09:06:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2332:482d18df has been stopped in total 668.6012 ms
2025-07-21 09:06:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 09:06:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 09:06:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 successfully announced in 80.9358 ms
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 successfully announced in 81.1784 ms
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 all the dispatchers started
2025-07-21 09:06:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 all the dispatchers started
2025-07-21 09:07:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 caught stopping signal...
2025-07-21 09:07:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 caught stopping signal...
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 caught stopped signal...
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 caught stopped signal...
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 All dispatchers stopped
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 successfully reported itself as stopped in 1.6579 ms
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:5b10a2d8 has been stopped in total 912.4714 ms
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 All dispatchers stopped
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 successfully reported itself as stopped in 0.7771 ms
2025-07-21 09:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32532:3bb42db9 has been stopped in total 918.879 ms
2025-07-21 09:13:51 [Error] Microsoft.EntityFrameworkCore.Database.Command: Failed executing DbCommand ("2"ms) [Parameters=[""], CommandType='Text', CommandTimeout='30']"
""CREATE TABLE [AspNetRoles] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);"
2025-07-21 10:47:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:47:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:47:19 [Fatal] Microsoft.AspNetCore.Hosting.Diagnostics: Application startup exception
System.InvalidOperationException: Scheme already exists: Identity.Application
   at Microsoft.AspNetCore.Authentication.AuthenticationOptions.AddScheme(String name, Action`1 configureBuilder)
   at Microsoft.Extensions.Options.ConfigureNamedOptions`1.Configure(String name, TOptions options)
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at Microsoft.Extensions.Options.UnnamedOptionsManager`1.get_Value()
   at Microsoft.AspNetCore.Authentication.AuthenticationSchemeProvider..ctor(IOptions`1 options, IDictionary`2 schemes)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ConstructorInfoEx.GetService(IServiceProvider serviceProvider, Int32 parameterIndex)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ConstructorMatcher.CreateInstance(IServiceProvider provider)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance(IServiceProvider provider, Type instanceType, Object[] parameters)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.ReflectionMiddlewareBinder.CreateMiddleware(RequestDelegate next)
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
2025-07-21 10:47:19 [Error] Microsoft.Extensions.Hosting.Internal.Host: Hosting failed to start
System.InvalidOperationException: Scheme already exists: Identity.Application
   at Microsoft.AspNetCore.Authentication.AuthenticationOptions.AddScheme(String name, Action`1 configureBuilder)
   at Microsoft.Extensions.Options.ConfigureNamedOptions`1.Configure(String name, TOptions options)
   at Microsoft.Extensions.Options.OptionsFactory`1.Create(String name)
   at Microsoft.Extensions.Options.UnnamedOptionsManager`1.get_Value()
   at Microsoft.AspNetCore.Authentication.AuthenticationSchemeProvider..ctor(IOptions`1 options, IDictionary`2 schemes)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ConstructorInfoEx.GetService(IServiceProvider serviceProvider, Int32 parameterIndex)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ConstructorMatcher.CreateInstance(IServiceProvider provider)
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.CreateInstance(IServiceProvider provider, Type instanceType, Object[] parameters)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.ReflectionMiddlewareBinder.CreateMiddleware(RequestDelegate next)
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-07-21 10:47:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:47:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:47:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 successfully announced in 80.6097 ms
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d successfully announced in 80.517 ms
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d all the dispatchers started
2025-07-21 10:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 all the dispatchers started
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 caught stopping signal...
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d caught stopping signal...
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d caught stopped signal...
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 caught stopped signal...
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d All dispatchers stopped
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d successfully reported itself as stopped in 2.8835 ms
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:fe6f1d7d has been stopped in total 849.0154 ms
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 All dispatchers stopped
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 successfully reported itself as stopped in 0.8189 ms
2025-07-21 10:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2488:970fa822 has been stopped in total 888.098 ms
2025-07-21 10:48:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:48:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:48:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf successfully announced in 81.4682 ms
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 successfully announced in 81.3627 ms
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf all the dispatchers started
2025-07-21 10:48:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 all the dispatchers started
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf caught stopping signal...
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 caught stopping signal...
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf All dispatchers stopped
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf successfully reported itself as stopped in 1.5949 ms
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:7b51dabf has been stopped in total 493.3477 ms
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 All dispatchers stopped
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 successfully reported itself as stopped in 0.7225 ms
2025-07-21 10:53:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20896:096ed963 has been stopped in total 503.8356 ms
2025-07-21 10:53:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:53:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:53:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 successfully announced in 80.0008 ms
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c successfully announced in 80.0495 ms
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c all the dispatchers started
2025-07-21 10:53:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 all the dispatchers started
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 caught stopping signal...
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c caught stopping signal...
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 All dispatchers stopped
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 successfully reported itself as stopped in 1.5341 ms
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:819352b5 has been stopped in total 131.5567 ms
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c All dispatchers stopped
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c successfully reported itself as stopped in 1.1805 ms
2025-07-21 10:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16212:20bba25c has been stopped in total 228.4839 ms
2025-07-21 10:54:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:54:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:54:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce successfully announced in 83.9572 ms
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 successfully announced in 84.1679 ms
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 all the dispatchers started
2025-07-21 10:54:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce all the dispatchers started
2025-07-21 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 caught stopping signal...
2025-07-21 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce caught stopping signal...
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce caught stopped signal...
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 caught stopped signal...
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce All dispatchers stopped
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce successfully reported itself as stopped in 1.7759 ms
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:c54567ce has been stopped in total 565.5911 ms
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 All dispatchers stopped
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 successfully reported itself as stopped in 0.8496 ms
2025-07-21 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:64293e38 has been stopped in total 690.2024 ms
2025-07-21 10:57:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 10:57:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 10:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 successfully announced in 83.1672 ms
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 successfully announced in 83.4098 ms
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 all the dispatchers started
2025-07-21 10:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 all the dispatchers started
2025-07-21 10:57:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 caught stopping signal...
2025-07-21 10:57:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 caught stopping signal...
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 caught stopped signal...
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 caught stopped signal...
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 All dispatchers stopped
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 All dispatchers stopped
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 successfully reported itself as stopped in 1.9708 ms
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:bd98d2d5 has been stopped in total 556.0174 ms
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 successfully reported itself as stopped in 2.2562 ms
2025-07-21 10:57:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27472:5b95cc42 has been stopped in total 555.9522 ms
2025-07-21 11:05:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:05:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: ************\OA@0412'
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 successfully announced in 87.3138 ms
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 successfully announced in 87.1408 ms
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 all the dispatchers started
2025-07-21 11:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 all the dispatchers started
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 caught stopping signal...
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 caught stopping signal...
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 All dispatchers stopped
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 All dispatchers stopped
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 successfully reported itself as stopped in 1.8206 ms
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 successfully reported itself as stopped in 2.7165 ms
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:e45a5d52 has been stopped in total 225.8011 ms
2025-07-21 11:07:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8716:c34c42a2 has been stopped in total 225.9077 ms
2025-07-21 11:07:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:07:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
