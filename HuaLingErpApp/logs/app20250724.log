2025-07-24 08:04:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:04:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:04:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac successfully announced in 65.8335 ms
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 successfully announced in 65.9493 ms
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 all the dispatchers started
2025-07-24 08:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac all the dispatchers started
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 caught stopping signal...
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac caught stopping signal...
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac caught stopped signal...
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 caught stopped signal...
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 All dispatchers stopped
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 successfully reported itself as stopped in 2.0313 ms
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ed74b920 has been stopped in total 695.0506 ms
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac All dispatchers stopped
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac successfully reported itself as stopped in 0.7357 ms
2025-07-24 08:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31468:ec3fdeac has been stopped in total 722.9228 ms
2025-07-24 08:12:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:12:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:12:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f successfully announced in 61.7631 ms
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 successfully announced in 62.1226 ms
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 all the dispatchers started
2025-07-24 08:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f all the dispatchers started
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 caught stopping signal...
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f caught stopping signal...
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f All dispatchers stopped
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f successfully reported itself as stopped in 1.548 ms
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:8185683f has been stopped in total 233.0873 ms
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 All dispatchers stopped
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 successfully reported itself as stopped in 0.8984 ms
2025-07-24 08:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27964:1fbb7192 has been stopped in total 275.654 ms
2025-07-24 08:14:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:14:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:14:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:14:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 successfully announced in 62.0119 ms
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a successfully announced in 62.2277 ms
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a all the dispatchers started
2025-07-24 08:14:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 all the dispatchers started
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 caught stopping signal...
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a caught stopping signal...
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a All dispatchers stopped
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a successfully reported itself as stopped in 1.4773 ms
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:c0c4422a has been stopped in total 41.3495 ms
2025-07-24 08:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 caught stopped signal...
2025-07-24 08:17:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 All dispatchers stopped
2025-07-24 08:17:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 successfully reported itself as stopped in 1.0424 ms
2025-07-24 08:17:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34136:f75595f3 has been stopped in total 879.4066 ms
2025-07-24 08:17:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:17:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:17:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:17:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 successfully announced in 62.1574 ms
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 successfully announced in 62.3574 ms
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 all the dispatchers started
2025-07-24 08:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 all the dispatchers started
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 caught stopping signal...
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 caught stopping signal...
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 caught stopped signal...
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 caught stopped signal...
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 All dispatchers stopped
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 successfully reported itself as stopped in 1.7928 ms
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:801be363 has been stopped in total 659.9116 ms
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 All dispatchers stopped
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 successfully reported itself as stopped in 1.1875 ms
2025-07-24 08:18:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18296:25bc1e16 has been stopped in total 729.2516 ms
2025-07-24 08:18:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:18:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:18:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 successfully announced in 61.197 ms
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a successfully announced in 61.0047 ms
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 all the dispatchers started
2025-07-24 08:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a all the dispatchers started
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a caught stopping signal...
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 caught stopping signal...
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 All dispatchers stopped
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 successfully reported itself as stopped in 1.4231 ms
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:539197d8 has been stopped in total 121.9926 ms
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a All dispatchers stopped
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a successfully reported itself as stopped in 0.776 ms
2025-07-24 08:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11936:2804c69a has been stopped in total 206.7251 ms
2025-07-24 08:20:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:20:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:20:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 successfully announced in 61.3989 ms
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 successfully announced in 61.3997 ms
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 all the dispatchers started
2025-07-24 08:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 all the dispatchers started
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 caught stopping signal...
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 caught stopping signal...
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 All dispatchers stopped
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 successfully reported itself as stopped in 1.7404 ms
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:73ff2df2 has been stopped in total 4.658 ms
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 All dispatchers stopped
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 successfully reported itself as stopped in 1.1014 ms
2025-07-24 08:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22620:8a855488 has been stopped in total 31.0796 ms
2025-07-24 08:23:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:23:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 successfully announced in 65.23 ms
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd successfully announced in 49.5906 ms
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd all the dispatchers started
2025-07-24 08:23:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 all the dispatchers started
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 caught stopping signal...
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd caught stopping signal...
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 All dispatchers stopped
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 successfully reported itself as stopped in 1.9345 ms
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:ab302805 has been stopped in total 126.7217 ms
2025-07-24 08:26:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd caught stopped signal...
2025-07-24 08:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd All dispatchers stopped
2025-07-24 08:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd successfully reported itself as stopped in 1.4403 ms
2025-07-24 08:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25676:473a4abd has been stopped in total 981.452 ms
2025-07-24 08:26:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:26:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:26:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:26:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 successfully announced in 60.6445 ms
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 successfully announced in 60.4386 ms
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 all the dispatchers started
2025-07-24 08:26:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 all the dispatchers started
2025-07-24 08:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 caught stopping signal...
2025-07-24 08:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 caught stopping signal...
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 caught stopped signal...
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 caught stopped signal...
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 All dispatchers stopped
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 successfully reported itself as stopped in 1.5315 ms
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:80a50e72 has been stopped in total 972.8027 ms
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 All dispatchers stopped
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 successfully reported itself as stopped in 0.6309 ms
2025-07-24 08:27:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21584:1dc8f5e6 has been stopped in total 980.8152 ms
2025-07-24 08:27:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:27:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:27:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:27:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 successfully announced in 65.6243 ms
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 successfully announced in 65.8804 ms
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 all the dispatchers started
2025-07-24 08:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 all the dispatchers started
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 caught stopping signal...
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 caught stopping signal...
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 All dispatchers stopped
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 successfully reported itself as stopped in 1.6586 ms
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:19defc23 has been stopped in total 149.487 ms
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 All dispatchers stopped
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 successfully reported itself as stopped in 0.8152 ms
2025-07-24 08:29:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14556:116960d8 has been stopped in total 178.1502 ms
2025-07-24 08:29:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:29:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:29:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:29:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a successfully announced in 57.418 ms
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 successfully announced in 58.2504 ms
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a all the dispatchers started
2025-07-24 08:29:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 all the dispatchers started
2025-07-24 08:36:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 caught stopping signal...
2025-07-24 08:36:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a caught stopping signal...
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 All dispatchers stopped
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 successfully reported itself as stopped in 1.674 ms
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:fc054594 has been stopped in total 133.0436 ms
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a All dispatchers stopped
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a successfully reported itself as stopped in 0.7375 ms
2025-07-24 08:36:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33936:a4a3a02a has been stopped in total 135.3032 ms
2025-07-24 08:42:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 08:42:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 08:42:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 08:42:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c successfully announced in 56.0084 ms
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a successfully announced in 64.4745 ms
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c all the dispatchers started
2025-07-24 08:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a all the dispatchers started
2025-07-24 08:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a caught stopping signal...
2025-07-24 08:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c caught stopping signal...
2025-07-24 08:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c caught stopped signal...
2025-07-24 08:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a caught stopped signal...
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c All dispatchers stopped
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a All dispatchers stopped
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a successfully reported itself as stopped in 1.1633 ms
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c successfully reported itself as stopped in 2.4717 ms
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:2c87d21a has been stopped in total 859.2461 ms
2025-07-24 08:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24096:f63ff74c has been stopped in total 859.0805 ms
2025-07-24 09:30:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:30:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:30:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:30:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc successfully announced in 62.0317 ms
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 successfully announced in 61.9247 ms
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 all the dispatchers started
2025-07-24 09:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc all the dispatchers started
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 caught stopping signal...
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc caught stopping signal...
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc caught stopped signal...
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 caught stopped signal...
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc All dispatchers stopped
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc successfully reported itself as stopped in 2.7615 ms
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:e5d400fc has been stopped in total 655.9138 ms
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 All dispatchers stopped
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 successfully reported itself as stopped in 1.0255 ms
2025-07-24 09:33:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28264:f4089553 has been stopped in total 724.9233 ms
2025-07-24 09:33:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:33:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:33:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:33:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 successfully announced in 63.9905 ms
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c successfully announced in 64.229 ms
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c all the dispatchers started
2025-07-24 09:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 all the dispatchers started
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c caught stopping signal...
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 caught stopping signal...
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 All dispatchers stopped
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c All dispatchers stopped
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 successfully reported itself as stopped in 2.2028 ms
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:968d22b2 has been stopped in total 293.2849 ms
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c successfully reported itself as stopped in 1.2453 ms
2025-07-24 09:34:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17780:d368225c has been stopped in total 296.85 ms
2025-07-24 09:34:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:34:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:34:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:34:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:34:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:34:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:34:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:34:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 successfully announced in 62.8385 ms
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 successfully announced in 63.1404 ms
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 all the dispatchers started
2025-07-24 09:34:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 all the dispatchers started
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 caught stopping signal...
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 caught stopping signal...
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 All dispatchers stopped
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 successfully reported itself as stopped in 3.1213 ms
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:b0cbc3e5 has been stopped in total 52.4983 ms
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 All dispatchers stopped
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 successfully reported itself as stopped in 0.7221 ms
2025-07-24 09:34:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:a2cc1c19 has been stopped in total 64.2741 ms
2025-07-24 09:34:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:34:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 successfully announced in 60.9582 ms
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 successfully announced in 60.3148 ms
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 all the dispatchers started
2025-07-24 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 all the dispatchers started
2025-07-24 09:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 caught stopping signal...
2025-07-24 09:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 caught stopping signal...
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 All dispatchers stopped
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 successfully reported itself as stopped in 2.4713 ms
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:0299fe75 has been stopped in total 157.9168 ms
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 All dispatchers stopped
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 successfully reported itself as stopped in 1.3197 ms
2025-07-24 09:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28956:71a4a467 has been stopped in total 173.65 ms
2025-07-24 09:36:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:36:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:36:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:36:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 successfully announced in 62.0544 ms
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 successfully announced in 62.8997 ms
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 all the dispatchers started
2025-07-24 09:36:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 all the dispatchers started
2025-07-24 09:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 caught stopping signal...
2025-07-24 09:52:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 caught stopping signal...
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 caught stopped signal...
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 caught stopped signal...
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 All dispatchers stopped
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 All dispatchers stopped
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 successfully reported itself as stopped in 1.6605 ms
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:95c20904 has been stopped in total 976.3312 ms
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 successfully reported itself as stopped in 19.5936 ms
2025-07-24 09:52:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:26e51dc6 has been stopped in total 992.8412 ms
2025-07-24 09:53:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:53:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:53:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 successfully announced in 65.0501 ms
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c successfully announced in 65.0501 ms
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 all the dispatchers started
2025-07-24 09:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c all the dispatchers started
2025-07-24 09:53:07 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Failed to compare two elements in the array."
System.InvalidOperationException: Failed to compare two elements in the array.
 ---> System.InvalidOperationException: The following routes are ambiguous:
                    'finance/parameters' in 'HuaLingErpApp.Client.Pages.Finance.ParametersPageNew'
                    'finance/parameters' in 'HuaLingErpApp.Client.Pages.Finance.ParametersPage'
                    
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.RouteComparison(RouteEntry x, RouteEntry y)
   at System.Collections.Generic.ArraySortHelper`1.InsertionSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntroSort(Span`1 keys, Int32 depthLimit, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntrospectiveSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   --- End of inner exception stack trace ---
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   at System.Array.Sort[T](T[] array, Int32 index, Int32 length, IComparer`1 comparer)
   at System.Collections.Generic.List`1.Sort(Int32 index, Int32 count, IComparer`1 comparer)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(Dictionary`2 templatesByHandler)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(List`1 componentTypes)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(RouteKey routeKey)
   at BootstrapBlazor.Components.RouteTableFactory.RefreshRouteTable(IEnumerable`1 assemblies)
   at BootstrapBlazor.Components.RouteTableFactory.Create(IEnumerable`1 assemblies, String url)
   at BootstrapBlazor.Components.Layout.OnInitializedAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
2025-07-24 09:53:07 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Failed to compare two elements in the array.
 ---> System.InvalidOperationException: The following routes are ambiguous:
                    'finance/parameters' in 'HuaLingErpApp.Client.Pages.Finance.ParametersPageNew'
                    'finance/parameters' in 'HuaLingErpApp.Client.Pages.Finance.ParametersPage'
                    
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.RouteComparison(RouteEntry x, RouteEntry y)
   at System.Collections.Generic.ArraySortHelper`1.InsertionSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntroSort(Span`1 keys, Int32 depthLimit, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntrospectiveSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   --- End of inner exception stack trace ---
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   at System.Array.Sort[T](T[] array, Int32 index, Int32 length, IComparer`1 comparer)
   at System.Collections.Generic.List`1.Sort(Int32 index, Int32 count, IComparer`1 comparer)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(Dictionary`2 templatesByHandler)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(List`1 componentTypes)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(RouteKey routeKey)
   at BootstrapBlazor.Components.RouteTableFactory.RefreshRouteTable(IEnumerable`1 assemblies)
   at BootstrapBlazor.Components.RouteTableFactory.Create(IEnumerable`1 assemblies, String url)
   at BootstrapBlazor.Components.Layout.OnInitializedAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Localization.RequestLocalizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-24 09:53:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 caught stopping signal...
2025-07-24 09:53:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c caught stopping signal...
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c caught stopped signal...
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 caught stopped signal...
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c All dispatchers stopped
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 All dispatchers stopped
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c successfully reported itself as stopped in 2.346 ms
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 successfully reported itself as stopped in 1.3256 ms
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:19459d8c has been stopped in total 942.2456 ms
2025-07-24 09:53:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32560:cc942ad8 has been stopped in total 942.605 ms
2025-07-24 09:53:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:53:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:53:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:53:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:53:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:53:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de successfully announced in 79.6336 ms
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 successfully announced in 80.6343 ms
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 all the dispatchers started
2025-07-24 09:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de all the dispatchers started
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de caught stopping signal...
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 caught stopping signal...
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de All dispatchers stopped
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de successfully reported itself as stopped in 1.5736 ms
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:407194de has been stopped in total 111.707 ms
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 All dispatchers stopped
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 successfully reported itself as stopped in 0.691 ms
2025-07-24 09:55:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21120:2325a1f8 has been stopped in total 232.7891 ms
2025-07-24 09:55:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:55:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:55:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:55:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 successfully announced in 66.2849 ms
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 successfully announced in 66.5077 ms
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 all the dispatchers started
2025-07-24 09:55:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 all the dispatchers started
2025-07-24 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 caught stopping signal...
2025-07-24 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 caught stopping signal...
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 All dispatchers stopped
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 All dispatchers stopped
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 successfully reported itself as stopped in 2.6611 ms
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:7cc985f9 has been stopped in total 486.4414 ms
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 successfully reported itself as stopped in 1.1021 ms
2025-07-24 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23280:e6ca4c98 has been stopped in total 488.261 ms
2025-07-24 09:58:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:58:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:58:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:58:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 successfully announced in 62.3508 ms
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 successfully announced in 62.4919 ms
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 all the dispatchers started
2025-07-24 09:58:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 all the dispatchers started
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 caught stopping signal...
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 caught stopping signal...
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 caught stopped signal...
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 caught stopped signal...
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 All dispatchers stopped
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 successfully reported itself as stopped in 1.5799 ms
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:dbd3e937 has been stopped in total 571.3656 ms
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 All dispatchers stopped
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 successfully reported itself as stopped in 1.241 ms
2025-07-24 09:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34732:1dd3e9b3 has been stopped in total 590.9527 ms
2025-07-24 09:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 09:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 09:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 09:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 09:59:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 09:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 successfully announced in 62.534 ms
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 successfully announced in 62.5351 ms
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 all the dispatchers started
2025-07-24 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 all the dispatchers started
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 caught stopping signal...
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 caught stopping signal...
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 All dispatchers stopped
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 successfully reported itself as stopped in 1.4809 ms
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:933202b6 has been stopped in total 269.2233 ms
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 All dispatchers stopped
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 successfully reported itself as stopped in 0.6954 ms
2025-07-24 10:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30380:1c5f6342 has been stopped in total 310.9062 ms
2025-07-24 10:01:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:01:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b successfully announced in 61.3469 ms
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 successfully announced in 61.152 ms
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 all the dispatchers started
2025-07-24 10:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b all the dispatchers started
2025-07-24 10:01:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b caught stopping signal...
2025-07-24 10:01:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 caught stopping signal...
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 caught stopped signal...
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b caught stopped signal...
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b All dispatchers stopped
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b successfully reported itself as stopped in 1.8752 ms
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:8d69e60b has been stopped in total 939.5386 ms
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 All dispatchers stopped
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 successfully reported itself as stopped in 1.5802 ms
2025-07-24 10:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20304:1c620a44 has been stopped in total 947.6826 ms
2025-07-24 10:02:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:02:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:02:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:02:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:02:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:02:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:02:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:02:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 successfully announced in 61.4997 ms
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d successfully announced in 61.7928 ms
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 all the dispatchers started
2025-07-24 10:02:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d all the dispatchers started
2025-07-24 10:10:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d caught stopping signal...
2025-07-24 10:10:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 caught stopping signal...
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 All dispatchers stopped
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 successfully reported itself as stopped in 2.1276 ms
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:62a31b15 has been stopped in total 393.5803 ms
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d caught stopped signal...
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d All dispatchers stopped
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d successfully reported itself as stopped in 0.7555 ms
2025-07-24 10:10:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10076:6fd5059d has been stopped in total 541.9036 ms
2025-07-24 10:10:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:10:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:10:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:10:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:10:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:10:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:10:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:10:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 successfully announced in 61.5349 ms
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a successfully announced in 61.533 ms
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a all the dispatchers started
2025-07-24 10:10:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 all the dispatchers started
2025-07-24 10:16:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a caught stopping signal...
2025-07-24 10:16:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 caught stopping signal...
2025-07-24 10:16:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 caught stopped signal...
2025-07-24 10:16:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a caught stopped signal...
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 All dispatchers stopped
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a All dispatchers stopped
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 successfully reported itself as stopped in 2.39 ms
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:97b44532 has been stopped in total 892.3351 ms
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a successfully reported itself as stopped in 1.149 ms
2025-07-24 10:16:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19616:a45c8f6a has been stopped in total 893.6116 ms
2025-07-24 10:16:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:16:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:16:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:16:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 successfully announced in 67.039 ms
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 successfully announced in 67.0397 ms
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 all the dispatchers started
2025-07-24 10:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 all the dispatchers started
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 caught stopping signal...
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 caught stopping signal...
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 All dispatchers stopped
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 successfully reported itself as stopped in 1.592 ms
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:bafceb06 has been stopped in total 447.0614 ms
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 All dispatchers stopped
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 successfully reported itself as stopped in 1.0812 ms
2025-07-24 10:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33572:a464d824 has been stopped in total 500.9403 ms
2025-07-24 10:22:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:22:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:22:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf successfully announced in 66.0867 ms
2025-07-24 10:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d successfully announced in 66.0837 ms
2025-07-24 10:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:22:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d all the dispatchers started
2025-07-24 10:22:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf all the dispatchers started
2025-07-24 10:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d caught stopping signal...
2025-07-24 10:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf caught stopping signal...
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf All dispatchers stopped
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf successfully reported itself as stopped in 1.5187 ms
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:f2c32bdf has been stopped in total 69.5327 ms
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d All dispatchers stopped
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d successfully reported itself as stopped in 0.876 ms
2025-07-24 10:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8592:bffda93d has been stopped in total 150.3506 ms
2025-07-24 10:23:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:23:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:23:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d successfully announced in 64.0447 ms
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 successfully announced in 64.3617 ms
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 all the dispatchers started
2025-07-24 10:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d all the dispatchers started
2025-07-24 10:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d caught stopping signal...
2025-07-24 10:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 caught stopping signal...
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 caught stopped signal...
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d caught stopped signal...
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d All dispatchers stopped
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d successfully reported itself as stopped in 2.3658 ms
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:a9b3353d has been stopped in total 714.921 ms
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 All dispatchers stopped
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 successfully reported itself as stopped in 0.7844 ms
2025-07-24 10:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15760:3e4b4ab5 has been stopped in total 719.7325 ms
2025-07-24 10:25:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:25:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f successfully announced in 61.7357 ms
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 successfully announced in 62.666 ms
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 all the dispatchers started
2025-07-24 10:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f all the dispatchers started
2025-07-24 10:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f caught stopping signal...
2025-07-24 10:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 caught stopping signal...
2025-07-24 10:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 caught stopped signal...
2025-07-24 10:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f caught stopped signal...
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 All dispatchers stopped
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f All dispatchers stopped
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f successfully reported itself as stopped in 0.6214 ms
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:9b98d61f has been stopped in total 982.6399 ms
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 successfully reported itself as stopped in 1.8734 ms
2025-07-24 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31836:a4433689 has been stopped in total 982.7648 ms
2025-07-24 10:27:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:27:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:27:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:27:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 successfully announced in 65.6184 ms
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d successfully announced in 39.9051 ms
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 all the dispatchers started
2025-07-24 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d all the dispatchers started
2025-07-24 10:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 caught stopping signal...
2025-07-24 10:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d caught stopping signal...
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d caught stopped signal...
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 caught stopped signal...
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 All dispatchers stopped
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 successfully reported itself as stopped in 1.6942 ms
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:be249fc6 has been stopped in total 751.7208 ms
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d All dispatchers stopped
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d successfully reported itself as stopped in 0.8914 ms
2025-07-24 10:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32588:e74fc77d has been stopped in total 761.6239 ms
2025-07-24 10:29:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:29:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:29:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:29:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 successfully announced in 62.4959 ms
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 successfully announced in 62.6231 ms
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 all the dispatchers started
2025-07-24 10:29:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 all the dispatchers started
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 caught stopping signal...
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 caught stopping signal...
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 caught stopped signal...
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 caught stopped signal...
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 All dispatchers stopped
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 All dispatchers stopped
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 successfully reported itself as stopped in 2.3468 ms
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:30d4b577 has been stopped in total 904.8253 ms
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 successfully reported itself as stopped in 0.7437 ms
2025-07-24 10:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27512:0f1b5203 has been stopped in total 904.7114 ms
2025-07-24 10:32:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:32:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:32:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:32:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d successfully announced in 71.8575 ms
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 successfully announced in 72.3672 ms
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d all the dispatchers started
2025-07-24 10:32:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 all the dispatchers started
2025-07-24 10:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d caught stopping signal...
2025-07-24 10:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 caught stopping signal...
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d All dispatchers stopped
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 All dispatchers stopped
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d successfully reported itself as stopped in 1.6667 ms
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:87ab252d has been stopped in total 30.4373 ms
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 successfully reported itself as stopped in 0.6466 ms
2025-07-24 10:35:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29404:b37c4d33 has been stopped in total 31.092 ms
2025-07-24 10:35:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:35:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:35:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f successfully announced in 62.2867 ms
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a successfully announced in 62.286 ms
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f all the dispatchers started
2025-07-24 10:35:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a all the dispatchers started
2025-07-24 10:39:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a caught stopping signal...
2025-07-24 10:39:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f caught stopping signal...
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f caught stopped signal...
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a caught stopped signal...
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a All dispatchers stopped
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a successfully reported itself as stopped in 2.3101 ms
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:703c126a has been stopped in total 954.8879 ms
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f All dispatchers stopped
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f successfully reported itself as stopped in 0.9295 ms
2025-07-24 10:39:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26208:f6eab24f has been stopped in total 957.2013 ms
2025-07-24 10:39:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:39:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 successfully announced in 62.8503 ms
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab successfully announced in 62.9334 ms
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 all the dispatchers started
2025-07-24 10:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab all the dispatchers started
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 caught stopping signal...
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab caught stopping signal...
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab All dispatchers stopped
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab successfully reported itself as stopped in 2.5135 ms
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:f27b8cab has been stopped in total 489.554 ms
2025-07-24 10:51:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 caught stopped signal...
2025-07-24 10:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 All dispatchers stopped
2025-07-24 10:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 successfully reported itself as stopped in 0.9247 ms
2025-07-24 10:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:0f29de74 has been stopped in total 757.5466 ms
2025-07-24 10:52:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:52:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:52:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:52:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 successfully announced in 63.1188 ms
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a successfully announced in 63.0847 ms
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 all the dispatchers started
2025-07-24 10:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a all the dispatchers started
2025-07-24 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 caught stopping signal...
2025-07-24 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a caught stopping signal...
2025-07-24 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 All dispatchers stopped
2025-07-24 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 successfully reported itself as stopped in 2.4365 ms
2025-07-24 10:57:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:2e90dce6 has been stopped in total 16.1898 ms
2025-07-24 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a caught stopped signal...
2025-07-24 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a All dispatchers stopped
2025-07-24 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a successfully reported itself as stopped in 0.8881 ms
2025-07-24 10:57:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22056:5dc3208a has been stopped in total 906.9109 ms
2025-07-24 10:57:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:57:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:57:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:57:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:57:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:57:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 successfully announced in 64.9545 ms
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 successfully announced in 64.9402 ms
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 all the dispatchers started
2025-07-24 10:57:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 all the dispatchers started
2025-07-24 10:59:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 caught stopping signal...
2025-07-24 10:59:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 caught stopping signal...
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 caught stopped signal...
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 caught stopped signal...
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 All dispatchers stopped
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 All dispatchers stopped
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 successfully reported itself as stopped in 1.6568 ms
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:49b538d0 has been stopped in total 615.495 ms
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 successfully reported itself as stopped in 3.9714 ms
2025-07-24 10:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14480:ccc15393 has been stopped in total 617.2065 ms
2025-07-24 10:59:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 10:59:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 10:59:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 10:59:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 successfully announced in 58.0724 ms
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 successfully announced in 58.2548 ms
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 all the dispatchers started
2025-07-24 10:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 all the dispatchers started
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 caught stopping signal...
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 caught stopping signal...
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 All dispatchers stopped
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 successfully reported itself as stopped in 2.3053 ms
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:68550437 has been stopped in total 116.2536 ms
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 All dispatchers stopped
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 successfully reported itself as stopped in 0.8134 ms
2025-07-24 11:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11484:cc77bcf8 has been stopped in total 275.4173 ms
2025-07-24 11:00:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:00:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:00:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:00:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 successfully announced in 60.6035 ms
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 successfully announced in 60.4675 ms
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 all the dispatchers started
2025-07-24 11:00:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 all the dispatchers started
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 caught stopping signal...
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 caught stopping signal...
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 All dispatchers stopped
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 successfully reported itself as stopped in 1.7979 ms
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:034cb332 has been stopped in total 33.2769 ms
2025-07-24 11:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 caught stopped signal...
2025-07-24 11:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 All dispatchers stopped
2025-07-24 11:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 successfully reported itself as stopped in 1.9199 ms
2025-07-24 11:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26664:6fee6590 has been stopped in total 1002.5556 ms
2025-07-24 11:04:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:04:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:04:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:04:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce successfully announced in 64.0084 ms
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 successfully announced in 64.2023 ms
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 all the dispatchers started
2025-07-24 11:04:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce all the dispatchers started
2025-07-24 11:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 caught stopping signal...
2025-07-24 11:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce caught stopping signal...
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce caught stopped signal...
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 caught stopped signal...
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce All dispatchers stopped
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 All dispatchers stopped
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 successfully reported itself as stopped in 0.8724 ms
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:ac5c95c7 has been stopped in total 645.8741 ms
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce successfully reported itself as stopped in 2.4405 ms
2025-07-24 11:10:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21276:b8cfd6ce has been stopped in total 645.7711 ms
2025-07-24 11:10:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:10:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:10:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:10:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:10:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:10:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:10:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 successfully announced in 63.4262 ms
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 successfully announced in 63.8743 ms
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 all the dispatchers started
2025-07-24 11:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 all the dispatchers started
2025-07-24 11:21:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 caught stopping signal...
2025-07-24 11:21:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 caught stopping signal...
2025-07-24 11:21:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 caught stopped signal...
2025-07-24 11:21:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 caught stopped signal...
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 All dispatchers stopped
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 successfully reported itself as stopped in 3.949 ms
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:29189607 has been stopped in total 873.3764 ms
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 All dispatchers stopped
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 successfully reported itself as stopped in 1.1904 ms
2025-07-24 11:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27492:a87c6ad8 has been stopped in total 914.1781 ms
2025-07-24 11:21:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:22:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:22:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:22:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 successfully announced in 65.9302 ms
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 successfully announced in 66.3369 ms
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 all the dispatchers started
2025-07-24 11:22:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 all the dispatchers started
2025-07-24 11:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 caught stopping signal...
2025-07-24 11:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 caught stopping signal...
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 caught stopped signal...
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 caught stopped signal...
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 All dispatchers stopped
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 All dispatchers stopped
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 successfully reported itself as stopped in 1.0709 ms
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:6b6b38b7 has been stopped in total 718.1391 ms
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 successfully reported itself as stopped in 2.1767 ms
2025-07-24 11:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29528:38841d33 has been stopped in total 718.4937 ms
2025-07-24 11:32:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:32:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:32:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:32:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 successfully announced in 68.4811 ms
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 successfully announced in 68.4807 ms
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 all the dispatchers started
2025-07-24 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 all the dispatchers started
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 caught stopping signal...
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 caught stopping signal...
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 caught stopped signal...
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 caught stopped signal...
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 All dispatchers stopped
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 successfully reported itself as stopped in 1.7495 ms
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:f7a276f2 has been stopped in total 574.7962 ms
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 All dispatchers stopped
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 successfully reported itself as stopped in 0.8731 ms
2025-07-24 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20800:09fefdb7 has been stopped in total 784.3423 ms
2025-07-24 11:39:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 11:39:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 11:39:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 11:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 successfully announced in 63.464 ms
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df successfully announced in 64.2712 ms
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 all the dispatchers started
2025-07-24 11:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df all the dispatchers started
2025-07-24 12:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df caught stopping signal...
2025-07-24 12:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 caught stopping signal...
2025-07-24 12:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 All dispatchers stopped
2025-07-24 12:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 successfully reported itself as stopped in 3.0385 ms
2025-07-24 12:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:522c5614 has been stopped in total 221.3903 ms
2025-07-24 12:29:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df caught stopped signal...
2025-07-24 12:29:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df All dispatchers stopped
2025-07-24 12:29:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df successfully reported itself as stopped in 0.8724 ms
2025-07-24 12:29:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18388:e75360df has been stopped in total 814.9573 ms
2025-07-24 12:29:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 12:29:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 12:29:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 12:29:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd successfully announced in 65.3462 ms
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 successfully announced in 65.5023 ms
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd all the dispatchers started
2025-07-24 12:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 all the dispatchers started
2025-07-24 13:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd caught stopping signal...
2025-07-24 13:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 caught stopping signal...
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 All dispatchers stopped
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 successfully reported itself as stopped in 2.0294 ms
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:62d32c59 has been stopped in total 219.6902 ms
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd caught stopped signal...
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd All dispatchers stopped
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd successfully reported itself as stopped in 0.9475 ms
2025-07-24 13:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26652:82a343dd has been stopped in total 536.9451 ms
2025-07-24 13:05:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:05:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:05:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:05:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f successfully announced in 61.7181 ms
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 successfully announced in 62.6755 ms
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 all the dispatchers started
2025-07-24 13:05:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f all the dispatchers started
2025-07-24 13:07:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 caught stopping signal...
2025-07-24 13:07:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f caught stopping signal...
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 All dispatchers stopped
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 successfully reported itself as stopped in 4.6214 ms
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:f24938b2 has been stopped in total 138.6576 ms
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f All dispatchers stopped
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f successfully reported itself as stopped in 0.8189 ms
2025-07-24 13:07:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21028:be22624f has been stopped in total 576.5751 ms
2025-07-24 13:07:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:07:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:07:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed successfully announced in 63.5515 ms
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 successfully announced in 64.5386 ms
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed all the dispatchers started
2025-07-24 13:07:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 all the dispatchers started
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 caught stopping signal...
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed caught stopping signal...
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed All dispatchers stopped
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 All dispatchers stopped
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed successfully reported itself as stopped in 1.0233 ms
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 successfully reported itself as stopped in 1.8371 ms
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:d00c4042 has been stopped in total 28.3147 ms
2025-07-24 13:07:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23156:8c69c8ed has been stopped in total 28.0308 ms
2025-07-24 13:07:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:07:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 successfully announced in 61.474 ms
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 successfully announced in 40.8277 ms
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 all the dispatchers started
2025-07-24 13:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 all the dispatchers started
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 caught stopping signal...
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 caught stopping signal...
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 All dispatchers stopped
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 All dispatchers stopped
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 successfully reported itself as stopped in 1.541 ms
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:3e7f4497 has been stopped in total 261.1888 ms
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 successfully reported itself as stopped in 0.7192 ms
2025-07-24 13:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:2ae6bcb3 has been stopped in total 263.7676 ms
2025-07-24 13:09:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:09:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:09:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:09:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:09:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:09:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 successfully announced in 62.511 ms
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd successfully announced in 62.8777 ms
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd all the dispatchers started
2025-07-24 13:09:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 all the dispatchers started
2025-07-24 13:10:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd caught stopping signal...
2025-07-24 13:10:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 caught stopping signal...
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 caught stopped signal...
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd caught stopped signal...
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 All dispatchers stopped
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 successfully reported itself as stopped in 1.91 ms
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:c9b65ea1 has been stopped in total 896.3058 ms
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd All dispatchers stopped
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd successfully reported itself as stopped in 1.0834 ms
2025-07-24 13:10:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31660:226140dd has been stopped in total 919.2293 ms
2025-07-24 13:10:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:10:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:10:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:10:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 successfully announced in 62.8158 ms
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 successfully announced in 63.6596 ms
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 all the dispatchers started
2025-07-24 13:10:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 all the dispatchers started
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 caught stopping signal...
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 caught stopping signal...
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 caught stopped signal...
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 caught stopped signal...
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 All dispatchers stopped
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 All dispatchers stopped
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 successfully reported itself as stopped in 1.5304 ms
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 successfully reported itself as stopped in 0.7273 ms
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:2ebdc451 has been stopped in total 702.4886 ms
2025-07-24 13:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5980:0e63ae08 has been stopped in total 702.3527 ms
2025-07-24 13:12:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:12:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 successfully announced in 60.3591 ms
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e successfully announced in 59.9861 ms
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e all the dispatchers started
2025-07-24 13:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 all the dispatchers started
2025-07-24 13:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 caught stopping signal...
2025-07-24 13:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e caught stopping signal...
2025-07-24 13:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e All dispatchers stopped
2025-07-24 13:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e successfully reported itself as stopped in 3.3071 ms
2025-07-24 13:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:882bdb9e has been stopped in total 441.6306 ms
2025-07-24 13:14:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 caught stopped signal...
2025-07-24 13:14:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 All dispatchers stopped
2025-07-24 13:14:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 successfully reported itself as stopped in 1.0548 ms
2025-07-24 13:14:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25756:a134b3f8 has been stopped in total 512.1551 ms
2025-07-24 13:14:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:14:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:14:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:14:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 successfully announced in 62.6901 ms
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 successfully announced in 62.6909 ms
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 all the dispatchers started
2025-07-24 13:14:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 all the dispatchers started
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 caught stopping signal...
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 caught stopping signal...
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 All dispatchers stopped
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 All dispatchers stopped
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 successfully reported itself as stopped in 1.873 ms
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 successfully reported itself as stopped in 2.054 ms
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:4203ded7 has been stopped in total 370.4219 ms
2025-07-24 13:15:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28740:48f2c6d1 has been stopped in total 370.2922 ms
2025-07-24 13:15:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:15:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 successfully announced in 60.9424 ms
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 successfully announced in 61.7573 ms
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 all the dispatchers started
2025-07-24 13:15:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 all the dispatchers started
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 caught stopping signal...
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 caught stopping signal...
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 All dispatchers stopped
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 successfully reported itself as stopped in 1.6828 ms
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:bc50f820 has been stopped in total 318.3533 ms
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 All dispatchers stopped
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 successfully reported itself as stopped in 0.9632 ms
2025-07-24 13:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:e0f70383 has been stopped in total 348.5836 ms
2025-07-24 13:22:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:22:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:22:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:22:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c successfully announced in 63.6864 ms
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 successfully announced in 63.6864 ms
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c all the dispatchers started
2025-07-24 13:22:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 all the dispatchers started
2025-07-24 13:25:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 caught stopping signal...
2025-07-24 13:25:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c caught stopping signal...
2025-07-24 13:25:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c caught stopped signal...
2025-07-24 13:25:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 caught stopped signal...
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 All dispatchers stopped
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 successfully reported itself as stopped in 11.8809 ms
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7b1a9bf6 has been stopped in total 889.5237 ms
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c All dispatchers stopped
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c successfully reported itself as stopped in 0.7404 ms
2025-07-24 13:25:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12872:7ac5409c has been stopped in total 942.7222 ms
2025-07-24 13:25:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:25:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:25:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:25:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 successfully announced in 65.4301 ms
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c successfully announced in 65.6393 ms
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c all the dispatchers started
2025-07-24 13:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 all the dispatchers started
2025-07-24 13:26:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c caught stopping signal...
2025-07-24 13:26:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 caught stopping signal...
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 caught stopped signal...
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c caught stopped signal...
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 All dispatchers stopped
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c All dispatchers stopped
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c successfully reported itself as stopped in 1.7026 ms
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:fbc2a09c has been stopped in total 675.7186 ms
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 successfully reported itself as stopped in 0.8837 ms
2025-07-24 13:26:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:21281901 has been stopped in total 676.1113 ms
2025-07-24 13:26:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:26:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:26:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:26:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 successfully announced in 67.5145 ms
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 successfully announced in 68.912 ms
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 all the dispatchers started
2025-07-24 13:26:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 all the dispatchers started
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 caught stopping signal...
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 caught stopping signal...
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 All dispatchers stopped
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 successfully reported itself as stopped in 2.2852 ms
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:90d33188 has been stopped in total 124.1177 ms
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 All dispatchers stopped
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 successfully reported itself as stopped in 1.6462 ms
2025-07-24 13:26:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32920:dead53e9 has been stopped in total 222.3466 ms
2025-07-24 13:27:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:27:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:27:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:27:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 successfully announced in 81.7584 ms
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb successfully announced in 81.7591 ms
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 all the dispatchers started
2025-07-24 13:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb all the dispatchers started
2025-07-24 13:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 caught stopping signal...
2025-07-24 13:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb caught stopping signal...
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb caught stopped signal...
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 caught stopped signal...
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb All dispatchers stopped
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 All dispatchers stopped
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb successfully reported itself as stopped in 1.138 ms
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f5472abb has been stopped in total 762.2714 ms
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 successfully reported itself as stopped in 2.2599 ms
2025-07-24 13:27:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31212:f9de2e24 has been stopped in total 762.8356 ms
2025-07-24 13:27:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:27:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:27:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:27:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc successfully announced in 64.7563 ms
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 successfully announced in 64.7544 ms
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc all the dispatchers started
2025-07-24 13:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 all the dispatchers started
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 caught stopping signal...
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc caught stopping signal...
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc caught stopped signal...
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 caught stopped signal...
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 All dispatchers stopped
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc All dispatchers stopped
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc successfully reported itself as stopped in 2.3255 ms
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 successfully reported itself as stopped in 2.3387 ms
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:4cfd49dc has been stopped in total 869.841 ms
2025-07-24 13:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:6798bb99 has been stopped in total 870.1077 ms
2025-07-24 13:28:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 13:28:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 13:28:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 13:28:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 13:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 successfully announced in 61.7686 ms
2025-07-24 13:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e successfully announced in 62.6894 ms
2025-07-24 13:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 13:28:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 all the dispatchers started
2025-07-24 13:28:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e all the dispatchers started
2025-07-24 14:07:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 caught stopping signal...
2025-07-24 14:07:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e caught stopping signal...
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e All dispatchers stopped
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e successfully reported itself as stopped in 2.5952 ms
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:6e65ab5e has been stopped in total 420.4947 ms
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 All dispatchers stopped
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 successfully reported itself as stopped in 1.4813 ms
2025-07-24 14:07:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8212:b2b78fb6 has been stopped in total 438.9936 ms
2025-07-24 14:08:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:08:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 successfully announced in 63.1144 ms
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f successfully announced in 107.9642 ms
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 all the dispatchers started
2025-07-24 14:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f all the dispatchers started
2025-07-24 14:13:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 caught stopping signal...
2025-07-24 14:13:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f caught stopping signal...
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 All dispatchers stopped
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 successfully reported itself as stopped in 2.0679 ms
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2c44d3b8 has been stopped in total 229.18 ms
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f All dispatchers stopped
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f successfully reported itself as stopped in 1.4425 ms
2025-07-24 14:13:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:2db19f0f has been stopped in total 600.094 ms
2025-07-24 14:13:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:13:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:13:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:13:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e successfully announced in 65.7349 ms
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b successfully announced in 65.7936 ms
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b all the dispatchers started
2025-07-24 14:13:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e all the dispatchers started
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b caught stopping signal...
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e caught stopping signal...
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b All dispatchers stopped
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b successfully reported itself as stopped in 2.7864 ms
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:95e6372b has been stopped in total 169.0499 ms
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e All dispatchers stopped
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e successfully reported itself as stopped in 1.5348 ms
2025-07-24 14:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26996:2163eb1e has been stopped in total 189.908 ms
2025-07-24 14:19:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:19:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:19:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc successfully announced in 60.5551 ms
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 successfully announced in 65.3594 ms
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc all the dispatchers started
2025-07-24 14:19:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 all the dispatchers started
2025-07-24 14:21:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 caught stopping signal...
2025-07-24 14:21:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc caught stopping signal...
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 caught stopped signal...
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc caught stopped signal...
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc All dispatchers stopped
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc successfully reported itself as stopped in 3.2558 ms
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:369499cc has been stopped in total 523.0735 ms
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 All dispatchers stopped
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 successfully reported itself as stopped in 1.4355 ms
2025-07-24 14:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:4cae44b2 has been stopped in total 621.8572 ms
2025-07-24 14:22:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:22:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:22:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:22:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 successfully announced in 88.3357 ms
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab successfully announced in 88.3364 ms
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 all the dispatchers started
2025-07-24 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab all the dispatchers started
2025-07-24 14:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab caught stopping signal...
2025-07-24 14:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 caught stopping signal...
2025-07-24 14:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab All dispatchers stopped
2025-07-24 14:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab successfully reported itself as stopped in 2.05 ms
2025-07-24 14:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:df57bbab has been stopped in total 89.0227 ms
2025-07-24 14:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 caught stopped signal...
2025-07-24 14:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 All dispatchers stopped
2025-07-24 14:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 successfully reported itself as stopped in 1.0405 ms
2025-07-24 14:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17396:98a69be9 has been stopped in total 619.0172 ms
2025-07-24 14:26:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:26:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:26:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 successfully announced in 63.6226 ms
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c successfully announced in 63.9553 ms
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c all the dispatchers started
2025-07-24 14:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 all the dispatchers started
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c caught stopping signal...
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 caught stopping signal...
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c All dispatchers stopped
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c successfully reported itself as stopped in 2.2288 ms
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:53b78e7c has been stopped in total 502.7196 ms
2025-07-24 14:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 caught stopped signal...
2025-07-24 14:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 All dispatchers stopped
2025-07-24 14:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 successfully reported itself as stopped in 22.2288 ms
2025-07-24 14:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30572:dbcf3a38 has been stopped in total 941.0555 ms
2025-07-24 14:30:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:30:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:30:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:30:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 successfully announced in 61.2784 ms
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab successfully announced in 61.4847 ms
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 all the dispatchers started
2025-07-24 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab all the dispatchers started
2025-07-24 14:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab caught stopping signal...
2025-07-24 14:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 caught stopping signal...
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab caught stopped signal...
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 caught stopped signal...
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 All dispatchers stopped
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 successfully reported itself as stopped in 2.3002 ms
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:47540061 has been stopped in total 613.8561 ms
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab All dispatchers stopped
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab successfully reported itself as stopped in 1.5883 ms
2025-07-24 14:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34164:93098dab has been stopped in total 696.4576 ms
2025-07-24 14:32:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:32:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:32:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 successfully announced in 60.9446 ms
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 successfully announced in 63.6311 ms
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 all the dispatchers started
2025-07-24 14:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 all the dispatchers started
2025-07-24 14:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 caught stopping signal...
2025-07-24 14:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 caught stopping signal...
2025-07-24 14:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 All dispatchers stopped
2025-07-24 14:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 successfully reported itself as stopped in 3.3071 ms
2025-07-24 14:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:48a65ee9 has been stopped in total 302.7684 ms
2025-07-24 14:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 All dispatchers stopped
2025-07-24 14:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 successfully reported itself as stopped in 1.7935 ms
2025-07-24 14:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25668:2d8bfc46 has been stopped in total 339.1473 ms
2025-07-24 14:35:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:35:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:35:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:35:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 successfully announced in 60.654 ms
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 successfully announced in 69.0084 ms
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 all the dispatchers started
2025-07-24 14:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 all the dispatchers started
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 caught stopping signal...
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 caught stopping signal...
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 All dispatchers stopped
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 successfully reported itself as stopped in 1.8067 ms
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:1e663bc5 has been stopped in total 134.2648 ms
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 caught stopped signal...
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 All dispatchers stopped
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 successfully reported itself as stopped in 1.4839 ms
2025-07-24 14:48:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31908:d8948339 has been stopped in total 547.6349 ms
2025-07-24 14:48:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:48:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:48:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 successfully announced in 62.7671 ms
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf successfully announced in 63.1763 ms
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 all the dispatchers started
2025-07-24 14:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf all the dispatchers started
2025-07-24 14:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf caught stopping signal...
2025-07-24 14:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 caught stopping signal...
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 caught stopped signal...
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf caught stopped signal...
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 All dispatchers stopped
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 successfully reported itself as stopped in 1.6704 ms
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:b183caa4 has been stopped in total 694.5567 ms
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf All dispatchers stopped
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf successfully reported itself as stopped in 0.6445 ms
2025-07-24 14:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:359a36bf has been stopped in total 861.4771 ms
2025-07-24 14:56:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:56:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:56:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:56:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e successfully announced in 62.6645 ms
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e successfully announced in 63.4783 ms
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e all the dispatchers started
2025-07-24 14:56:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e all the dispatchers started
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e caught stopping signal...
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e caught stopping signal...
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e All dispatchers stopped
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e All dispatchers stopped
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e successfully reported itself as stopped in 1.2043 ms
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e successfully reported itself as stopped in 2.0939 ms
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:f6559d4e has been stopped in total 197.4724 ms
2025-07-24 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26188:5d1a8d6e has been stopped in total 197.6805 ms
2025-07-24 14:57:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 14:57:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 14:57:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 14:57:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a successfully announced in 53.2828 ms
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad successfully announced in 65.3542 ms
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad all the dispatchers started
2025-07-24 14:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a all the dispatchers started
2025-07-24 15:05:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad caught stopping signal...
2025-07-24 15:05:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a caught stopping signal...
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a caught stopped signal...
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad caught stopped signal...
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad All dispatchers stopped
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad successfully reported itself as stopped in 1.7352 ms
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:c3c183ad has been stopped in total 734.5652 ms
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a All dispatchers stopped
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a successfully reported itself as stopped in 1.9122 ms
2025-07-24 15:05:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12904:0103132a has been stopped in total 895.8972 ms
2025-07-24 15:05:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 15:05:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 15:05:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:05:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b successfully announced in 62.2134 ms
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee successfully announced in 62.212 ms
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b all the dispatchers started
2025-07-24 15:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee all the dispatchers started
2025-07-24 15:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee caught stopping signal...
2025-07-24 15:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b caught stopping signal...
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b caught stopped signal...
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee caught stopped signal...
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee All dispatchers stopped
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee successfully reported itself as stopped in 5.3479 ms
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:086420ee has been stopped in total 731.5105 ms
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b All dispatchers stopped
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b successfully reported itself as stopped in 0.9167 ms
2025-07-24 15:07:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:cf6fe35b has been stopped in total 745.377 ms
2025-07-24 15:08:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 15:08:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 15:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 15:08:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 15:08:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:08:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 successfully announced in 62.6997 ms
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 successfully announced in 65.1721 ms
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 all the dispatchers started
2025-07-24 15:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 all the dispatchers started
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 caught stopping signal...
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 caught stopping signal...
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 All dispatchers stopped
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 successfully reported itself as stopped in 1.5355 ms
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:c355dee8 has been stopped in total 229.3588 ms
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 All dispatchers stopped
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 successfully reported itself as stopped in 1.068 ms
2025-07-24 15:09:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:d872cc23 has been stopped in total 491.8224 ms
2025-07-24 15:09:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 15:09:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 15:09:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:09:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea successfully announced in 62.7081 ms
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 successfully announced in 63.0107 ms
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea all the dispatchers started
2025-07-24 15:09:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 all the dispatchers started
2025-07-24 15:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 caught stopping signal...
2025-07-24 15:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea caught stopping signal...
2025-07-24 15:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea caught stopped signal...
2025-07-24 15:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 caught stopped signal...
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 All dispatchers stopped
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 successfully reported itself as stopped in 1.7367 ms
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:d5b44305 has been stopped in total 739.4959 ms
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea All dispatchers stopped
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea successfully reported itself as stopped in 0.7533 ms
2025-07-24 15:10:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27400:b2885bea has been stopped in total 780.0616 ms
2025-07-24 15:10:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 15:10:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 15:10:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:10:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 successfully announced in 61.6177 ms
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 successfully announced in 61.5466 ms
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 all the dispatchers started
2025-07-24 15:10:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 all the dispatchers started
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 caught stopping signal...
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 caught stopping signal...
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 All dispatchers stopped
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 successfully reported itself as stopped in 2.106 ms
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:0f1b8580 has been stopped in total 210.089 ms
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 All dispatchers stopped
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 successfully reported itself as stopped in 0.8866 ms
2025-07-24 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19440:94b52779 has been stopped in total 366.863 ms
2025-07-24 15:55:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 15:55:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 15:55:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 successfully announced in 63.9579 ms
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e successfully announced in 63.9938 ms
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 all the dispatchers started
2025-07-24 15:55:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e all the dispatchers started
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 caught stopping signal...
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e caught stopping signal...
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 All dispatchers stopped
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 successfully reported itself as stopped in 1.991 ms
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:346dfc12 has been stopped in total 243.6532 ms
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e caught stopped signal...
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e All dispatchers stopped
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e successfully reported itself as stopped in 1.4919 ms
2025-07-24 16:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14208:eb2eed2e has been stopped in total 947.975 ms
2025-07-24 16:14:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:14:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:14:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:14:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:14:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:14:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:14:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 successfully announced in 64.6277 ms
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 successfully announced in 65.5624 ms
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 all the dispatchers started
2025-07-24 16:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 all the dispatchers started
2025-07-24 16:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 caught stopping signal...
2025-07-24 16:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 caught stopping signal...
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 All dispatchers stopped
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 successfully reported itself as stopped in 1.9345 ms
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:3a88a4f3 has been stopped in total 328.7811 ms
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 All dispatchers stopped
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 successfully reported itself as stopped in 19.4885 ms
2025-07-24 16:23:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10472:e30060b5 has been stopped in total 595.2363 ms
2025-07-24 16:23:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:23:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:23:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:23:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 successfully announced in 65.5869 ms
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 successfully announced in 65.5862 ms
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 all the dispatchers started
2025-07-24 16:23:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 all the dispatchers started
2025-07-24 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 caught stopping signal...
2025-07-24 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 caught stopping signal...
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 caught stopped signal...
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 caught stopped signal...
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 All dispatchers stopped
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 All dispatchers stopped
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 successfully reported itself as stopped in 1.3509 ms
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:156a36a5 has been stopped in total 743.4017 ms
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 successfully reported itself as stopped in 2.7326 ms
2025-07-24 16:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:74461ed3 has been stopped in total 743.5358 ms
2025-07-24 16:28:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:28:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:28:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:28:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:28:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:28:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e successfully announced in 64.1737 ms
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c successfully announced in 64.0015 ms
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c all the dispatchers started
2025-07-24 16:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e all the dispatchers started
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e caught stopping signal...
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c caught stopping signal...
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e All dispatchers stopped
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e successfully reported itself as stopped in 1.9078 ms
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:eed15d2e has been stopped in total 151.0467 ms
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c All dispatchers stopped
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c successfully reported itself as stopped in 1.6238 ms
2025-07-24 16:29:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31124:29066a8c has been stopped in total 207.8049 ms
2025-07-24 16:29:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:29:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:29:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:29:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad successfully announced in 66.6967 ms
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef successfully announced in 66.6846 ms
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef all the dispatchers started
2025-07-24 16:29:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad all the dispatchers started
2025-07-24 16:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef caught stopping signal...
2025-07-24 16:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad caught stopping signal...
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad caught stopped signal...
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef caught stopped signal...
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef All dispatchers stopped
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef successfully reported itself as stopped in 1.8994 ms
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:5b020bef has been stopped in total 653.1424 ms
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad All dispatchers stopped
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad successfully reported itself as stopped in 1.6433 ms
2025-07-24 16:33:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31808:15e432ad has been stopped in total 750.6634 ms
2025-07-24 16:33:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:33:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:33:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:33:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:33:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:33:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:33:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:33:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 successfully announced in 63.6237 ms
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 successfully announced in 64.199 ms
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 all the dispatchers started
2025-07-24 16:33:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 all the dispatchers started
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 caught stopping signal...
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 caught stopping signal...
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 All dispatchers stopped
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 All dispatchers stopped
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 successfully reported itself as stopped in 2.0012 ms
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:05fc77c0 has been stopped in total 388.5895 ms
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 successfully reported itself as stopped in 0.8874 ms
2025-07-24 16:33:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2036:a2c3e816 has been stopped in total 391.7314 ms
2025-07-24 16:34:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:34:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:34:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:34:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c successfully announced in 62.8763 ms
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 successfully announced in 62.8774 ms
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c all the dispatchers started
2025-07-24 16:34:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 all the dispatchers started
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c caught stopping signal...
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 caught stopping signal...
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c All dispatchers stopped
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 All dispatchers stopped
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 successfully reported itself as stopped in 2.5512 ms
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:0c8e9291 has been stopped in total 197.6424 ms
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c successfully reported itself as stopped in 2.922 ms
2025-07-24 16:44:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7256:df62a12c has been stopped in total 198.1857 ms
2025-07-24 16:44:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:44:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:44:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:44:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:44:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:44:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 successfully announced in 64.7229 ms
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 successfully announced in 65.2956 ms
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 all the dispatchers started
2025-07-24 16:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 all the dispatchers started
2025-07-24 16:50:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 caught stopping signal...
2025-07-24 16:50:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 caught stopping signal...
2025-07-24 16:50:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 caught stopped signal...
2025-07-24 16:50:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 caught stopped signal...
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 All dispatchers stopped
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 All dispatchers stopped
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 successfully reported itself as stopped in 0.6701 ms
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:005dd184 has been stopped in total 765.0157 ms
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 successfully reported itself as stopped in 2.3522 ms
2025-07-24 16:50:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5844:d25f4c26 has been stopped in total 765.3726 ms
2025-07-24 16:50:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:50:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:50:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:50:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b successfully announced in 62.9547 ms
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f successfully announced in 62.7737 ms
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b all the dispatchers started
2025-07-24 16:50:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f all the dispatchers started
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f caught stopping signal...
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b caught stopping signal...
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f All dispatchers stopped
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f successfully reported itself as stopped in 1.5476 ms
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:166c338f has been stopped in total 173.358 ms
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b All dispatchers stopped
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b successfully reported itself as stopped in 0.9251 ms
2025-07-24 16:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31616:5ac3328b has been stopped in total 365.9943 ms
2025-07-24 16:54:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-24 16:54:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-24 16:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-24 16:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 successfully announced in 63.1738 ms
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a successfully announced in 62.9503 ms
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 all the dispatchers started
2025-07-24 16:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a all the dispatchers started
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a caught stopping signal...
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 caught stopping signal...
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a All dispatchers stopped
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a successfully reported itself as stopped in 2.2497 ms
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:fc0b798a has been stopped in total 173.2323 ms
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 All dispatchers stopped
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 successfully reported itself as stopped in 1.4161 ms
2025-07-24 16:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:2ccfdf44 has been stopped in total 192.5992 ms
