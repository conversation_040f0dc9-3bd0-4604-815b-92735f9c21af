2025-07-17 08:44:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 08:44:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 08:44:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 successfully announced in 148.5541 ms
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 successfully announced in 149.0521 ms
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 all the dispatchers started
2025-07-17 08:44:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 all the dispatchers started
2025-07-17 08:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 caught stopping signal...
2025-07-17 08:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 caught stopping signal...
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 All dispatchers stopped
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 successfully reported itself as stopped in 2.3915 ms
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:5fff71f2 has been stopped in total 323.4613 ms
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 caught stopped signal...
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 All dispatchers stopped
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 successfully reported itself as stopped in 1.2377 ms
2025-07-17 08:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21720:400abae4 has been stopped in total 747.0668 ms
2025-07-17 08:48:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 08:48:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 08:48:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 successfully announced in 115.5831 ms
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f successfully announced in 123.663 ms
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f all the dispatchers started
2025-07-17 08:48:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 all the dispatchers started
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 caught stopping signal...
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f caught stopping signal...
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f All dispatchers stopped
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 All dispatchers stopped
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f successfully reported itself as stopped in 1.94 ms
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:e9099f6f has been stopped in total 127.035 ms
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 successfully reported itself as stopped in 0.8485 ms
2025-07-17 09:00:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24088:24d3d0f1 has been stopped in total 127.6766 ms
2025-07-17 09:00:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 09:00:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 09:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 successfully announced in 120.815 ms
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 successfully announced in 120.812 ms
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 all the dispatchers started
2025-07-17 09:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 all the dispatchers started
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 caught stopping signal...
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 caught stopping signal...
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 caught stopped signal...
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 caught stopped signal...
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 All dispatchers stopped
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 successfully reported itself as stopped in 23.4669 ms
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:ce1fe6b6 has been stopped in total 585.0668 ms
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 All dispatchers stopped
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 successfully reported itself as stopped in 24.1674 ms
2025-07-17 10:02:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16328:0cace120 has been stopped in total 750.2402 ms
2025-07-17 10:03:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:03:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:03:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 successfully announced in 115.6996 ms
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 successfully announced in 115.6982 ms
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 all the dispatchers started
2025-07-17 10:03:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 all the dispatchers started
2025-07-17 10:03:35 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged=\"OnPoNumSelected\" *@' is not a valid attribute name."
System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnPoNumSelected" *@' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
2025-07-17 10:03:35 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"DF-r9yvRQhmi1Yn4IA8sgwO1cldkP0gkB0zrFbzTbq0"'.
System.AggregateException: One or more errors occurred. (InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnPoNumSelected" *@' is not a valid attribute name.)
 ---> System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnPoNumSelected" *@' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-07-17 10:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 caught stopping signal...
2025-07-17 10:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 caught stopping signal...
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 caught stopped signal...
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 caught stopped signal...
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 All dispatchers stopped
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 successfully reported itself as stopped in 1.5744 ms
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:3b7a7336 has been stopped in total 742.7777 ms
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 All dispatchers stopped
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 successfully reported itself as stopped in 1.633 ms
2025-07-17 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34720:237e1ee9 has been stopped in total 746.3641 ms
2025-07-17 10:04:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:04:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:04:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 successfully announced in 115.4006 ms
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 successfully announced in 115.4197 ms
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 all the dispatchers started
2025-07-17 10:04:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 all the dispatchers started
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 caught stopping signal...
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 caught stopping signal...
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 All dispatchers stopped
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 successfully reported itself as stopped in 2.6069 ms
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:27cf4765 has been stopped in total 360.2678 ms
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 All dispatchers stopped
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 successfully reported itself as stopped in 1.5432 ms
2025-07-17 10:06:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28112:180942d5 has been stopped in total 381.2864 ms
2025-07-17 10:06:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:06:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:06:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c successfully announced in 123.4476 ms
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 successfully announced in 123.4307 ms
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c all the dispatchers started
2025-07-17 10:06:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 all the dispatchers started
2025-07-17 10:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 caught stopping signal...
2025-07-17 10:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c caught stopping signal...
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c All dispatchers stopped
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 All dispatchers stopped
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c successfully reported itself as stopped in 2.1218 ms
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 successfully reported itself as stopped in 0.6705 ms
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:e86ffc8c has been stopped in total 192.711 ms
2025-07-17 10:06:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:f5edce09 has been stopped in total 192.9389 ms
2025-07-17 10:07:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:07:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:07:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:050c922e successfully announced in 266.9333 ms
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:34fa3061 successfully announced in 266.8941 ms
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:34fa3061 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:050c922e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:34fa3061 all the dispatchers started
2025-07-17 10:07:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28752:050c922e all the dispatchers started
2025-07-17 10:09:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:09:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:09:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 successfully announced in 116.0451 ms
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 successfully announced in 116.0444 ms
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 all the dispatchers started
2025-07-17 10:09:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 all the dispatchers started
2025-07-17 10:14:35 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 caught stopping signal...
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 caught stopping signal...
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 All dispatchers stopped
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 successfully reported itself as stopped in 1.9551 ms
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:4ed82ad0 has been stopped in total 108.6581 ms
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 All dispatchers stopped
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 successfully reported itself as stopped in 0.9387 ms
2025-07-17 10:22:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28184:e0256f31 has been stopped in total 387.1115 ms
2025-07-17 10:22:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:22:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:22:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:53e06d8b successfully announced in 287.5544 ms
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:98be145a successfully announced in 287.521 ms
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:53e06d8b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:98be145a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:53e06d8b all the dispatchers started
2025-07-17 10:22:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:508:98be145a all the dispatchers started
2025-07-17 10:24:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:24:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:24:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:42eb1ec9 successfully announced in 338.1383 ms
2025-07-17 10:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:46c6a9bc successfully announced in 338.7311 ms
2025-07-17 10:24:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:42eb1ec9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:24:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:46c6a9bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:24:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:46c6a9bc all the dispatchers started
2025-07-17 10:24:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4572:42eb1ec9 all the dispatchers started
2025-07-17 10:26:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:26:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:e2d18890 successfully announced in 242.4877 ms
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:53ff0d2f successfully announced in 242.5148 ms
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:e2d18890 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:53ff0d2f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:53ff0d2f all the dispatchers started
2025-07-17 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3296:e2d18890 all the dispatchers started
2025-07-17 10:39:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:39:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:39:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 successfully announced in 129.9783 ms
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 successfully announced in 129.7606 ms
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 all the dispatchers started
2025-07-17 10:39:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 all the dispatchers started
2025-07-17 10:39:43 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 10:39:43 [Information] Hangfire.Server.ServerWatchdog: 4 servers were removed due to timeout
2025-07-17 10:57:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 caught stopping signal...
2025-07-17 10:57:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 caught stopping signal...
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 caught stopped signal...
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 caught stopped signal...
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 All dispatchers stopped
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 All dispatchers stopped
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 successfully reported itself as stopped in 3.6028 ms
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:317538c2 has been stopped in total 887.7884 ms
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 successfully reported itself as stopped in 3.6233 ms
2025-07-17 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25892:75ebb052 has been stopped in total 888.5344 ms
2025-07-17 10:58:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:58:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 successfully announced in 113.4093 ms
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 successfully announced in 114.2439 ms
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 all the dispatchers started
2025-07-17 10:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 all the dispatchers started
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 caught stopping signal...
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 caught stopping signal...
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 All dispatchers stopped
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 successfully reported itself as stopped in 1.4176 ms
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:13544f45 has been stopped in total 297.9873 ms
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 All dispatchers stopped
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 successfully reported itself as stopped in 0.6903 ms
2025-07-17 10:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32612:98b01660 has been stopped in total 305.3706 ms
2025-07-17 10:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 10:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 10:59:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:39a842e5 successfully announced in 238.9043 ms
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:edd3e217 successfully announced in 238.912 ms
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:edd3e217 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:39a842e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:edd3e217 all the dispatchers started
2025-07-17 10:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:39a842e5 all the dispatchers started
2025-07-17 11:00:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:00:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:6bd50c12 successfully announced in 340.769 ms
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:00d72027 successfully announced in 343.3481 ms
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:00d72027 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:6bd50c12 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:6bd50c12 all the dispatchers started
2025-07-17 11:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33744:00d72027 all the dispatchers started
2025-07-17 11:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:01:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:40cf1397 successfully announced in 247.5631 ms
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:d42590c8 successfully announced in 247.6049 ms
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:40cf1397 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:d42590c8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:40cf1397 all the dispatchers started
2025-07-17 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21672:d42590c8 all the dispatchers started
2025-07-17 11:06:51 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 11:06:51 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 11:11:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:11:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:11:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 successfully announced in 115.5186 ms
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 successfully announced in 115.5179 ms
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 all the dispatchers started
2025-07-17 11:11:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 all the dispatchers started
2025-07-17 11:16:51 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 caught stopping signal...
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 caught stopping signal...
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 All dispatchers stopped
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 successfully reported itself as stopped in 1.8353 ms
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:c7e8a7f4 has been stopped in total 175.3582 ms
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 All dispatchers stopped
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 successfully reported itself as stopped in 1.0427 ms
2025-07-17 11:19:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33984:9dc10803 has been stopped in total 193.9227 ms
2025-07-17 11:19:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:19:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:19:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 successfully announced in 110.7345 ms
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 successfully announced in 111.5285 ms
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 all the dispatchers started
2025-07-17 11:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 all the dispatchers started
2025-07-17 11:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 caught stopping signal...
2025-07-17 11:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 caught stopping signal...
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 caught stopped signal...
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 caught stopped signal...
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 All dispatchers stopped
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 All dispatchers stopped
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 successfully reported itself as stopped in 1.6216 ms
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:addbf763 has been stopped in total 632.8573 ms
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 successfully reported itself as stopped in 1.4469 ms
2025-07-17 11:20:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5852:294b90a7 has been stopped in total 633.9162 ms
2025-07-17 11:20:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:20:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:20:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 successfully announced in 112.8311 ms
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 successfully announced in 125.7555 ms
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 all the dispatchers started
2025-07-17 11:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 all the dispatchers started
2025-07-17 11:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 caught stopping signal...
2025-07-17 11:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 caught stopping signal...
2025-07-17 11:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 caught stopped signal...
2025-07-17 11:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 caught stopped signal...
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 All dispatchers stopped
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 All dispatchers stopped
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 successfully reported itself as stopped in 1.7744 ms
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 successfully reported itself as stopped in 1.8122 ms
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:e792b157 has been stopped in total 544.4095 ms
2025-07-17 11:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:afbbdb48 has been stopped in total 544.1467 ms
2025-07-17 11:22:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:22:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 successfully announced in 111.4065 ms
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 successfully announced in 111.6351 ms
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 all the dispatchers started
2025-07-17 11:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 all the dispatchers started
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 caught stopping signal...
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 caught stopping signal...
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 All dispatchers stopped
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 successfully reported itself as stopped in 3.1854 ms
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:aecb17a9 has been stopped in total 472.9923 ms
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 caught stopped signal...
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 All dispatchers stopped
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 successfully reported itself as stopped in 0.9079 ms
2025-07-17 11:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11988:f6fe5121 has been stopped in total 522.7588 ms
2025-07-17 11:22:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:22:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:22:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:22:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:22:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:22:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:22:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:22:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af successfully announced in 111.6655 ms
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 successfully announced in 111.3325 ms
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 all the dispatchers started
2025-07-17 11:22:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af all the dispatchers started
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 caught stopping signal...
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af caught stopping signal...
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 All dispatchers stopped
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 successfully reported itself as stopped in 2.2922 ms
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:e49d88b5 has been stopped in total 227.5404 ms
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af All dispatchers stopped
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af successfully reported itself as stopped in 1.2538 ms
2025-07-17 11:23:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20220:316108af has been stopped in total 456.9579 ms
2025-07-17 11:24:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:24:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:24:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 successfully announced in 149.218 ms
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f successfully announced in 153.8391 ms
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 all the dispatchers started
2025-07-17 11:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f all the dispatchers started
2025-07-17 11:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f caught stopping signal...
2025-07-17 11:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 caught stopping signal...
2025-07-17 11:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f All dispatchers stopped
2025-07-17 11:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f successfully reported itself as stopped in 1.729 ms
2025-07-17 11:28:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:7829b63f has been stopped in total 222.6042 ms
2025-07-17 11:28:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 All dispatchers stopped
2025-07-17 11:28:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 successfully reported itself as stopped in 0.8214 ms
2025-07-17 11:28:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25024:c6f715e5 has been stopped in total 312.9896 ms
2025-07-17 11:29:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:29:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:29:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e successfully announced in 136.099 ms
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e successfully announced in 136.2372 ms
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e all the dispatchers started
2025-07-17 11:29:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e all the dispatchers started
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e caught stopping signal...
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e caught stopping signal...
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e All dispatchers stopped
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e successfully reported itself as stopped in 1.7788 ms
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:0943f77e has been stopped in total 106.5751 ms
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e All dispatchers stopped
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e successfully reported itself as stopped in 0.8357 ms
2025-07-17 11:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29980:ba66198e has been stopped in total 192.2537 ms
2025-07-17 11:31:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:31:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:31:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 successfully announced in 109.1564 ms
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 successfully announced in 109.3953 ms
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 all the dispatchers started
2025-07-17 11:31:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 all the dispatchers started
2025-07-17 11:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 caught stopping signal...
2025-07-17 11:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 caught stopping signal...
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 caught stopped signal...
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 caught stopped signal...
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 All dispatchers stopped
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 All dispatchers stopped
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 successfully reported itself as stopped in 1.9206 ms
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:58b3aed9 has been stopped in total 927.6694 ms
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 successfully reported itself as stopped in 1.245 ms
2025-07-17 11:33:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27612:b01e3a19 has been stopped in total 929.1888 ms
2025-07-17 11:33:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:33:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:33:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 successfully announced in 127.674 ms
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c successfully announced in 127.6744 ms
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c all the dispatchers started
2025-07-17 11:33:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 all the dispatchers started
2025-07-17 11:38:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 caught stopping signal...
2025-07-17 11:38:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c caught stopping signal...
2025-07-17 11:38:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c All dispatchers stopped
2025-07-17 11:38:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c successfully reported itself as stopped in 1.9085 ms
2025-07-17 11:38:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:4427074c has been stopped in total 114.2432 ms
2025-07-17 11:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 caught stopped signal...
2025-07-17 11:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 All dispatchers stopped
2025-07-17 11:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 successfully reported itself as stopped in 1.1805 ms
2025-07-17 11:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26404:3589e480 has been stopped in total 918.2748 ms
2025-07-17 11:40:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 11:40:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 11:40:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 successfully announced in 114.9357 ms
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f successfully announced in 114.9232 ms
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 all the dispatchers started
2025-07-17 11:40:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f all the dispatchers started
2025-07-17 11:42:18 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnSaveAsync(PurchaseOrderReceivingDto arg1, ItemChangedType arg2) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 216
   at BootstrapBlazor.Components.Table`1.InternalOnSaveAsync(TItem item, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveModelAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.ClickUpdateButtonCallback()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 13:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 caught stopping signal...
2025-07-17 13:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f caught stopping signal...
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 All dispatchers stopped
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 successfully reported itself as stopped in 3.4918 ms
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:233f3f32 has been stopped in total 276.8869 ms
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f caught stopped signal...
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f All dispatchers stopped
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f successfully reported itself as stopped in 0.8456 ms
2025-07-17 13:19:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28148:b0c1cd0f has been stopped in total 803.6828 ms
2025-07-17 13:19:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:19:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:19:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 successfully announced in 116.4174 ms
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 successfully announced in 116.4119 ms
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 all the dispatchers started
2025-07-17 13:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 all the dispatchers started
2025-07-17 13:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 caught stopping signal...
2025-07-17 13:22:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 caught stopping signal...
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 caught stopped signal...
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 caught stopped signal...
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 All dispatchers stopped
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 successfully reported itself as stopped in 2.671 ms
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:410956d1 has been stopped in total 782.7184 ms
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 All dispatchers stopped
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 successfully reported itself as stopped in 1.3685 ms
2025-07-17 13:22:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14904:5d2ce114 has been stopped in total 787.0954 ms
2025-07-17 13:29:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:29:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:29:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 successfully announced in 111.4655 ms
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 successfully announced in 111.4904 ms
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 all the dispatchers started
2025-07-17 13:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 all the dispatchers started
2025-07-17 13:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 caught stopping signal...
2025-07-17 13:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 caught stopping signal...
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 caught stopped signal...
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 caught stopped signal...
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 All dispatchers stopped
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 All dispatchers stopped
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 successfully reported itself as stopped in 1.5938 ms
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:eb05cc94 has been stopped in total 536.3669 ms
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 successfully reported itself as stopped in 0.8819 ms
2025-07-17 13:30:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23536:c30d00e4 has been stopped in total 537.1199 ms
2025-07-17 13:30:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:30:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:30:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe successfully announced in 112.1943 ms
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 successfully announced in 113.107 ms
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 all the dispatchers started
2025-07-17 13:30:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe all the dispatchers started
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe caught stopping signal...
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 caught stopping signal...
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 All dispatchers stopped
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 successfully reported itself as stopped in 2.2013 ms
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:6bd2eba0 has been stopped in total 457.3221 ms
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe All dispatchers stopped
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe successfully reported itself as stopped in 1.1622 ms
2025-07-17 13:37:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31508:604d66fe has been stopped in total 475.6696 ms
2025-07-17 13:38:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:38:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:38:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a successfully announced in 117.2063 ms
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db successfully announced in 111.7593 ms
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db all the dispatchers started
2025-07-17 13:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a all the dispatchers started
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a caught stopping signal...
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db caught stopping signal...
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db All dispatchers stopped
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db successfully reported itself as stopped in 1.8232 ms
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:781781db has been stopped in total 281.3776 ms
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a All dispatchers stopped
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a successfully reported itself as stopped in 0.8724 ms
2025-07-17 13:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19928:f242584a has been stopped in total 286.4738 ms
2025-07-17 13:39:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:39:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:39:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca successfully announced in 112.7651 ms
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 successfully announced in 112.7648 ms
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca all the dispatchers started
2025-07-17 13:39:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 all the dispatchers started
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca caught stopping signal...
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 caught stopping signal...
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 All dispatchers stopped
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 successfully reported itself as stopped in 1.7865 ms
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:8d774e92 has been stopped in total 63.7498 ms
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca All dispatchers stopped
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca successfully reported itself as stopped in 0.8353 ms
2025-07-17 13:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31216:2e4fa5ca has been stopped in total 74.3732 ms
2025-07-17 13:41:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:41:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 successfully announced in 112.3089 ms
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 successfully announced in 113.6646 ms
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 all the dispatchers started
2025-07-17 13:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 all the dispatchers started
2025-07-17 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 caught stopping signal...
2025-07-17 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 caught stopping signal...
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 caught stopped signal...
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 caught stopped signal...
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 All dispatchers stopped
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 All dispatchers stopped
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 successfully reported itself as stopped in 1.9034 ms
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:d7296225 has been stopped in total 963.4221 ms
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 successfully reported itself as stopped in 1.171 ms
2025-07-17 13:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15620:ded17a31 has been stopped in total 963.2165 ms
2025-07-17 13:45:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:45:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:45:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d successfully announced in 151.7839 ms
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 successfully announced in 151.7609 ms
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d all the dispatchers started
2025-07-17 13:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 all the dispatchers started
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d caught stopping signal...
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 caught stopping signal...
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d All dispatchers stopped
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 All dispatchers stopped
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d successfully reported itself as stopped in 1.4798 ms
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:7ffb4e3d has been stopped in total 9.4854 ms
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 successfully reported itself as stopped in 23.0638 ms
2025-07-17 13:46:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24780:d34d09f8 has been stopped in total 31.7208 ms
2025-07-17 13:47:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:47:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:47:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 successfully announced in 109.554 ms
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 successfully announced in 109.5514 ms
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 all the dispatchers started
2025-07-17 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 all the dispatchers started
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 caught stopping signal...
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 caught stopping signal...
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 All dispatchers stopped
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 successfully reported itself as stopped in 1.9173 ms
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:ed5cbcf4 has been stopped in total 49.8903 ms
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 All dispatchers stopped
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 successfully reported itself as stopped in 1.6532 ms
2025-07-17 13:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18084:0e281fb3 has been stopped in total 98.049 ms
2025-07-17 13:50:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:50:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:50:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 successfully announced in 113.3096 ms
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 successfully announced in 113.7815 ms
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 all the dispatchers started
2025-07-17 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 all the dispatchers started
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 caught stopping signal...
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 caught stopping signal...
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 All dispatchers stopped
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 successfully reported itself as stopped in 21.8924 ms
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:6e90fca2 has been stopped in total 468.7842 ms
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 caught stopped signal...
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 All dispatchers stopped
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 successfully reported itself as stopped in 1.5843 ms
2025-07-17 13:52:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15852:b4787b12 has been stopped in total 578.9105 ms
2025-07-17 13:52:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:52:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:52:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed successfully announced in 114.6803 ms
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 successfully announced in 114.684 ms
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed all the dispatchers started
2025-07-17 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 all the dispatchers started
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed caught stopping signal...
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 caught stopping signal...
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 All dispatchers stopped
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 successfully reported itself as stopped in 2.0027 ms
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:b8bd78c0 has been stopped in total 474.0366 ms
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed All dispatchers stopped
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed successfully reported itself as stopped in 0.68 ms
2025-07-17 13:53:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18332:efe8d8ed has been stopped in total 475.8876 ms
2025-07-17 13:53:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:53:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:53:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 successfully announced in 112.3251 ms
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b successfully announced in 112.3463 ms
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b all the dispatchers started
2025-07-17 13:53:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 all the dispatchers started
2025-07-17 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b caught stopping signal...
2025-07-17 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 caught stopping signal...
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 caught stopped signal...
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b caught stopped signal...
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 All dispatchers stopped
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 successfully reported itself as stopped in 1.6894 ms
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:e188fd99 has been stopped in total 706.9664 ms
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b All dispatchers stopped
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b successfully reported itself as stopped in 0.8683 ms
2025-07-17 13:53:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16420:9298617b has been stopped in total 710.9755 ms
2025-07-17 13:54:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:54:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f successfully announced in 135.0951 ms
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 successfully announced in 135.1072 ms
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 all the dispatchers started
2025-07-17 13:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f all the dispatchers started
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f caught stopping signal...
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 caught stopping signal...
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 caught stopped signal...
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f caught stopped signal...
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f All dispatchers stopped
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f successfully reported itself as stopped in 1.9979 ms
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:8240125f has been stopped in total 564.9027 ms
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 All dispatchers stopped
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 successfully reported itself as stopped in 0.6837 ms
2025-07-17 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25904:6d2395e4 has been stopped in total 670.4156 ms
2025-07-17 13:59:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:59:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 successfully announced in 115.2292 ms
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e successfully announced in 115.1859 ms
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 all the dispatchers started
2025-07-17 13:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e all the dispatchers started
2025-07-17 13:59:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 caught stopping signal...
2025-07-17 13:59:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e caught stopping signal...
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e caught stopped signal...
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 caught stopped signal...
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 All dispatchers stopped
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 successfully reported itself as stopped in 3.1788 ms
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:92c30829 has been stopped in total 521.191 ms
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e All dispatchers stopped
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e successfully reported itself as stopped in 1.0893 ms
2025-07-17 13:59:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29308:e081887e has been stopped in total 620.8287 ms
2025-07-17 13:59:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 13:59:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 13:59:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 successfully announced in 124.8762 ms
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 successfully announced in 125.0018 ms
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 all the dispatchers started
2025-07-17 13:59:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 all the dispatchers started
2025-07-17 14:01:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 caught stopping signal...
2025-07-17 14:01:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 caught stopping signal...
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 All dispatchers stopped
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 successfully reported itself as stopped in 1.7722 ms
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:e8bcf215 has been stopped in total 327.853 ms
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 All dispatchers stopped
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 successfully reported itself as stopped in 0.8423 ms
2025-07-17 14:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30536:eb022c24 has been stopped in total 462.4825 ms
2025-07-17 14:02:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:02:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:02:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 successfully announced in 117.063 ms
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b successfully announced in 117.0626 ms
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 all the dispatchers started
2025-07-17 14:02:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b all the dispatchers started
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 caught stopping signal...
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b caught stopping signal...
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b All dispatchers stopped
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b successfully reported itself as stopped in 1.6022 ms
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:0ac98d1b has been stopped in total 188.8645 ms
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 All dispatchers stopped
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 successfully reported itself as stopped in 0.8643 ms
2025-07-17 14:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:1a5c6951 has been stopped in total 252.3402 ms
2025-07-17 14:04:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:04:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:04:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 successfully announced in 117.8262 ms
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 successfully announced in 117.8328 ms
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 all the dispatchers started
2025-07-17 14:04:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 all the dispatchers started
2025-07-17 14:07:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 caught stopping signal...
2025-07-17 14:07:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 caught stopping signal...
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 caught stopped signal...
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 caught stopped signal...
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 All dispatchers stopped
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 successfully reported itself as stopped in 1.6795 ms
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:7d906865 has been stopped in total 641.3714 ms
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 All dispatchers stopped
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 successfully reported itself as stopped in 0.7602 ms
2025-07-17 14:07:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7160:59cf6709 has been stopped in total 649.5938 ms
2025-07-17 14:07:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:07:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:07:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 successfully announced in 111.2339 ms
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec successfully announced in 111.2156 ms
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 all the dispatchers started
2025-07-17 14:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec all the dispatchers started
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 caught stopping signal...
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec caught stopping signal...
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec caught stopped signal...
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 caught stopped signal...
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 All dispatchers stopped
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 successfully reported itself as stopped in 2.294 ms
2025-07-17 14:12:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:69c68587 has been stopped in total 628.1699 ms
2025-07-17 14:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec All dispatchers stopped
2025-07-17 14:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec successfully reported itself as stopped in 1.0695 ms
2025-07-17 14:12:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7580:1d8261ec has been stopped in total 729.6503 ms
2025-07-17 14:12:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:12:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:12:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec successfully announced in 127.6308 ms
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 successfully announced in 128.7988 ms
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec all the dispatchers started
2025-07-17 14:12:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 all the dispatchers started
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec caught stopping signal...
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 caught stopping signal...
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 caught stopped signal...
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec caught stopped signal...
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec All dispatchers stopped
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 All dispatchers stopped
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 successfully reported itself as stopped in 1.7283 ms
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:f099e036 has been stopped in total 693.5473 ms
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec successfully reported itself as stopped in 2.2625 ms
2025-07-17 14:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30348:1d7986ec has been stopped in total 694.2808 ms
2025-07-17 14:13:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:13:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:13:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 successfully announced in 115.5578 ms
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 successfully announced in 115.4875 ms
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 all the dispatchers started
2025-07-17 14:13:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 all the dispatchers started
2025-07-17 14:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 caught stopping signal...
2025-07-17 14:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 caught stopping signal...
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 All dispatchers stopped
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 All dispatchers stopped
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 successfully reported itself as stopped in 3.6969 ms
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:9781f5a3 has been stopped in total 420.1129 ms
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 successfully reported itself as stopped in 0.8954 ms
2025-07-17 14:13:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22676:4d7a95f7 has been stopped in total 419.985 ms
2025-07-17 14:13:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:13:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:13:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 successfully announced in 173.6614 ms
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a successfully announced in 174.0692 ms
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a all the dispatchers started
2025-07-17 14:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 all the dispatchers started
2025-07-17 14:14:08 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnSaveAsync(PurchaseOrderReceivingDto arg1, ItemChangedType arg2) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 246
   at BootstrapBlazor.Components.Table`1.InternalOnSaveAsync(TItem item, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveModelAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.ClickUpdateButtonCallback()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 14:20:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a caught stopping signal...
2025-07-17 14:20:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 caught stopping signal...
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 caught stopped signal...
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a caught stopped signal...
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 All dispatchers stopped
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 successfully reported itself as stopped in 2.4662 ms
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:37437663 has been stopped in total 948.2322 ms
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a All dispatchers stopped
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a successfully reported itself as stopped in 0.6144 ms
2025-07-17 14:20:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:24a9338a has been stopped in total 970.5559 ms
2025-07-17 14:20:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:20:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:20:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb successfully announced in 126.7532 ms
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 successfully announced in 127.3644 ms
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 all the dispatchers started
2025-07-17 14:20:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb all the dispatchers started
2025-07-17 14:20:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb caught stopping signal...
2025-07-17 14:20:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 caught stopping signal...
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 caught stopped signal...
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb caught stopped signal...
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb All dispatchers stopped
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 All dispatchers stopped
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 successfully reported itself as stopped in 2.3471 ms
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:56192d67 has been stopped in total 615.6038 ms
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb successfully reported itself as stopped in 2.4655 ms
2025-07-17 14:20:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24460:4e067ccb has been stopped in total 615.9893 ms
2025-07-17 14:20:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:20:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:20:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 successfully announced in 115.8641 ms
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 successfully announced in 115.8385 ms
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 all the dispatchers started
2025-07-17 14:20:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 all the dispatchers started
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 caught stopping signal...
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 caught stopping signal...
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 All dispatchers stopped
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 All dispatchers stopped
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 successfully reported itself as stopped in 2.2247 ms
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 successfully reported itself as stopped in 1.5843 ms
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:5dffc5c2 has been stopped in total 284.3758 ms
2025-07-17 14:22:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26532:14589157 has been stopped in total 284.6089 ms
2025-07-17 14:22:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:22:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:22:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c successfully announced in 112.9494 ms
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e successfully announced in 113.7702 ms
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c all the dispatchers started
2025-07-17 14:22:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e all the dispatchers started
2025-07-17 14:23:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e caught stopping signal...
2025-07-17 14:23:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c caught stopping signal...
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c All dispatchers stopped
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c successfully reported itself as stopped in 2.586 ms
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:f4f1ce4c has been stopped in total 300.3037 ms
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e All dispatchers stopped
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e successfully reported itself as stopped in 1.2739 ms
2025-07-17 14:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ce05d42e has been stopped in total 321.2611 ms
2025-07-17 14:23:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:23:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db successfully announced in 124.2555 ms
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 successfully announced in 124.3324 ms
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db all the dispatchers started
2025-07-17 14:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 all the dispatchers started
2025-07-17 14:24:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db caught stopping signal...
2025-07-17 14:24:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 caught stopping signal...
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db All dispatchers stopped
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db successfully reported itself as stopped in 1.9496 ms
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:612ff4db has been stopped in total 499.8829 ms
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 All dispatchers stopped
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 successfully reported itself as stopped in 0.7566 ms
2025-07-17 14:24:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:d27284d5 has been stopped in total 516.1874 ms
2025-07-17 14:24:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:24:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 successfully announced in 113.4158 ms
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 successfully announced in 113.4257 ms
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 all the dispatchers started
2025-07-17 14:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 all the dispatchers started
2025-07-17 14:28:19 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnSaveAsync(PurchaseOrderReceivingDto arg1, ItemChangedType arg2) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 245
   at BootstrapBlazor.Components.Table`1.InternalOnSaveAsync(TItem item, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveModelAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.ClickUpdateButtonCallback()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 14:28:33 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnAddAsync() in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 241
   at BootstrapBlazor.Components.Table`1.InternalOnAddAsync()
   at BootstrapBlazor.Components.Table`1.AddAsync()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 14:28:36 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnAddAsync() in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 241
   at BootstrapBlazor.Components.Table`1.InternalOnAddAsync()
   at BootstrapBlazor.Components.Table`1.AddAsync()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 14:28:42 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnDeleteAsync(IEnumerable`1 arg) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 249
   at BootstrapBlazor.Components.Table`1.InternalOnDeleteAsync()
   at BootstrapBlazor.Components.Table`1.<DeleteAsync>g__DeleteItemsAsync|1549_0()
   at BootstrapBlazor.Components.Table`1.DeleteAsync()
   at BootstrapBlazor.Components.TableToolbar`1.OnConfirm(TableToolbarPopConfirmButton`1 button)
   at BootstrapBlazor.Components.PopConfirmButton.OnClickConfirm()
   at BootstrapBlazor.Components.PopConfirmButtonContent.OnConfirmClick()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 caught stopping signal...
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 caught stopping signal...
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 All dispatchers stopped
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 successfully reported itself as stopped in 4.8218 ms
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:3377f372 has been stopped in total 362.0808 ms
2025-07-17 14:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 caught stopped signal...
2025-07-17 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 All dispatchers stopped
2025-07-17 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 successfully reported itself as stopped in 4.3414 ms
2025-07-17 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27856:59e56142 has been stopped in total 989.6235 ms
2025-07-17 14:34:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:34:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 successfully announced in 120.5893 ms
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e successfully announced in 117.4774 ms
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e all the dispatchers started
2025-07-17 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 all the dispatchers started
2025-07-17 14:42:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 caught stopping signal...
2025-07-17 14:42:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e caught stopping signal...
2025-07-17 14:42:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e caught stopped signal...
2025-07-17 14:42:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 caught stopped signal...
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e All dispatchers stopped
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e successfully reported itself as stopped in 2.2394 ms
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:0d34ee9e has been stopped in total 711.2357 ms
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 All dispatchers stopped
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 successfully reported itself as stopped in 1.7283 ms
2025-07-17 14:42:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:07b09a80 has been stopped in total 833.956 ms
2025-07-17 14:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 successfully announced in 133.9721 ms
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea successfully announced in 131.5113 ms
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea all the dispatchers started
2025-07-17 14:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 all the dispatchers started
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 caught stopping signal...
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea caught stopping signal...
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea All dispatchers stopped
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea successfully reported itself as stopped in 2.357 ms
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:67a01eea has been stopped in total 423.9557 ms
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 All dispatchers stopped
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 successfully reported itself as stopped in 0.6884 ms
2025-07-17 14:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18480:9a0e35b7 has been stopped in total 437.7353 ms
2025-07-17 14:49:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 14:49:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 14:49:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae successfully announced in 113.7973 ms
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb successfully announced in 113.7969 ms
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb all the dispatchers started
2025-07-17 14:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae all the dispatchers started
2025-07-17 14:50:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb caught stopping signal...
2025-07-17 14:50:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae caught stopping signal...
2025-07-17 14:50:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae caught stopped signal...
2025-07-17 14:50:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb caught stopped signal...
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb All dispatchers stopped
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae All dispatchers stopped
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae successfully reported itself as stopped in 2.2335 ms
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:213294ae has been stopped in total 785.8226 ms
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb successfully reported itself as stopped in 2.5292 ms
2025-07-17 14:50:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4324:068ec1bb has been stopped in total 786.5261 ms
2025-07-17 15:23:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 15:23:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 15:23:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:23:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:23:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:23:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:23:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:23:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 successfully announced in 126.2516 ms
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f successfully announced in 128.0349 ms
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 all the dispatchers started
2025-07-17 15:23:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f all the dispatchers started
2025-07-17 15:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 caught stopping signal...
2025-07-17 15:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f caught stopping signal...
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f caught stopped signal...
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 caught stopped signal...
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 All dispatchers stopped
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 successfully reported itself as stopped in 1.6788 ms
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:af0b1a06 has been stopped in total 699.1287 ms
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f All dispatchers stopped
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f successfully reported itself as stopped in 0.7265 ms
2025-07-17 15:30:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32248:f1dfbd0f has been stopped in total 725.4565 ms
2025-07-17 15:30:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 15:30:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:30:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 successfully announced in 120.1166 ms
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb successfully announced in 120.14 ms
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 all the dispatchers started
2025-07-17 15:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb all the dispatchers started
2025-07-17 15:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 caught stopping signal...
2025-07-17 15:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb caught stopping signal...
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb caught stopped signal...
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 caught stopped signal...
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb All dispatchers stopped
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 All dispatchers stopped
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb successfully reported itself as stopped in 2.017 ms
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:aa725eeb has been stopped in total 876.6029 ms
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 successfully reported itself as stopped in 0.8372 ms
2025-07-17 15:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17440:ea548a04 has been stopped in total 905.8758 ms
2025-07-17 15:39:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 15:39:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 15:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 successfully announced in 121.3583 ms
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 successfully announced in 121.7328 ms
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 all the dispatchers started
2025-07-17 15:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 all the dispatchers started
2025-07-17 15:53:11 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnSaveAsync(PurchaseOrderReceivingDto arg1, ItemChangedType arg2) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 248
   at BootstrapBlazor.Components.Table`1.InternalOnSaveAsync(TItem item, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveModelAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.ClickUpdateButtonCallback()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 15:53:17 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnDeleteAsync(IEnumerable`1 arg) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 252
   at BootstrapBlazor.Components.Table`1.InternalOnDeleteAsync()
   at BootstrapBlazor.Components.Table`1.<DeleteAsync>g__DeleteItemsAsync|1549_0()
   at BootstrapBlazor.Components.Table`1.DeleteAsync()
   at BootstrapBlazor.Components.PopConfirmButton.OnClickConfirm()
   at BootstrapBlazor.Components.PopConfirmButtonContent.OnConfirmClick()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 caught stopping signal...
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 caught stopping signal...
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 All dispatchers stopped
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 successfully reported itself as stopped in 19.6043 ms
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:f2ee1657 has been stopped in total 426.6601 ms
2025-07-17 16:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 caught stopped signal...
2025-07-17 16:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 All dispatchers stopped
2025-07-17 16:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 successfully reported itself as stopped in 0.9229 ms
2025-07-17 16:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33216:88bac6b8 has been stopped in total 760.8787 ms
2025-07-17 16:26:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:26:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:26:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b successfully announced in 119.3288 ms
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd successfully announced in 119.3281 ms
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd all the dispatchers started
2025-07-17 16:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b all the dispatchers started
2025-07-17 16:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b caught stopping signal...
2025-07-17 16:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd caught stopping signal...
2025-07-17 16:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd All dispatchers stopped
2025-07-17 16:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd successfully reported itself as stopped in 1.9408 ms
2025-07-17 16:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:17a116dd has been stopped in total 10.4622 ms
2025-07-17 16:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b caught stopped signal...
2025-07-17 16:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b All dispatchers stopped
2025-07-17 16:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b successfully reported itself as stopped in 0.9735 ms
2025-07-17 16:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21128:c69f9c1b has been stopped in total 981.8103 ms
2025-07-17 16:27:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:27:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:27:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e7e04869 successfully announced in 279.1993 ms
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e194118a successfully announced in 279.2132 ms
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e7e04869 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e194118a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e7e04869 all the dispatchers started
2025-07-17 16:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32136:e194118a all the dispatchers started
2025-07-17 16:31:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:31:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:31:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 successfully announced in 116.1723 ms
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 successfully announced in 116.1631 ms
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 all the dispatchers started
2025-07-17 16:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 all the dispatchers started
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 caught stopping signal...
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 caught stopping signal...
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 All dispatchers stopped
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 All dispatchers stopped
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 successfully reported itself as stopped in 2.1075 ms
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:569c5f10 has been stopped in total 451.9645 ms
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 successfully reported itself as stopped in 1.2966 ms
2025-07-17 16:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5100:89564ef6 has been stopped in total 452.3855 ms
2025-07-17 16:34:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:34:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 successfully announced in 112.1155 ms
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 successfully announced in 112.1371 ms
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 all the dispatchers started
2025-07-17 16:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 all the dispatchers started
2025-07-17 16:35:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 caught stopping signal...
2025-07-17 16:35:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 caught stopping signal...
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 caught stopped signal...
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 caught stopped signal...
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 All dispatchers stopped
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 successfully reported itself as stopped in 1.9851 ms
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:775fbe67 has been stopped in total 972.0303 ms
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 All dispatchers stopped
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 successfully reported itself as stopped in 1.7088 ms
2025-07-17 16:35:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30728:c1c7e441 has been stopped in total 982.2504 ms
2025-07-17 16:35:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:35:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:35:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f successfully announced in 136.0474 ms
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 successfully announced in 136.0679 ms
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 all the dispatchers started
2025-07-17 16:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f all the dispatchers started
2025-07-17 16:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f caught stopping signal...
2025-07-17 16:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 caught stopping signal...
2025-07-17 16:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 caught stopped signal...
2025-07-17 16:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f caught stopped signal...
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f All dispatchers stopped
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f successfully reported itself as stopped in 2.0763 ms
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:b7cc2f9f has been stopped in total 808.0499 ms
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 All dispatchers stopped
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 successfully reported itself as stopped in 1.5916 ms
2025-07-17 16:35:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29492:1bad16b3 has been stopped in total 823.3901 ms
2025-07-17 16:36:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:36:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:36:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed successfully announced in 112.76 ms
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b successfully announced in 113.5943 ms
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:36:04 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed all the dispatchers started
2025-07-17 16:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b all the dispatchers started
2025-07-17 16:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed caught stopping signal...
2025-07-17 16:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b caught stopping signal...
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed All dispatchers stopped
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed successfully reported itself as stopped in 2.4515 ms
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:d8ee73ed has been stopped in total 216.6758 ms
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b All dispatchers stopped
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b successfully reported itself as stopped in 1.4978 ms
2025-07-17 16:36:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:873eea4b has been stopped in total 228.1981 ms
2025-07-17 16:36:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:36:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:36:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 successfully announced in 116.9593 ms
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca successfully announced in 103.3724 ms
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca all the dispatchers started
2025-07-17 16:36:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 all the dispatchers started
2025-07-17 16:38:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 caught stopping signal...
2025-07-17 16:38:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca caught stopping signal...
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca caught stopped signal...
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 caught stopped signal...
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca All dispatchers stopped
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca successfully reported itself as stopped in 2.0683 ms
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:ac2d6cca has been stopped in total 610.4214 ms
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 All dispatchers stopped
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 successfully reported itself as stopped in 1.3663 ms
2025-07-17 16:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24644:be488f61 has been stopped in total 635.1063 ms
2025-07-17 16:38:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:38:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:38:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 successfully announced in 135.7455 ms
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 successfully announced in 135.8349 ms
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 all the dispatchers started
2025-07-17 16:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 all the dispatchers started
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 caught stopping signal...
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 caught stopping signal...
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 All dispatchers stopped
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 successfully reported itself as stopped in 1.6315 ms
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:5cf69257 has been stopped in total 207.1538 ms
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 All dispatchers stopped
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 successfully reported itself as stopped in 0.717 ms
2025-07-17 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32692:f514d7b5 has been stopped in total 289.8817 ms
2025-07-17 16:40:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:40:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:40:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f successfully announced in 114.9064 ms
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 successfully announced in 114.954 ms
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f all the dispatchers started
2025-07-17 16:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 all the dispatchers started
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f caught stopping signal...
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 caught stopping signal...
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f caught stopped signal...
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 caught stopped signal...
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 All dispatchers stopped
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 successfully reported itself as stopped in 1.8986 ms
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:1b688d37 has been stopped in total 544.8843 ms
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f All dispatchers stopped
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f successfully reported itself as stopped in 0.7401 ms
2025-07-17 16:41:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19880:9302f76f has been stopped in total 547.407 ms
2025-07-17 16:41:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:41:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:41:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec successfully announced in 112.2075 ms
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 successfully announced in 112.2038 ms
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec all the dispatchers started
2025-07-17 16:41:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 all the dispatchers started
2025-07-17 16:46:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 caught stopping signal...
2025-07-17 16:46:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec caught stopping signal...
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 All dispatchers stopped
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 successfully reported itself as stopped in 1.6308 ms
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:50189f86 has been stopped in total 259.1579 ms
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec All dispatchers stopped
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec successfully reported itself as stopped in 0.6624 ms
2025-07-17 16:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27876:cf9195ec has been stopped in total 350.6813 ms
2025-07-17 16:46:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:46:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:46:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c successfully announced in 114.3392 ms
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 successfully announced in 114.3604 ms
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 all the dispatchers started
2025-07-17 16:46:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c all the dispatchers started
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c caught stopping signal...
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 caught stopping signal...
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 All dispatchers stopped
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 successfully reported itself as stopped in 1.4571 ms
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:b0d97fa2 has been stopped in total 34.6318 ms
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c All dispatchers stopped
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c successfully reported itself as stopped in 0.71 ms
2025-07-17 16:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:8e026d0c has been stopped in total 39.1899 ms
2025-07-17 16:47:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:47:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 successfully announced in 123.4043 ms
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f successfully announced in 124.1554 ms
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 all the dispatchers started
2025-07-17 16:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f all the dispatchers started
2025-07-17 16:47:39 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnDeleteAsync(IEnumerable`1 arg) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 273
   at BootstrapBlazor.Components.Table`1.InternalOnDeleteAsync()
   at BootstrapBlazor.Components.Table`1.<DeleteAsync>g__DeleteItemsAsync|1549_0()
   at BootstrapBlazor.Components.Table`1.DeleteAsync()
   at BootstrapBlazor.Components.TableToolbar`1.OnConfirm(TableToolbarPopConfirmButton`1 button)
   at BootstrapBlazor.Components.PopConfirmButton.OnClickConfirm()
   at BootstrapBlazor.Components.PopConfirmButtonContent.OnConfirmClick()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f caught stopping signal...
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 caught stopping signal...
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f All dispatchers stopped
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f successfully reported itself as stopped in 1.6422 ms
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:7c70f18f has been stopped in total 250.5899 ms
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 All dispatchers stopped
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 successfully reported itself as stopped in 0.7851 ms
2025-07-17 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14496:c8f155c4 has been stopped in total 277.1288 ms
2025-07-17 16:48:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:48:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:48:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 successfully announced in 113.687 ms
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 successfully announced in 115.3523 ms
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 all the dispatchers started
2025-07-17 16:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 all the dispatchers started
2025-07-17 16:50:45 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The method or operation is not implemented."
System.NotImplementedException: The method or operation is not implemented.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.OnSaveAsync(PurchaseOrderReceivingDto arg1, ItemChangedType arg2) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderReceivingPage.razor:line 270
   at BootstrapBlazor.Components.Table`1.InternalOnSaveAsync(TItem item, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveModelAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.SaveAsync(EditContext context, ItemChangedType changedType)
   at BootstrapBlazor.Components.Table`1.ClickUpdateButtonCallback()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-17 16:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 caught stopping signal...
2025-07-17 16:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 caught stopping signal...
2025-07-17 16:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 caught stopped signal...
2025-07-17 16:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 caught stopped signal...
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 All dispatchers stopped
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 successfully reported itself as stopped in 2.191 ms
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:96e8db60 has been stopped in total 747.0558 ms
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 All dispatchers stopped
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 successfully reported itself as stopped in 0.6954 ms
2025-07-17 16:56:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26296:105b3527 has been stopped in total 759.7377 ms
2025-07-17 16:56:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:56:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:56:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 successfully announced in 114.1237 ms
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad successfully announced in 114.0996 ms
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 all the dispatchers started
2025-07-17 16:56:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad all the dispatchers started
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 caught stopping signal...
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad caught stopping signal...
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 All dispatchers stopped
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad All dispatchers stopped
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 successfully reported itself as stopped in 2.2588 ms
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad successfully reported itself as stopped in 2.2562 ms
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:b9ac48ad has been stopped in total 275.0487 ms
2025-07-17 16:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:8666a587 has been stopped in total 275.1949 ms
2025-07-17 16:57:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:57:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:57:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 successfully announced in 111.5073 ms
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e successfully announced in 112.7681 ms
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e all the dispatchers started
2025-07-17 16:57:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 all the dispatchers started
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e caught stopping signal...
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 caught stopping signal...
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e All dispatchers stopped
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e successfully reported itself as stopped in 1.5692 ms
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:a037973e has been stopped in total 164.6835 ms
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 All dispatchers stopped
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 successfully reported itself as stopped in 0.6331 ms
2025-07-17 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17472:b3a9a476 has been stopped in total 191.5847 ms
2025-07-17 16:59:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:59:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 successfully announced in 123.9961 ms
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b successfully announced in 124.1455 ms
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 all the dispatchers started
2025-07-17 16:59:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b all the dispatchers started
2025-07-17 16:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b caught stopping signal...
2025-07-17 16:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 caught stopping signal...
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 caught stopped signal...
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b caught stopped signal...
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 All dispatchers stopped
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 successfully reported itself as stopped in 2.305 ms
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:03644266 has been stopped in total 606.6592 ms
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b All dispatchers stopped
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b successfully reported itself as stopped in 1.6381 ms
2025-07-17 16:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27500:687ec49b has been stopped in total 639.207 ms
2025-07-17 16:59:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 16:59:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 16:59:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff successfully announced in 115.1493 ms
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 successfully announced in 114.9082 ms
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff all the dispatchers started
2025-07-17 16:59:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 all the dispatchers started
2025-07-17 17:00:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 caught stopping signal...
2025-07-17 17:00:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff caught stopping signal...
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff caught stopped signal...
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 caught stopped signal...
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 All dispatchers stopped
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff All dispatchers stopped
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff successfully reported itself as stopped in 2.1808 ms
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 successfully reported itself as stopped in 2.1852 ms
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:ae5f6cff has been stopped in total 877.4123 ms
2025-07-17 17:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33020:8725f331 has been stopped in total 877.7259 ms
2025-07-17 17:00:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-17 17:00:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-17 17:00:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 successfully announced in 111.3717 ms
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 successfully announced in 75.7373 ms
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 all the dispatchers started
2025-07-17 17:00:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 all the dispatchers started
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 caught stopping signal...
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 caught stopping signal...
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 All dispatchers stopped
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 successfully reported itself as stopped in 1.8759 ms
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:f1da27d7 has been stopped in total 45.5609 ms
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 All dispatchers stopped
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 successfully reported itself as stopped in 0.7917 ms
2025-07-17 17:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:18dcb8f0 has been stopped in total 79.0426 ms
