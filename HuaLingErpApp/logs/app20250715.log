2025-07-15 08:26:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 08:26:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:26:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 successfully announced in 114.7752 ms
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a successfully announced in 114.7847 ms
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 all the dispatchers started
2025-07-15 08:26:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a all the dispatchers started
2025-07-15 08:49:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 caught stopping signal...
2025-07-15 08:49:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a caught stopping signal...
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a caught stopped signal...
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 caught stopped signal...
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a All dispatchers stopped
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a successfully reported itself as stopped in 2.4798 ms
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:6ffdeb0a has been stopped in total 768.6266 ms
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 All dispatchers stopped
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 successfully reported itself as stopped in 1.4205 ms
2025-07-15 08:49:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22904:55172a75 has been stopped in total 987.4265 ms
2025-07-15 08:49:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 08:49:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:49:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 successfully announced in 115.9004 ms
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 successfully announced in 115.9019 ms
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 all the dispatchers started
2025-07-15 08:49:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 all the dispatchers started
2025-07-15 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 caught stopping signal...
2025-07-15 08:50:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 caught stopping signal...
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 All dispatchers stopped
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 successfully reported itself as stopped in 1.674 ms
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:ecd68587 has been stopped in total 177.838 ms
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 All dispatchers stopped
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 successfully reported itself as stopped in 0.5653 ms
2025-07-15 08:50:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2124:14725885 has been stopped in total 179.1721 ms
2025-07-15 08:51:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 08:51:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:51:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 successfully announced in 105.5499 ms
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 successfully announced in 105.5613 ms
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 all the dispatchers started
2025-07-15 08:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 all the dispatchers started
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 caught stopping signal...
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 caught stopping signal...
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 All dispatchers stopped
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 successfully reported itself as stopped in 2.4292 ms
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:2ff08de9 has been stopped in total 229.2562 ms
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 All dispatchers stopped
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 successfully reported itself as stopped in 5.0724 ms
2025-07-15 08:54:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14648:c0c351b9 has been stopped in total 565.3163 ms
2025-07-15 08:54:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 08:54:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 08:54:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f successfully announced in 105.8585 ms
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 successfully announced in 105.8676 ms
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 all the dispatchers started
2025-07-15 08:54:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f all the dispatchers started
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 caught stopping signal...
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f caught stopping signal...
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 All dispatchers stopped
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 successfully reported itself as stopped in 1.6975 ms
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:7eca2d47 has been stopped in total 319.1037 ms
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f All dispatchers stopped
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f successfully reported itself as stopped in 0.6818 ms
2025-07-15 09:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29432:0f3e825f has been stopped in total 499.8052 ms
2025-07-15 09:01:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:01:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 successfully announced in 114.9606 ms
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 successfully announced in 115.4926 ms
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 all the dispatchers started
2025-07-15 09:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 all the dispatchers started
2025-07-15 09:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 caught stopping signal...
2025-07-15 09:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 caught stopping signal...
2025-07-15 09:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 caught stopped signal...
2025-07-15 09:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 caught stopped signal...
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 All dispatchers stopped
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 successfully reported itself as stopped in 3.7519 ms
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:57aba546 has been stopped in total 629.3446 ms
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 All dispatchers stopped
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 successfully reported itself as stopped in 1.4304 ms
2025-07-15 09:29:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6836:124e82f5 has been stopped in total 933.5824 ms
2025-07-15 09:29:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:29:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d successfully announced in 106.0017 ms
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 successfully announced in 106.5795 ms
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 all the dispatchers started
2025-07-15 09:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d all the dispatchers started
2025-07-15 09:41:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d caught stopping signal...
2025-07-15 09:41:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 caught stopping signal...
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 caught stopped signal...
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d caught stopped signal...
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d All dispatchers stopped
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 All dispatchers stopped
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d successfully reported itself as stopped in 1.9177 ms
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 successfully reported itself as stopped in 0.8148 ms
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:baa8f60d has been stopped in total 737.1593 ms
2025-07-15 09:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19696:1c639157 has been stopped in total 736.978 ms
2025-07-15 09:42:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:42:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:42:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af successfully announced in 146.1018 ms
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 successfully announced in 153.428 ms
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af all the dispatchers started
2025-07-15 09:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 all the dispatchers started
2025-07-15 09:42:07 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object of type 'BootstrapBlazor.Components.EditorItem`2[[HuaLingErpApp.Shared.Models.PurchaseOrder, HuaLingErpApp.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]' does not have a property matching the name 'IsShow'."
System.InvalidOperationException: Object of type 'BootstrapBlazor.Components.EditorItem`2[[HuaLingErpApp.Shared.Models.PurchaseOrder, HuaLingErpApp.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]' does not have a property matching the name 'IsShow'.
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.ThrowForUnknownIncomingParameterName(Type targetType, String parameterName)
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.SetProperties(ParameterView& parameters, Object target)
   at Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(ParameterView parameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 caught stopping signal...
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af caught stopping signal...
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 All dispatchers stopped
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 successfully reported itself as stopped in 1.6011 ms
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e6411db1 has been stopped in total 433.9984 ms
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af All dispatchers stopped
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af successfully reported itself as stopped in 0.6998 ms
2025-07-15 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11980:e202f1af has been stopped in total 471.0035 ms
2025-07-15 09:42:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:42:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c successfully announced in 134.732 ms
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 successfully announced in 124.7039 ms
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c all the dispatchers started
2025-07-15 09:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 all the dispatchers started
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c caught stopping signal...
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 caught stopping signal...
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 caught stopped signal...
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c caught stopped signal...
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c All dispatchers stopped
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c successfully reported itself as stopped in 2.0404 ms
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:51124f4c has been stopped in total 751.3563 ms
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 All dispatchers stopped
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 successfully reported itself as stopped in 1.1966 ms
2025-07-15 09:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:b14d5ae5 has been stopped in total 785.159 ms
2025-07-15 09:46:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:46:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:46:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f successfully announced in 108.044 ms
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 successfully announced in 108.3874 ms
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f all the dispatchers started
2025-07-15 09:46:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 all the dispatchers started
2025-07-15 09:46:50 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object of type 'BootstrapBlazor.Components.EditorItem`2[[HuaLingErpApp.Shared.Models.PurchaseOrder, HuaLingErpApp.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]' does not have a property matching the name 'ShowLabel'."
System.InvalidOperationException: Object of type 'BootstrapBlazor.Components.EditorItem`2[[HuaLingErpApp.Shared.Models.PurchaseOrder, HuaLingErpApp.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[System.Int32, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]' does not have a property matching the name 'ShowLabel'.
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.ThrowForUnknownIncomingParameterName(Type targetType, String parameterName)
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.SetProperties(ParameterView& parameters, Object target)
   at Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(ParameterView parameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 caught stopping signal...
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f caught stopping signal...
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f All dispatchers stopped
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 All dispatchers stopped
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f successfully reported itself as stopped in 2.2218 ms
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:81e3e29f has been stopped in total 468.2463 ms
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 successfully reported itself as stopped in 2.5897 ms
2025-07-15 09:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13712:9fcd5945 has been stopped in total 468.9949 ms
2025-07-15 09:47:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:47:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:47:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b successfully announced in 103.7868 ms
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e successfully announced in 106.1314 ms
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b all the dispatchers started
2025-07-15 09:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e all the dispatchers started
2025-07-15 09:51:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e caught stopping signal...
2025-07-15 09:51:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b caught stopping signal...
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b caught stopped signal...
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e caught stopped signal...
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e All dispatchers stopped
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b All dispatchers stopped
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e successfully reported itself as stopped in 2.1123 ms
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:4f7e0b1e has been stopped in total 837.5339 ms
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b successfully reported itself as stopped in 23.9556 ms
2025-07-15 09:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15936:0e20e56b has been stopped in total 860.1002 ms
2025-07-15 09:51:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:51:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f successfully announced in 111.6197 ms
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 successfully announced in 111.9272 ms
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f all the dispatchers started
2025-07-15 09:51:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 all the dispatchers started
2025-07-15 09:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f caught stopping signal...
2025-07-15 09:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 caught stopping signal...
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 caught stopped signal...
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f caught stopped signal...
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 All dispatchers stopped
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 successfully reported itself as stopped in 1.7107 ms
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:26ee0c81 has been stopped in total 563.1597 ms
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f All dispatchers stopped
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f successfully reported itself as stopped in 0.8266 ms
2025-07-15 09:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28300:2ba9bf9f has been stopped in total 680.2139 ms
2025-07-15 09:56:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:56:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:56:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c successfully announced in 109.4818 ms
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 successfully announced in 109.6753 ms
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c all the dispatchers started
2025-07-15 09:56:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 all the dispatchers started
2025-07-15 09:56:13 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged=\"OnVendorChanged\" *@' is not a valid attribute name."
System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnVendorChanged" *@' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
2025-07-15 09:56:13 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"E43eUCZhR2lIA9qmOFBRSBGzCe_-zCRfLSZz290sW9g"'.
System.AggregateException: One or more errors occurred. (InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnVendorChanged" *@' is not a valid attribute name.)
 ---> System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@* OnSelectedItemChanged="OnVendorChanged" *@' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 caught stopping signal...
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c caught stopping signal...
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 All dispatchers stopped
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 successfully reported itself as stopped in 3.613 ms
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:600ea8a4 has been stopped in total 351.1858 ms
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c All dispatchers stopped
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c successfully reported itself as stopped in 0.71 ms
2025-07-15 09:56:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25776:942a164c has been stopped in total 352.3246 ms
2025-07-15 09:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 successfully announced in 113.1975 ms
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 successfully announced in 114.1732 ms
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 all the dispatchers started
2025-07-15 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 all the dispatchers started
2025-07-15 09:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 caught stopping signal...
2025-07-15 09:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 caught stopping signal...
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 caught stopped signal...
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 caught stopped signal...
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 All dispatchers stopped
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 All dispatchers stopped
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 successfully reported itself as stopped in 2.1628 ms
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:4c64f3c1 has been stopped in total 631.421 ms
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 successfully reported itself as stopped in 3.0887 ms
2025-07-15 09:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5856:a5a16ce4 has been stopped in total 630.9831 ms
2025-07-15 09:58:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 09:58:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 09:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d successfully announced in 107.3625 ms
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 successfully announced in 106.6997 ms
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d all the dispatchers started
2025-07-15 09:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 all the dispatchers started
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 caught stopping signal...
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d caught stopping signal...
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 All dispatchers stopped
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 successfully reported itself as stopped in 2.1877 ms
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:f9e2c1c9 has been stopped in total 82.356 ms
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d All dispatchers stopped
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d successfully reported itself as stopped in 0.8438 ms
2025-07-15 10:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11188:e721cf4d has been stopped in total 211.7235 ms
2025-07-15 10:04:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:04:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:04:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec successfully announced in 107.4157 ms
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 successfully announced in 107.1346 ms
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec all the dispatchers started
2025-07-15 10:04:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 all the dispatchers started
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 caught stopping signal...
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec caught stopping signal...
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec All dispatchers stopped
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 All dispatchers stopped
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec successfully reported itself as stopped in 1.844 ms
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:9f5622ec has been stopped in total 248.4113 ms
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 successfully reported itself as stopped in 0.9431 ms
2025-07-15 10:08:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23256:8f1099a5 has been stopped in total 248.7847 ms
2025-07-15 10:08:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:08:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:08:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c successfully announced in 113.1158 ms
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 successfully announced in 113.1865 ms
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c all the dispatchers started
2025-07-15 10:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 all the dispatchers started
2025-07-15 10:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c caught stopping signal...
2025-07-15 10:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 caught stopping signal...
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 caught stopped signal...
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c caught stopped signal...
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 All dispatchers stopped
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c All dispatchers stopped
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 successfully reported itself as stopped in 1.6326 ms
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:56dab348 has been stopped in total 506.1542 ms
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c successfully reported itself as stopped in 1.2977 ms
2025-07-15 10:09:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:91785d5c has been stopped in total 507.212 ms
2025-07-15 10:09:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:09:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:09:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b successfully announced in 127.231 ms
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 successfully announced in 128.1287 ms
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 all the dispatchers started
2025-07-15 10:09:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b all the dispatchers started
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b caught stopping signal...
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 caught stopping signal...
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b All dispatchers stopped
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 All dispatchers stopped
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b successfully reported itself as stopped in 1.6136 ms
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:ea76282b has been stopped in total 357.1922 ms
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 successfully reported itself as stopped in 0.8566 ms
2025-07-15 10:10:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25516:06ce3258 has been stopped in total 375.7798 ms
2025-07-15 10:10:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:10:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:10:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:10:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 successfully announced in 108.1224 ms
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 successfully announced in 108.8567 ms
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 all the dispatchers started
2025-07-15 10:10:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 all the dispatchers started
2025-07-15 10:11:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 caught stopping signal...
2025-07-15 10:11:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 caught stopping signal...
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 All dispatchers stopped
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 successfully reported itself as stopped in 1.6565 ms
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:ca919ea7 has been stopped in total 417.0304 ms
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 All dispatchers stopped
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 successfully reported itself as stopped in 0.8181 ms
2025-07-15 10:11:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24620:b0163c83 has been stopped in total 461.6328 ms
2025-07-15 10:11:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:11:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:11:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 successfully announced in 109.0846 ms
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 successfully announced in 109.9185 ms
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 all the dispatchers started
2025-07-15 10:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 all the dispatchers started
2025-07-15 10:12:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 caught stopping signal...
2025-07-15 10:12:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 caught stopping signal...
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 caught stopped signal...
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 caught stopped signal...
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 All dispatchers stopped
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 successfully reported itself as stopped in 1.6231 ms
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:c532afd1 has been stopped in total 920.8261 ms
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 All dispatchers stopped
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 successfully reported itself as stopped in 0.9079 ms
2025-07-15 10:12:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11904:6c958318 has been stopped in total 945.9616 ms
2025-07-15 10:12:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:12:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:12:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c successfully announced in 110.5044 ms
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df successfully announced in 110.5059 ms
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c all the dispatchers started
2025-07-15 10:12:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df all the dispatchers started
2025-07-15 10:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df caught stopping signal...
2025-07-15 10:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c caught stopping signal...
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df All dispatchers stopped
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df successfully reported itself as stopped in 1.9137 ms
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:2189d1df has been stopped in total 243.4968 ms
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c All dispatchers stopped
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c successfully reported itself as stopped in 1.1237 ms
2025-07-15 10:16:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24240:fbca9b4c has been stopped in total 297.3582 ms
2025-07-15 10:16:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:16:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:16:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 successfully announced in 110.7979 ms
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade successfully announced in 111.3896 ms
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 all the dispatchers started
2025-07-15 10:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade all the dispatchers started
2025-07-15 10:17:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 caught stopping signal...
2025-07-15 10:17:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade caught stopping signal...
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade caught stopped signal...
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 caught stopped signal...
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 All dispatchers stopped
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 successfully reported itself as stopped in 2.0632 ms
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8982ac72 has been stopped in total 686.0027 ms
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade All dispatchers stopped
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade successfully reported itself as stopped in 1.6271 ms
2025-07-15 10:17:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29384:8de6bade has been stopped in total 696.31 ms
2025-07-15 10:17:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:17:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:17:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a successfully announced in 105.3825 ms
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece successfully announced in 101.336 ms
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece all the dispatchers started
2025-07-15 10:17:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a all the dispatchers started
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a caught stopping signal...
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece caught stopping signal...
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece All dispatchers stopped
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece successfully reported itself as stopped in 1.9587 ms
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:ce885ece has been stopped in total 270.8725 ms
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a All dispatchers stopped
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a successfully reported itself as stopped in 0.8346 ms
2025-07-15 10:19:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15080:74da659a has been stopped in total 313.9726 ms
2025-07-15 10:19:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:19:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:19:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 successfully announced in 109.6258 ms
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 successfully announced in 110.6114 ms
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 all the dispatchers started
2025-07-15 10:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 all the dispatchers started
2025-07-15 10:19:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 caught stopping signal...
2025-07-15 10:19:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 caught stopping signal...
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 All dispatchers stopped
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 successfully reported itself as stopped in 3.1649 ms
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:626f3dc2 has been stopped in total 171.7814 ms
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 All dispatchers stopped
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 successfully reported itself as stopped in 1.1442 ms
2025-07-15 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21456:338e4741 has been stopped in total 179.8847 ms
2025-07-15 10:20:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:20:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:20:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 successfully announced in 84.6277 ms
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 successfully announced in 106.8415 ms
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 all the dispatchers started
2025-07-15 10:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 all the dispatchers started
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 caught stopping signal...
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 caught stopping signal...
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 All dispatchers stopped
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 All dispatchers stopped
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 successfully reported itself as stopped in 1.8547 ms
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:89c7aad1 has been stopped in total 291.4496 ms
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 successfully reported itself as stopped in 1.0893 ms
2025-07-15 10:20:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19764:a4216fa9 has been stopped in total 291.7101 ms
2025-07-15 10:21:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:21:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:21:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 successfully announced in 103.6007 ms
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 successfully announced in 104.14 ms
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 all the dispatchers started
2025-07-15 10:21:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 all the dispatchers started
2025-07-15 10:23:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 caught stopping signal...
2025-07-15 10:23:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 caught stopping signal...
2025-07-15 10:23:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 caught stopped signal...
2025-07-15 10:23:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 caught stopped signal...
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 All dispatchers stopped
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 successfully reported itself as stopped in 2.4244 ms
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:2225dce3 has been stopped in total 642.3372 ms
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 All dispatchers stopped
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 successfully reported itself as stopped in 1.6777 ms
2025-07-15 10:23:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1620:30f4d166 has been stopped in total 682.4211 ms
2025-07-15 10:23:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:23:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:23:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 successfully announced in 108.8454 ms
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 successfully announced in 109.6709 ms
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 all the dispatchers started
2025-07-15 10:23:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 all the dispatchers started
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 caught stopping signal...
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 caught stopping signal...
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 caught stopped signal...
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 caught stopped signal...
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 All dispatchers stopped
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 All dispatchers stopped
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 successfully reported itself as stopped in 1.651 ms
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:674dde66 has been stopped in total 694.7432 ms
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 successfully reported itself as stopped in 2.6703 ms
2025-07-15 10:24:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17124:2834a281 has been stopped in total 695.1118 ms
2025-07-15 10:24:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:24:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:24:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 successfully announced in 109.3642 ms
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d successfully announced in 109.3913 ms
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 all the dispatchers started
2025-07-15 10:24:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d all the dispatchers started
2025-07-15 10:26:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d caught stopping signal...
2025-07-15 10:26:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 caught stopping signal...
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 caught stopped signal...
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d caught stopped signal...
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 All dispatchers stopped
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 successfully reported itself as stopped in 1.8426 ms
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:d6c4f4d5 has been stopped in total 764.2056 ms
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d All dispatchers stopped
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d successfully reported itself as stopped in 0.7459 ms
2025-07-15 10:26:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15952:af03dc1d has been stopped in total 829.0781 ms
2025-07-15 10:26:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:26:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:26:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 successfully announced in 106.1193 ms
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 successfully announced in 98.7741 ms
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 all the dispatchers started
2025-07-15 10:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 all the dispatchers started
2025-07-15 10:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 caught stopping signal...
2025-07-15 10:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 caught stopping signal...
2025-07-15 10:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 caught stopped signal...
2025-07-15 10:27:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 caught stopped signal...
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 All dispatchers stopped
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 successfully reported itself as stopped in 1.9639 ms
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:ee6d02a9 has been stopped in total 812.5222 ms
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 All dispatchers stopped
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 successfully reported itself as stopped in 0.6532 ms
2025-07-15 10:27:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17488:0f883812 has been stopped in total 838.6712 ms
2025-07-15 10:28:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:28:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:28:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 successfully announced in 109.2422 ms
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e successfully announced in 109.7852 ms
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 all the dispatchers started
2025-07-15 10:28:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e all the dispatchers started
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 caught stopping signal...
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e caught stopping signal...
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e caught stopped signal...
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 caught stopped signal...
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 All dispatchers stopped
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e All dispatchers stopped
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 successfully reported itself as stopped in 1.7367 ms
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:ea1b6dd7 has been stopped in total 582.4533 ms
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e successfully reported itself as stopped in 2.1218 ms
2025-07-15 10:29:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:5c82191e has been stopped in total 582.4613 ms
2025-07-15 10:29:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 10:29:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 10:29:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 successfully announced in 107.9374 ms
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 successfully announced in 108.5728 ms
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 all the dispatchers started
2025-07-15 10:29:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 all the dispatchers started
2025-07-15 11:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 caught stopping signal...
2025-07-15 11:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 caught stopping signal...
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 caught stopped signal...
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 All dispatchers stopped
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 All dispatchers stopped
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 successfully reported itself as stopped in 2.7571 ms
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 has been stopped in total 507.7096 ms
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:18aa0fe9 caught stopped signal...
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 successfully reported itself as stopped in 0.6686 ms
2025-07-15 11:06:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15048:4bc23fb3 has been stopped in total 508.3867 ms
2025-07-15 11:21:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:21:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:21:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b successfully announced in 109.8577 ms
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e successfully announced in 109.8552 ms
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b all the dispatchers started
2025-07-15 11:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e all the dispatchers started
2025-07-15 11:24:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b caught stopping signal...
2025-07-15 11:24:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e caught stopping signal...
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b All dispatchers stopped
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b successfully reported itself as stopped in 1.6425 ms
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:7dde977b has been stopped in total 237.996 ms
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e All dispatchers stopped
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e successfully reported itself as stopped in 1.0647 ms
2025-07-15 11:24:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17260:55312f4e has been stopped in total 568.6429 ms
2025-07-15 11:24:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:24:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 successfully announced in 124.0309 ms
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f successfully announced in 125.07 ms
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 all the dispatchers started
2025-07-15 11:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f all the dispatchers started
2025-07-15 11:25:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 caught stopping signal...
2025-07-15 11:25:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f caught stopping signal...
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f caught stopped signal...
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 caught stopped signal...
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f All dispatchers stopped
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 All dispatchers stopped
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f successfully reported itself as stopped in 1.9584 ms
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:6eecd38f has been stopped in total 725.7536 ms
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 successfully reported itself as stopped in 1.2659 ms
2025-07-15 11:25:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:d8c4eda3 has been stopped in total 740.3335 ms
2025-07-15 11:25:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:25:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:25:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc successfully announced in 98.3007 ms
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 successfully announced in 103.5681 ms
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc all the dispatchers started
2025-07-15 11:25:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 all the dispatchers started
2025-07-15 11:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 caught stopping signal...
2025-07-15 11:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc caught stopping signal...
2025-07-15 11:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 All dispatchers stopped
2025-07-15 11:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 successfully reported itself as stopped in 1.5718 ms
2025-07-15 11:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:2045fbd3 has been stopped in total 14.2259 ms
2025-07-15 11:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc caught stopped signal...
2025-07-15 11:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc All dispatchers stopped
2025-07-15 11:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc successfully reported itself as stopped in 1.1505 ms
2025-07-15 11:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12592:8400aecc has been stopped in total 961.2981 ms
2025-07-15 11:26:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:26:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:26:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 successfully announced in 116.1159 ms
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 successfully announced in 116.5372 ms
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 all the dispatchers started
2025-07-15 11:26:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 all the dispatchers started
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 caught stopping signal...
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 caught stopping signal...
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 All dispatchers stopped
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 successfully reported itself as stopped in 2.4376 ms
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:64cbf7f5 has been stopped in total 123.9928 ms
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 All dispatchers stopped
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 successfully reported itself as stopped in 1.5813 ms
2025-07-15 11:27:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27096:78373300 has been stopped in total 129.8024 ms
2025-07-15 11:27:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:27:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:27:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 successfully announced in 115.6187 ms
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a successfully announced in 104.8761 ms
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 all the dispatchers started
2025-07-15 11:27:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a all the dispatchers started
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 caught stopping signal...
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a caught stopping signal...
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 All dispatchers stopped
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 successfully reported itself as stopped in 1.5432 ms
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:188f6ac2 has been stopped in total 454.3934 ms
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a All dispatchers stopped
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a successfully reported itself as stopped in 0.7199 ms
2025-07-15 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:2d04b89a has been stopped in total 479.5069 ms
2025-07-15 11:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c successfully announced in 109.5547 ms
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 successfully announced in 109.5738 ms
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 all the dispatchers started
2025-07-15 11:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c all the dispatchers started
2025-07-15 11:33:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c caught stopping signal...
2025-07-15 11:33:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 caught stopping signal...
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 caught stopped signal...
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c caught stopped signal...
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 All dispatchers stopped
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 successfully reported itself as stopped in 2.2573 ms
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:d788bd54 has been stopped in total 917.6029 ms
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c All dispatchers stopped
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c successfully reported itself as stopped in 0.6466 ms
2025-07-15 11:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24988:7beb409c has been stopped in total 925.7403 ms
2025-07-15 11:33:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:33:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:33:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac successfully announced in 93.3649 ms
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 successfully announced in 111.1233 ms
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 all the dispatchers started
2025-07-15 11:33:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac all the dispatchers started
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 caught stopping signal...
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac caught stopping signal...
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac All dispatchers stopped
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac successfully reported itself as stopped in 3.4976 ms
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:da15b2ac has been stopped in total 212.5828 ms
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 All dispatchers stopped
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 successfully reported itself as stopped in 2.5043 ms
2025-07-15 11:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15864:9e136ed0 has been stopped in total 267.5441 ms
2025-07-15 11:35:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:35:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:35:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 successfully announced in 128.2646 ms
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 successfully announced in 127.9873 ms
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 all the dispatchers started
2025-07-15 11:35:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 all the dispatchers started
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 caught stopping signal...
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 caught stopping signal...
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 All dispatchers stopped
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 successfully reported itself as stopped in 2.9509 ms
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:80fc2006 has been stopped in total 26.1588 ms
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 All dispatchers stopped
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 successfully reported itself as stopped in 1.3597 ms
2025-07-15 11:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14596:f0f0f9c3 has been stopped in total 30.29 ms
2025-07-15 11:35:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:35:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:35:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa successfully announced in 144.76 ms
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 successfully announced in 144.7567 ms
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 all the dispatchers started
2025-07-15 11:35:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa all the dispatchers started
2025-07-15 11:38:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 caught stopping signal...
2025-07-15 11:38:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa caught stopping signal...
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa All dispatchers stopped
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa successfully reported itself as stopped in 3.4668 ms
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:523881aa has been stopped in total 431.3361 ms
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 All dispatchers stopped
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 successfully reported itself as stopped in 1.0222 ms
2025-07-15 11:38:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17596:e94b48e2 has been stopped in total 464.9223 ms
2025-07-15 11:39:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:39:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:39:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 successfully announced in 109.9369 ms
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 successfully announced in 109.938 ms
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 all the dispatchers started
2025-07-15 11:39:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 all the dispatchers started
2025-07-15 11:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 caught stopping signal...
2025-07-15 11:40:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 caught stopping signal...
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 All dispatchers stopped
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 successfully reported itself as stopped in 1.7906 ms
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:6b7365c2 has been stopped in total 328.994 ms
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 All dispatchers stopped
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 successfully reported itself as stopped in 0.8302 ms
2025-07-15 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13024:aaa2ad48 has been stopped in total 351.5812 ms
2025-07-15 11:40:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:40:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:40:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 successfully announced in 112.3723 ms
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 successfully announced in 113.709 ms
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 all the dispatchers started
2025-07-15 11:40:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 all the dispatchers started
2025-07-15 11:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 caught stopping signal...
2025-07-15 11:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 caught stopping signal...
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 All dispatchers stopped
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 successfully reported itself as stopped in 2.3215 ms
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:3a03f6e7 has been stopped in total 446.6206 ms
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 All dispatchers stopped
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 successfully reported itself as stopped in 0.9603 ms
2025-07-15 11:49:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29604:eabda8d9 has been stopped in total 477.7958 ms
2025-07-15 11:49:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:49:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:49:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee successfully announced in 114.2113 ms
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 successfully announced in 114.4605 ms
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 all the dispatchers started
2025-07-15 11:49:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee all the dispatchers started
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 caught stopping signal...
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee caught stopping signal...
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee All dispatchers stopped
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 All dispatchers stopped
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee successfully reported itself as stopped in 0.894 ms
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 successfully reported itself as stopped in 2.0426 ms
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ae8140ee has been stopped in total 488.7058 ms
2025-07-15 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5908:ee3a8629 has been stopped in total 489.5826 ms
2025-07-15 11:50:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:50:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:50:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a successfully announced in 103.0881 ms
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 successfully announced in 103.4186 ms
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 all the dispatchers started
2025-07-15 11:50:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a all the dispatchers started
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a caught stopping signal...
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 caught stopping signal...
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 caught stopped signal...
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a caught stopped signal...
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a All dispatchers stopped
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a successfully reported itself as stopped in 2.7344 ms
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:76301a7a has been stopped in total 543.9907 ms
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 All dispatchers stopped
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 successfully reported itself as stopped in 1.422 ms
2025-07-15 11:51:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18828:5d8176c9 has been stopped in total 546.7086 ms
2025-07-15 11:51:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:51:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:51:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d successfully announced in 113.3609 ms
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 successfully announced in 111.6395 ms
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d all the dispatchers started
2025-07-15 11:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 all the dispatchers started
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 caught stopping signal...
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d caught stopping signal...
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d All dispatchers stopped
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 All dispatchers stopped
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 successfully reported itself as stopped in 2.1969 ms
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d successfully reported itself as stopped in 2.789 ms
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:82e84bb4 has been stopped in total 465.1554 ms
2025-07-15 11:52:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26244:954a663d has been stopped in total 465.0062 ms
2025-07-15 11:52:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:52:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:52:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 successfully announced in 115.4684 ms
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 successfully announced in 118.0732 ms
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 all the dispatchers started
2025-07-15 11:52:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 all the dispatchers started
2025-07-15 11:56:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 caught stopping signal...
2025-07-15 11:56:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 caught stopping signal...
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 caught stopped signal...
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 caught stopped signal...
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 All dispatchers stopped
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 All dispatchers stopped
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 successfully reported itself as stopped in 0.82 ms
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:24302770 has been stopped in total 564.2003 ms
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 successfully reported itself as stopped in 2.4962 ms
2025-07-15 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29412:eecc86d6 has been stopped in total 565.0998 ms
2025-07-15 11:56:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:56:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:56:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad successfully announced in 105.495 ms
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 successfully announced in 105.5261 ms
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 all the dispatchers started
2025-07-15 11:56:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad all the dispatchers started
2025-07-15 11:58:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad caught stopping signal...
2025-07-15 11:58:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 caught stopping signal...
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 caught stopped signal...
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad caught stopped signal...
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 All dispatchers stopped
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 successfully reported itself as stopped in 2.0654 ms
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:767b2a58 has been stopped in total 953.5974 ms
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad All dispatchers stopped
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad successfully reported itself as stopped in 1.3725 ms
2025-07-15 11:58:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27832:a35bd3ad has been stopped in total 992.2813 ms
2025-07-15 11:59:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 11:59:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 successfully announced in 103.6919 ms
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b successfully announced in 109.2971 ms
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 all the dispatchers started
2025-07-15 11:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b all the dispatchers started
2025-07-15 12:21:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b caught stopping signal...
2025-07-15 12:21:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 caught stopping signal...
2025-07-15 12:21:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 All dispatchers stopped
2025-07-15 12:21:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 successfully reported itself as stopped in 2.7322 ms
2025-07-15 12:21:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:39cb7719 has been stopped in total 427.2273 ms
2025-07-15 12:21:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b All dispatchers stopped
2025-07-15 12:21:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b successfully reported itself as stopped in 0.7152 ms
2025-07-15 12:21:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18536:4773797b has been stopped in total 470.8276 ms
2025-07-15 12:22:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:22:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 successfully announced in 125.8893 ms
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 successfully announced in 125.196 ms
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 all the dispatchers started
2025-07-15 12:22:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 all the dispatchers started
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 caught stopping signal...
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 caught stopping signal...
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 caught stopped signal...
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 All dispatchers stopped
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 All dispatchers stopped
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 successfully reported itself as stopped in 2.3094 ms
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:1d2d3075 has been stopped in total 506.6269 ms
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 successfully reported itself as stopped in 0.8709 ms
2025-07-15 12:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21484:58ac68f3 has been stopped in total 507.3681 ms
2025-07-15 12:23:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:23:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:23:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 successfully announced in 109.2451 ms
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d successfully announced in 107.4058 ms
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 all the dispatchers started
2025-07-15 12:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d all the dispatchers started
2025-07-15 12:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 caught stopping signal...
2025-07-15 12:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d caught stopping signal...
2025-07-15 12:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d All dispatchers stopped
2025-07-15 12:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d successfully reported itself as stopped in 2.6721 ms
2025-07-15 12:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:cbc72c9d has been stopped in total 28.7573 ms
2025-07-15 12:29:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 caught stopped signal...
2025-07-15 12:29:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 All dispatchers stopped
2025-07-15 12:29:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 successfully reported itself as stopped in 1.2604 ms
2025-07-15 12:29:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24108:8f6f4fb9 has been stopped in total 987.1173 ms
2025-07-15 12:29:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:29:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:29:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 successfully announced in 107.5809 ms
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 successfully announced in 107.5798 ms
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 all the dispatchers started
2025-07-15 12:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 all the dispatchers started
2025-07-15 12:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 caught stopping signal...
2025-07-15 12:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 caught stopping signal...
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 caught stopped signal...
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 caught stopped signal...
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 All dispatchers stopped
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 successfully reported itself as stopped in 2.1617 ms
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:bb13f685 has been stopped in total 549.9275 ms
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 All dispatchers stopped
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 successfully reported itself as stopped in 1.0098 ms
2025-07-15 12:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17252:c7a18f55 has been stopped in total 583.4187 ms
2025-07-15 12:31:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:31:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:31:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a successfully announced in 111.0287 ms
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a successfully announced in 98.6733 ms
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a all the dispatchers started
2025-07-15 12:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a all the dispatchers started
2025-07-15 12:31:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a caught stopping signal...
2025-07-15 12:31:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a caught stopping signal...
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a caught stopped signal...
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a caught stopped signal...
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a All dispatchers stopped
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a All dispatchers stopped
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a successfully reported itself as stopped in 2.1086 ms
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:d82aef2a has been stopped in total 841.7922 ms
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a successfully reported itself as stopped in 2.4834 ms
2025-07-15 12:31:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19884:93ca7a6a has been stopped in total 842.4228 ms
2025-07-15 12:31:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:31:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:31:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 successfully announced in 116.9197 ms
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 successfully announced in 116.9282 ms
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 all the dispatchers started
2025-07-15 12:31:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 all the dispatchers started
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 caught stopping signal...
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 caught stopping signal...
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 All dispatchers stopped
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 All dispatchers stopped
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 successfully reported itself as stopped in 1.7799 ms
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 successfully reported itself as stopped in 2.1826 ms
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:22022998 has been stopped in total 104.1675 ms
2025-07-15 12:31:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14860:3aee51a4 has been stopped in total 103.9015 ms
2025-07-15 12:32:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:32:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c successfully announced in 111.5043 ms
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e successfully announced in 112.0133 ms
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c all the dispatchers started
2025-07-15 12:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e all the dispatchers started
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e caught stopping signal...
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c caught stopping signal...
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c caught stopped signal...
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e caught stopped signal...
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c All dispatchers stopped
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e All dispatchers stopped
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e successfully reported itself as stopped in 2.1676 ms
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:15982b5e has been stopped in total 710.6381 ms
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c successfully reported itself as stopped in 1.4509 ms
2025-07-15 12:33:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29876:2e5cfb9c has been stopped in total 713.3997 ms
2025-07-15 12:33:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 12:33:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 12:33:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c successfully announced in 133.8841 ms
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 successfully announced in 133.8273 ms
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 all the dispatchers started
2025-07-15 12:33:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c all the dispatchers started
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 caught stopping signal...
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c caught stopping signal...
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c caught stopped signal...
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 caught stopped signal...
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c All dispatchers stopped
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c successfully reported itself as stopped in 2.6875 ms
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:36f62f6c has been stopped in total 606.8813 ms
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 All dispatchers stopped
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 successfully reported itself as stopped in 0.8207 ms
2025-07-15 13:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13080:0ebbaf16 has been stopped in total 610.3683 ms
2025-07-15 13:02:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:02:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:02:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:02:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 successfully announced in 106.9445 ms
2025-07-15 13:02:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 successfully announced in 106.9371 ms
2025-07-15 13:02:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:02:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:02:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 all the dispatchers started
2025-07-15 13:02:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 all the dispatchers started
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 caught stopping signal...
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 caught stopping signal...
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 All dispatchers stopped
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 All dispatchers stopped
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 successfully reported itself as stopped in 1.8964 ms
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:d0490946 has been stopped in total 108.9366 ms
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 successfully reported itself as stopped in 0.7694 ms
2025-07-15 13:06:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10856:ec429986 has been stopped in total 108.7779 ms
2025-07-15 13:06:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:06:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:06:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 successfully announced in 108.125 ms
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 successfully announced in 108.1499 ms
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 all the dispatchers started
2025-07-15 13:06:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 all the dispatchers started
2025-07-15 13:08:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 caught stopping signal...
2025-07-15 13:08:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 caught stopping signal...
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 All dispatchers stopped
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 All dispatchers stopped
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 successfully reported itself as stopped in 2.2742 ms
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:3355de72 has been stopped in total 437.5382 ms
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 successfully reported itself as stopped in 0.8449 ms
2025-07-15 13:08:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8576:395a25b1 has been stopped in total 438.452 ms
2025-07-15 13:08:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:08:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:08:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 successfully announced in 133.1517 ms
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 successfully announced in 133.1238 ms
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 all the dispatchers started
2025-07-15 13:08:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 all the dispatchers started
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 caught stopping signal...
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 caught stopping signal...
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 caught stopped signal...
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 caught stopped signal...
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 All dispatchers stopped
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 All dispatchers stopped
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 successfully reported itself as stopped in 2.0031 ms
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:331b14f7 has been stopped in total 926.2184 ms
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 successfully reported itself as stopped in 1.8213 ms
2025-07-15 13:14:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14836:7f1f6b20 has been stopped in total 926.919 ms
2025-07-15 13:14:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:14:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:14:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 successfully announced in 94.0578 ms
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 successfully announced in 105.1311 ms
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 all the dispatchers started
2025-07-15 13:14:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 all the dispatchers started
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 caught stopping signal...
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 caught stopping signal...
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 caught stopped signal...
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 caught stopped signal...
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 All dispatchers stopped
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 All dispatchers stopped
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 successfully reported itself as stopped in 2.1907 ms
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:d020db95 has been stopped in total 562.3107 ms
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 successfully reported itself as stopped in 2.3731 ms
2025-07-15 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:89e70326 has been stopped in total 562.2657 ms
2025-07-15 13:15:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:15:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:15:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 successfully announced in 105.9856 ms
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 successfully announced in 109.1619 ms
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 all the dispatchers started
2025-07-15 13:15:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 all the dispatchers started
2025-07-15 13:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 caught stopping signal...
2025-07-15 13:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 caught stopping signal...
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 All dispatchers stopped
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 All dispatchers stopped
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 successfully reported itself as stopped in 1.9496 ms
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:817e0f28 has been stopped in total 452.6691 ms
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 successfully reported itself as stopped in 1.271 ms
2025-07-15 13:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16688:02af58f8 has been stopped in total 453.067 ms
2025-07-15 13:17:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:17:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:17:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe successfully announced in 111.1046 ms
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa successfully announced in 111.1222 ms
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa all the dispatchers started
2025-07-15 13:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe all the dispatchers started
2025-07-15 13:17:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe caught stopping signal...
2025-07-15 13:17:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa caught stopping signal...
2025-07-15 13:17:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa caught stopped signal...
2025-07-15 13:17:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe caught stopped signal...
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa All dispatchers stopped
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe All dispatchers stopped
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe successfully reported itself as stopped in 2.7168 ms
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa successfully reported itself as stopped in 2.752 ms
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:a80c66fe has been stopped in total 630.9165 ms
2025-07-15 13:17:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30008:6a79d8aa has been stopped in total 630.668 ms
2025-07-15 13:17:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:17:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:17:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb successfully announced in 106.9232 ms
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 successfully announced in 106.9075 ms
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb all the dispatchers started
2025-07-15 13:17:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 all the dispatchers started
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb caught stopping signal...
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 caught stopping signal...
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 All dispatchers stopped
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb All dispatchers stopped
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 successfully reported itself as stopped in 1.9888 ms
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:4f571e37 has been stopped in total 11.5361 ms
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb successfully reported itself as stopped in 2.0588 ms
2025-07-15 13:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25104:cab4c2cb has been stopped in total 11.8505 ms
2025-07-15 13:18:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:18:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:18:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde successfully announced in 105.2088 ms
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 successfully announced in 105.2096 ms
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde all the dispatchers started
2025-07-15 13:18:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 all the dispatchers started
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 caught stopping signal...
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde caught stopping signal...
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 All dispatchers stopped
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde All dispatchers stopped
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde successfully reported itself as stopped in 2.4772 ms
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:39cb3bde has been stopped in total 119.5106 ms
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 successfully reported itself as stopped in 21.4729 ms
2025-07-15 13:19:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29816:4a631478 has been stopped in total 138.7294 ms
2025-07-15 13:19:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:19:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:19:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 successfully announced in 109.9094 ms
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 successfully announced in 110.1714 ms
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 all the dispatchers started
2025-07-15 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 all the dispatchers started
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 caught stopping signal...
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 caught stopping signal...
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 All dispatchers stopped
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 successfully reported itself as stopped in 2.9524 ms
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:930ba1f1 has been stopped in total 37.3355 ms
2025-07-15 13:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 caught stopped signal...
2025-07-15 13:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 All dispatchers stopped
2025-07-15 13:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 successfully reported itself as stopped in 1.2061 ms
2025-07-15 13:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5996:fee50553 has been stopped in total 859.3853 ms
2025-07-15 13:27:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:27:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:27:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 successfully announced in 129.6145 ms
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac successfully announced in 129.6159 ms
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 all the dispatchers started
2025-07-15 13:27:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac all the dispatchers started
2025-07-15 13:28:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 caught stopping signal...
2025-07-15 13:28:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac caught stopping signal...
2025-07-15 13:28:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac caught stopped signal...
2025-07-15 13:28:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 caught stopped signal...
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac All dispatchers stopped
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 All dispatchers stopped
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac successfully reported itself as stopped in 2.1287 ms
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:3d62d1ac has been stopped in total 689.1816 ms
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 successfully reported itself as stopped in 0.6532 ms
2025-07-15 13:28:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:7453d130 has been stopped in total 690.1749 ms
2025-07-15 13:29:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:29:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:29:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 successfully announced in 105.8438 ms
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 successfully announced in 105.865 ms
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 all the dispatchers started
2025-07-15 13:29:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 all the dispatchers started
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 caught stopping signal...
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 caught stopping signal...
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 All dispatchers stopped
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 successfully reported itself as stopped in 2.3533 ms
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:32fd0069 has been stopped in total 474.5012 ms
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 All dispatchers stopped
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 successfully reported itself as stopped in 1.2948 ms
2025-07-15 13:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27420:ceb14008 has been stopped in total 539.2729 ms
2025-07-15 13:30:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:30:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:30:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 successfully announced in 130.9093 ms
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 successfully announced in 129.8303 ms
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 all the dispatchers started
2025-07-15 13:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 all the dispatchers started
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 caught stopping signal...
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 caught stopping signal...
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 All dispatchers stopped
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 successfully reported itself as stopped in 3.0631 ms
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b732799 has been stopped in total 278.2529 ms
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 All dispatchers stopped
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 successfully reported itself as stopped in 0.8782 ms
2025-07-15 13:31:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6244:4b014e78 has been stopped in total 279.9284 ms
2025-07-15 13:31:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:31:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:31:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 successfully announced in 104.3067 ms
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b successfully announced in 104.9516 ms
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 all the dispatchers started
2025-07-15 13:31:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b all the dispatchers started
2025-07-15 13:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b caught stopping signal...
2025-07-15 13:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 caught stopping signal...
2025-07-15 13:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b caught stopped signal...
2025-07-15 13:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 caught stopped signal...
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 All dispatchers stopped
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 successfully reported itself as stopped in 2.0445 ms
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:4a03d532 has been stopped in total 713.7961 ms
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b All dispatchers stopped
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b successfully reported itself as stopped in 0.8786 ms
2025-07-15 13:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16504:521d7f7b has been stopped in total 728.1689 ms
2025-07-15 13:33:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:33:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:33:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 successfully announced in 106.8694 ms
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 successfully announced in 107.3409 ms
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 all the dispatchers started
2025-07-15 13:33:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 all the dispatchers started
2025-07-15 13:35:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 caught stopping signal...
2025-07-15 13:35:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 caught stopping signal...
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 caught stopped signal...
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 caught stopped signal...
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 All dispatchers stopped
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 successfully reported itself as stopped in 1.6172 ms
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:0c0c9dc7 has been stopped in total 930.7915 ms
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 All dispatchers stopped
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 successfully reported itself as stopped in 6.1375 ms
2025-07-15 13:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30608:82a8f690 has been stopped in total 938.0572 ms
2025-07-15 13:36:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:36:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:36:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f successfully announced in 114.8708 ms
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca successfully announced in 116.4599 ms
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca all the dispatchers started
2025-07-15 13:36:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f all the dispatchers started
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca caught stopping signal...
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f caught stopping signal...
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca All dispatchers stopped
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca successfully reported itself as stopped in 2.1478 ms
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:0c85a5ca has been stopped in total 57.632 ms
2025-07-15 13:42:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f caught stopped signal...
2025-07-15 13:42:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f All dispatchers stopped
2025-07-15 13:42:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f successfully reported itself as stopped in 1.2721 ms
2025-07-15 13:42:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13584:edad353f has been stopped in total 937.7769 ms
2025-07-15 13:43:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:43:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:43:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 successfully announced in 116.8248 ms
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac successfully announced in 116.4984 ms
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 all the dispatchers started
2025-07-15 13:43:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac all the dispatchers started
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac caught stopping signal...
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 caught stopping signal...
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac All dispatchers stopped
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac successfully reported itself as stopped in 1.9192 ms
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:e9033eac has been stopped in total 360.4561 ms
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 All dispatchers stopped
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 successfully reported itself as stopped in 1.0779 ms
2025-07-15 13:43:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7360:85c01580 has been stopped in total 372.2066 ms
2025-07-15 13:44:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:44:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:b1747ebd successfully announced in 300.3399 ms
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:be00988c successfully announced in 300.0604 ms
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:be00988c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:b1747ebd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:b1747ebd all the dispatchers started
2025-07-15 13:44:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20788:be00988c all the dispatchers started
2025-07-15 13:49:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:49:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f successfully announced in 110.4249 ms
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 successfully announced in 110.4249 ms
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f all the dispatchers started
2025-07-15 13:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 all the dispatchers started
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 caught stopping signal...
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f caught stopping signal...
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 All dispatchers stopped
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f All dispatchers stopped
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 successfully reported itself as stopped in 1.8514 ms
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:886b5442 has been stopped in total 157.122 ms
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f successfully reported itself as stopped in 1.8814 ms
2025-07-15 13:51:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26764:ad90264f has been stopped in total 156.7663 ms
2025-07-15 13:51:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:51:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:51:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 successfully announced in 108.383 ms
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 successfully announced in 109.3656 ms
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 all the dispatchers started
2025-07-15 13:51:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 all the dispatchers started
2025-07-15 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 caught stopping signal...
2025-07-15 13:52:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 caught stopping signal...
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 caught stopped signal...
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 caught stopped signal...
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 All dispatchers stopped
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 successfully reported itself as stopped in 2.4981 ms
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:0a1ea0e2 has been stopped in total 980.9346 ms
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 All dispatchers stopped
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 successfully reported itself as stopped in 0.8258 ms
2025-07-15 13:52:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24580:6d74de59 has been stopped in total 984.9332 ms
2025-07-15 13:52:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:52:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:52:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:52:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:52:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:52:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:52:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:52:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 successfully announced in 125.6298 ms
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e successfully announced in 125.6427 ms
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 all the dispatchers started
2025-07-15 13:52:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e all the dispatchers started
2025-07-15 13:52:53 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-15 13:52:55 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object of type 'BootstrapBlazor.Components.SelectOption' does not have a property matching the name 'ValueChanged'."
System.InvalidOperationException: Object of type 'BootstrapBlazor.Components.SelectOption' does not have a property matching the name 'ValueChanged'.
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.ThrowForUnknownIncomingParameterName(Type targetType, String parameterName)
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.SetProperties(ParameterView& parameters, Object target)
   at Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(ParameterView parameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
2025-07-15 13:52:55 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name."
System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
2025-07-15 13:52:55 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"yOT9GNTx91JCvD3va5YK0UXtPysNdKWYvUGUZMzb1sA"'.
System.AggregateException: One or more errors occurred. (InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.)
 ---> System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 caught stopping signal...
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e caught stopping signal...
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e All dispatchers stopped
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 All dispatchers stopped
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e successfully reported itself as stopped in 1.8547 ms
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:327aec6e has been stopped in total 130.5286 ms
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 successfully reported itself as stopped in 0.8592 ms
2025-07-15 13:53:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25236:fbeeb549 has been stopped in total 131.3431 ms
2025-07-15 13:54:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:54:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:54:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d successfully announced in 147.2677 ms
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 successfully announced in 147.2875 ms
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 all the dispatchers started
2025-07-15 13:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d all the dispatchers started
2025-07-15 13:54:12 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name."
System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
2025-07-15 13:54:12 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"yf9obVGhA2oI0debZyGO3WS3la3jN4mVYGsYj3JA80M"'.
System.AggregateException: One or more errors occurred. (InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.)
 ---> System.InvalidOperationException: InvalidCharacterError: Failed to execute 'setAttribute' on 'Element': '@bind' is not a valid attribute name.
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InvokeRenderCompletedCallsAfterUpdateDisplayTask(Task updateDisplayTask, Int32[] updatedComponents)
   --- End of inner exception stack trace ---
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 caught stopping signal...
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d caught stopping signal...
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d caught stopped signal...
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 caught stopped signal...
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 All dispatchers stopped
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 successfully reported itself as stopped in 1.6271 ms
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:5f6a9ed6 has been stopped in total 648.4887 ms
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d All dispatchers stopped
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d successfully reported itself as stopped in 1.0596 ms
2025-07-15 13:55:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6616:6b519d0d has been stopped in total 679.469 ms
2025-07-15 13:55:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:55:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:55:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 successfully announced in 108.3822 ms
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb successfully announced in 108.3822 ms
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb all the dispatchers started
2025-07-15 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 all the dispatchers started
2025-07-15 13:55:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 caught stopping signal...
2025-07-15 13:55:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb caught stopping signal...
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb caught stopped signal...
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 caught stopped signal...
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 All dispatchers stopped
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb All dispatchers stopped
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 successfully reported itself as stopped in 2.8201 ms
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:68301147 has been stopped in total 518.8782 ms
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb successfully reported itself as stopped in 1.105 ms
2025-07-15 13:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13672:4f3291fb has been stopped in total 519.6561 ms
2025-07-15 13:55:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:55:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:55:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 successfully announced in 120.4482 ms
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 successfully announced in 120.9132 ms
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 all the dispatchers started
2025-07-15 13:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 all the dispatchers started
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 caught stopping signal...
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 caught stopping signal...
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 caught stopped signal...
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 caught stopped signal...
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 All dispatchers stopped
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 successfully reported itself as stopped in 1.6946 ms
2025-07-15 13:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:c7888cd8 has been stopped in total 517.0917 ms
2025-07-15 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 All dispatchers stopped
2025-07-15 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 successfully reported itself as stopped in 1.5147 ms
2025-07-15 13:58:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15820:f1e69579 has been stopped in total 650.575 ms
2025-07-15 13:59:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 13:59:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 13:59:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:59:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:59:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 13:59:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 13:59:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 successfully announced in 106.6949 ms
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 successfully announced in 115.0537 ms
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 all the dispatchers started
2025-07-15 13:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 all the dispatchers started
2025-07-15 13:59:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 caught stopping signal...
2025-07-15 13:59:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 caught stopping signal...
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 All dispatchers stopped
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 successfully reported itself as stopped in 1.5931 ms
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:02461236 has been stopped in total 457.4056 ms
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 All dispatchers stopped
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 successfully reported itself as stopped in 0.8474 ms
2025-07-15 13:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14816:3f69fa11 has been stopped in total 484.7205 ms
2025-07-15 14:00:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:00:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a successfully announced in 120.9377 ms
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb successfully announced in 121.0678 ms
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a all the dispatchers started
2025-07-15 14:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb all the dispatchers started
2025-07-15 14:00:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a caught stopping signal...
2025-07-15 14:00:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb caught stopping signal...
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a caught stopped signal...
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb caught stopped signal...
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb All dispatchers stopped
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb successfully reported itself as stopped in 2.7282 ms
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:af282ccb has been stopped in total 919.252 ms
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a All dispatchers stopped
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a successfully reported itself as stopped in 0.8588 ms
2025-07-15 14:00:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:715e8f5a has been stopped in total 937.1727 ms
2025-07-15 14:00:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:00:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 successfully announced in 117.1216 ms
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa successfully announced in 116.9542 ms
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 all the dispatchers started
2025-07-15 14:00:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa all the dispatchers started
2025-07-15 14:00:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 caught stopping signal...
2025-07-15 14:00:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa caught stopping signal...
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 All dispatchers stopped
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 successfully reported itself as stopped in 2.6263 ms
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:c5744e54 has been stopped in total 91.8004 ms
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa All dispatchers stopped
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa successfully reported itself as stopped in 0.7155 ms
2025-07-15 14:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:9a0df8fa has been stopped in total 97.9625 ms
2025-07-15 14:00:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:00:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f successfully announced in 141.0286 ms
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 successfully announced in 144.7648 ms
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f all the dispatchers started
2025-07-15 14:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 all the dispatchers started
2025-07-15 14:01:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 caught stopping signal...
2025-07-15 14:01:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f caught stopping signal...
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f All dispatchers stopped
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f successfully reported itself as stopped in 1.7059 ms
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:817bbe9f has been stopped in total 248.256 ms
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 All dispatchers stopped
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 successfully reported itself as stopped in 2.5677 ms
2025-07-15 14:01:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24712:c3e10594 has been stopped in total 369.802 ms
2025-07-15 14:01:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:01:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:01:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 successfully announced in 104.0129 ms
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b successfully announced in 110.7429 ms
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b all the dispatchers started
2025-07-15 14:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 all the dispatchers started
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 caught stopping signal...
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b caught stopping signal...
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b caught stopped signal...
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 caught stopped signal...
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b All dispatchers stopped
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 All dispatchers stopped
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b successfully reported itself as stopped in 2.3856 ms
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:cf74011b has been stopped in total 602.332 ms
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 successfully reported itself as stopped in 1.5674 ms
2025-07-15 14:03:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2424:5f5d88c2 has been stopped in total 603.6305 ms
2025-07-15 14:03:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:03:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db successfully announced in 103.6256 ms
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd successfully announced in 104.358 ms
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db all the dispatchers started
2025-07-15 14:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd all the dispatchers started
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db caught stopping signal...
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd caught stopping signal...
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd All dispatchers stopped
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db All dispatchers stopped
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd successfully reported itself as stopped in 1.5876 ms
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:38f7f2fd has been stopped in total 392.5588 ms
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db successfully reported itself as stopped in 0.7295 ms
2025-07-15 14:05:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13964:e2dd27db has been stopped in total 393.2051 ms
2025-07-15 14:05:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:05:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 successfully announced in 125.5236 ms
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 successfully announced in 125.5218 ms
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 all the dispatchers started
2025-07-15 14:05:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 all the dispatchers started
2025-07-15 14:07:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 caught stopping signal...
2025-07-15 14:07:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 caught stopping signal...
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 All dispatchers stopped
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 successfully reported itself as stopped in 1.7473 ms
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:45751479 has been stopped in total 461.6115 ms
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 caught stopped signal...
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 All dispatchers stopped
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 successfully reported itself as stopped in 2.2112 ms
2025-07-15 14:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18832:da7df503 has been stopped in total 545.5094 ms
2025-07-15 14:07:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:07:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:07:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 successfully announced in 106.256 ms
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d successfully announced in 106.2758 ms
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d all the dispatchers started
2025-07-15 14:07:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 all the dispatchers started
2025-07-15 14:08:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 caught stopping signal...
2025-07-15 14:08:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d caught stopping signal...
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d caught stopped signal...
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 caught stopped signal...
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 All dispatchers stopped
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 successfully reported itself as stopped in 4.2806 ms
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:ddb25976 has been stopped in total 881.5317 ms
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d All dispatchers stopped
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d successfully reported itself as stopped in 0.9083 ms
2025-07-15 14:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1396:10377c3d has been stopped in total 888.0519 ms
2025-07-15 14:09:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:09:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce successfully announced in 104.07 ms
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 successfully announced in 104.7501 ms
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 all the dispatchers started
2025-07-15 14:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce all the dispatchers started
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 caught stopping signal...
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce caught stopping signal...
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 All dispatchers stopped
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 successfully reported itself as stopped in 1.544 ms
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:75bf5d81 has been stopped in total 313.7894 ms
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce All dispatchers stopped
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce successfully reported itself as stopped in 0.7884 ms
2025-07-15 14:12:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24548:468046ce has been stopped in total 415.5582 ms
2025-07-15 14:12:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:12:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d successfully announced in 116.2247 ms
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 successfully announced in 116.9659 ms
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d all the dispatchers started
2025-07-15 14:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 all the dispatchers started
2025-07-15 14:13:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d caught stopping signal...
2025-07-15 14:13:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 caught stopping signal...
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d All dispatchers stopped
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d successfully reported itself as stopped in 1.5476 ms
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:1e122e5d has been stopped in total 217.2405 ms
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 All dispatchers stopped
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 successfully reported itself as stopped in 0.7602 ms
2025-07-15 14:13:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11384:376cd6d5 has been stopped in total 224.1138 ms
2025-07-15 14:13:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:13:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c successfully announced in 103.9418 ms
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 successfully announced in 104.1217 ms
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c all the dispatchers started
2025-07-15 14:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 all the dispatchers started
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c caught stopping signal...
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 caught stopping signal...
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c All dispatchers stopped
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 All dispatchers stopped
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 successfully reported itself as stopped in 1.7697 ms
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:70280e17 has been stopped in total 280.8302 ms
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c successfully reported itself as stopped in 1.8166 ms
2025-07-15 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18152:e926ee6c has been stopped in total 281.0976 ms
2025-07-15 14:16:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:16:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:16:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 successfully announced in 107.5754 ms
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 successfully announced in 107.5666 ms
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 all the dispatchers started
2025-07-15 14:16:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 all the dispatchers started
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 caught stopping signal...
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 caught stopping signal...
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 All dispatchers stopped
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 All dispatchers stopped
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 successfully reported itself as stopped in 2.242 ms
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:37d1bce8 has been stopped in total 470.1208 ms
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 successfully reported itself as stopped in 0.7027 ms
2025-07-15 14:20:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25852:2fe7d626 has been stopped in total 471.1753 ms
2025-07-15 14:21:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:21:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 successfully announced in 115.3398 ms
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 successfully announced in 105.9237 ms
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 all the dispatchers started
2025-07-15 14:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 all the dispatchers started
2025-07-15 14:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 caught stopping signal...
2025-07-15 14:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 caught stopping signal...
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 caught stopped signal...
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 caught stopped signal...
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 All dispatchers stopped
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 All dispatchers stopped
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 successfully reported itself as stopped in 1.7957 ms
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 successfully reported itself as stopped in 1.8345 ms
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:a19f9cd6 has been stopped in total 651.9153 ms
2025-07-15 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16452:1a2891c9 has been stopped in total 651.7167 ms
2025-07-15 14:21:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:21:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:21:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 successfully announced in 79.3108 ms
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 successfully announced in 134.7606 ms
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 all the dispatchers started
2025-07-15 14:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 all the dispatchers started
2025-07-15 14:25:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 caught stopping signal...
2025-07-15 14:25:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 caught stopping signal...
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 caught stopped signal...
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 caught stopped signal...
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 All dispatchers stopped
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 successfully reported itself as stopped in 1.8455 ms
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:26a59fd8 has been stopped in total 756.9732 ms
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 All dispatchers stopped
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 successfully reported itself as stopped in 0.7404 ms
2025-07-15 14:25:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:45c4b457 has been stopped in total 804.5054 ms
2025-07-15 14:25:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:25:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:25:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac successfully announced in 111.4373 ms
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a successfully announced in 111.4387 ms
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a all the dispatchers started
2025-07-15 14:25:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac all the dispatchers started
2025-07-15 14:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a caught stopping signal...
2025-07-15 14:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac caught stopping signal...
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac caught stopped signal...
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a caught stopped signal...
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a All dispatchers stopped
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac All dispatchers stopped
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a successfully reported itself as stopped in 2.1156 ms
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:c8dd643a has been stopped in total 776.8593 ms
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac successfully reported itself as stopped in 0.7804 ms
2025-07-15 14:34:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18344:2ec275ac has been stopped in total 776.9406 ms
2025-07-15 14:34:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:34:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:34:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 successfully announced in 109.569 ms
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb successfully announced in 109.5796 ms
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 all the dispatchers started
2025-07-15 14:34:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb all the dispatchers started
2025-07-15 14:35:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 caught stopping signal...
2025-07-15 14:35:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb caught stopping signal...
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 All dispatchers stopped
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 successfully reported itself as stopped in 2.0364 ms
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:611353a5 has been stopped in total 123.7213 ms
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb All dispatchers stopped
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb successfully reported itself as stopped in 0.964 ms
2025-07-15 14:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30652:2ad2eabb has been stopped in total 172.9022 ms
2025-07-15 14:35:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:35:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:35:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 successfully announced in 136.9839 ms
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd successfully announced in 137.0015 ms
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 all the dispatchers started
2025-07-15 14:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd all the dispatchers started
2025-07-15 14:43:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 caught stopping signal...
2025-07-15 14:43:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd caught stopping signal...
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd All dispatchers stopped
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd successfully reported itself as stopped in 2.0822 ms
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:8697e6fd has been stopped in total 466.4477 ms
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 caught stopped signal...
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 All dispatchers stopped
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 successfully reported itself as stopped in 1.47 ms
2025-07-15 14:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19528:0b2ee2e6 has been stopped in total 528.5739 ms
2025-07-15 14:44:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:44:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:44:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 successfully announced in 110.382 ms
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c successfully announced in 110.3967 ms
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c all the dispatchers started
2025-07-15 14:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 all the dispatchers started
2025-07-15 14:56:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 caught stopping signal...
2025-07-15 14:56:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c caught stopping signal...
2025-07-15 14:56:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c caught stopped signal...
2025-07-15 14:56:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 caught stopped signal...
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 All dispatchers stopped
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c All dispatchers stopped
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 successfully reported itself as stopped in 7.3899 ms
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:e99fd363 has been stopped in total 956.1549 ms
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c successfully reported itself as stopped in 24.1502 ms
2025-07-15 14:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4004:d991c51c has been stopped in total 973.5905 ms
2025-07-15 14:56:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 14:56:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 14:56:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 successfully announced in 124.7933 ms
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 successfully announced in 124.4288 ms
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 all the dispatchers started
2025-07-15 14:56:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 all the dispatchers started
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 caught stopping signal...
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 caught stopping signal...
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 caught stopped signal...
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 caught stopped signal...
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 All dispatchers stopped
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 successfully reported itself as stopped in 2.4442 ms
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:de356508 has been stopped in total 744.8552 ms
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 All dispatchers stopped
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 successfully reported itself as stopped in 0.8071 ms
2025-07-15 15:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15424:e07c4691 has been stopped in total 812.9396 ms
2025-07-15 15:02:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:02:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:02:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 successfully announced in 108.0286 ms
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 successfully announced in 108.0917 ms
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 all the dispatchers started
2025-07-15 15:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 all the dispatchers started
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 caught stopping signal...
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 caught stopping signal...
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 caught stopped signal...
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 caught stopped signal...
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 All dispatchers stopped
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 successfully reported itself as stopped in 2.0789 ms
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:bc1f89f4 has been stopped in total 704.0044 ms
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 All dispatchers stopped
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 successfully reported itself as stopped in 0.8577 ms
2025-07-15 15:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:a2ad9f50 has been stopped in total 756.4071 ms
2025-07-15 15:04:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:04:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:04:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b successfully announced in 122.6532 ms
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f successfully announced in 122.6697 ms
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f all the dispatchers started
2025-07-15 15:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b all the dispatchers started
2025-07-15 15:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b caught stopping signal...
2025-07-15 15:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f caught stopping signal...
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f caught stopped signal...
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b caught stopped signal...
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b All dispatchers stopped
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b successfully reported itself as stopped in 3.2393 ms
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:9c7f6e7b has been stopped in total 646.9018 ms
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f All dispatchers stopped
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f successfully reported itself as stopped in 1.3032 ms
2025-07-15 15:10:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17816:5fe6633f has been stopped in total 701.5792 ms
2025-07-15 15:10:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:10:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:10:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab successfully announced in 110.4278 ms
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 successfully announced in 111.0866 ms
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 all the dispatchers started
2025-07-15 15:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab all the dispatchers started
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 caught stopping signal...
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab caught stopping signal...
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab All dispatchers stopped
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab successfully reported itself as stopped in 1.8627 ms
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:8053daab has been stopped in total 102.5854 ms
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 All dispatchers stopped
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 successfully reported itself as stopped in 0.8526 ms
2025-07-15 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3908:ffb1db94 has been stopped in total 150.7961 ms
2025-07-15 15:18:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:18:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:18:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 successfully announced in 95.7956 ms
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e successfully announced in 114.8195 ms
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e all the dispatchers started
2025-07-15 15:18:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 all the dispatchers started
2025-07-15 15:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e caught stopping signal...
2025-07-15 15:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 caught stopping signal...
2025-07-15 15:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 caught stopped signal...
2025-07-15 15:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e caught stopped signal...
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 All dispatchers stopped
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 successfully reported itself as stopped in 2.0104 ms
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:29074b59 has been stopped in total 856.8385 ms
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e All dispatchers stopped
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e successfully reported itself as stopped in 1.5725 ms
2025-07-15 15:18:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28716:9288b56e has been stopped in total 900.9206 ms
2025-07-15 15:18:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:18:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:18:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e successfully announced in 130.6972 ms
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 successfully announced in 130.0285 ms
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 all the dispatchers started
2025-07-15 15:18:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e all the dispatchers started
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 caught stopping signal...
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e caught stopping signal...
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e All dispatchers stopped
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 All dispatchers stopped
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 successfully reported itself as stopped in 2.5662 ms
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:02dcd555 has been stopped in total 311.7607 ms
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e successfully reported itself as stopped in 2.6084 ms
2025-07-15 15:25:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19836:b3fd8c9e has been stopped in total 311.5287 ms
2025-07-15 15:25:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:25:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 successfully announced in 134.5231 ms
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 successfully announced in 137.8339 ms
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 all the dispatchers started
2025-07-15 15:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 all the dispatchers started
2025-07-15 15:29:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 caught stopping signal...
2025-07-15 15:29:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 caught stopping signal...
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 All dispatchers stopped
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 successfully reported itself as stopped in 1.6942 ms
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:bff4fdb8 has been stopped in total 437.7998 ms
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 All dispatchers stopped
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 successfully reported itself as stopped in 0.7287 ms
2025-07-15 15:29:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20284:b82c2075 has been stopped in total 451.3699 ms
2025-07-15 15:32:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:32:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:32:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 successfully announced in 113.048 ms
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 successfully announced in 113.4382 ms
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 all the dispatchers started
2025-07-15 15:32:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 all the dispatchers started
2025-07-15 15:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 caught stopping signal...
2025-07-15 15:38:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 caught stopping signal...
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 All dispatchers stopped
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 All dispatchers stopped
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 successfully reported itself as stopped in 2.3991 ms
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:c9836fe0 has been stopped in total 495.3743 ms
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 successfully reported itself as stopped in 1.1732 ms
2025-07-15 15:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19208:514cffa1 has been stopped in total 496.6853 ms
2025-07-15 15:39:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:39:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:39:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:39:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:39:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:39:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:39:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:39:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 successfully announced in 117.1139 ms
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef successfully announced in 89.2088 ms
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 all the dispatchers started
2025-07-15 15:39:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef all the dispatchers started
2025-07-15 15:39:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 caught stopping signal...
2025-07-15 15:39:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef caught stopping signal...
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 All dispatchers stopped
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 successfully reported itself as stopped in 2.8762 ms
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:36d32f10 has been stopped in total 176.649 ms
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef All dispatchers stopped
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef successfully reported itself as stopped in 1.1534 ms
2025-07-15 15:39:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17248:ef41afef has been stopped in total 178.4378 ms
2025-07-15 15:40:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:40:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 successfully announced in 112.2529 ms
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 successfully announced in 112.2159 ms
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 all the dispatchers started
2025-07-15 15:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 all the dispatchers started
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 caught stopping signal...
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 caught stopping signal...
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 caught stopped signal...
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 caught stopped signal...
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 All dispatchers stopped
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 successfully reported itself as stopped in 2.2933 ms
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:d5723346 has been stopped in total 758.7829 ms
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 All dispatchers stopped
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 successfully reported itself as stopped in 1.0808 ms
2025-07-15 15:42:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12812:da853063 has been stopped in total 781.5192 ms
2025-07-15 15:42:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:42:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:42:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f successfully announced in 99.3281 ms
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d successfully announced in 98.6414 ms
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f all the dispatchers started
2025-07-15 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d all the dispatchers started
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d caught stopping signal...
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f caught stopping signal...
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d All dispatchers stopped
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d successfully reported itself as stopped in 2.6446 ms
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:80755d5d has been stopped in total 354.5919 ms
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f All dispatchers stopped
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f successfully reported itself as stopped in 1.1702 ms
2025-07-15 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20188:b861a94f has been stopped in total 403.1863 ms
2025-07-15 15:48:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:48:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:48:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 successfully announced in 113.2642 ms
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 successfully announced in 113.2656 ms
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 all the dispatchers started
2025-07-15 15:48:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 all the dispatchers started
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 caught stopping signal...
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 caught stopping signal...
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 All dispatchers stopped
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 All dispatchers stopped
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 successfully reported itself as stopped in 2.6791 ms
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:ae29c0f6 has been stopped in total 289.2515 ms
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 successfully reported itself as stopped in 2.9678 ms
2025-07-15 15:49:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1320:611f2b19 has been stopped in total 289.2735 ms
2025-07-15 15:49:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:49:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:49:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c successfully announced in 101.8006 ms
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b successfully announced in 103.9814 ms
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c all the dispatchers started
2025-07-15 15:49:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b all the dispatchers started
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b caught stopping signal...
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c caught stopping signal...
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c caught stopped signal...
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b caught stopped signal...
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c All dispatchers stopped
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c successfully reported itself as stopped in 1.7865 ms
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:298b9c3c has been stopped in total 680.0425 ms
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b All dispatchers stopped
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b successfully reported itself as stopped in 0.7716 ms
2025-07-15 15:52:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12340:2497ad7b has been stopped in total 721.2996 ms
2025-07-15 15:52:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:52:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:52:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 successfully announced in 120.5915 ms
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b successfully announced in 120.6021 ms
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b all the dispatchers started
2025-07-15 15:52:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 all the dispatchers started
2025-07-15 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 caught stopping signal...
2025-07-15 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b caught stopping signal...
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b caught stopped signal...
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 caught stopped signal...
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b All dispatchers stopped
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b successfully reported itself as stopped in 1.5223 ms
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:baa6324b has been stopped in total 892.9444 ms
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 All dispatchers stopped
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 successfully reported itself as stopped in 0.6371 ms
2025-07-15 15:53:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26292:eb77fcd5 has been stopped in total 896.6769 ms
2025-07-15 15:53:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:53:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:53:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 successfully announced in 103.9631 ms
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e successfully announced in 120.9648 ms
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 all the dispatchers started
2025-07-15 15:53:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e all the dispatchers started
2025-07-15 15:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e caught stopping signal...
2025-07-15 15:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 caught stopping signal...
2025-07-15 15:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 caught stopped signal...
2025-07-15 15:59:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e caught stopped signal...
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e All dispatchers stopped
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e successfully reported itself as stopped in 1.9166 ms
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:b9b7c02e has been stopped in total 931.9691 ms
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 All dispatchers stopped
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 successfully reported itself as stopped in 0.9559 ms
2025-07-15 15:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4364:44ddb1a5 has been stopped in total 988.0369 ms
2025-07-15 15:59:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 15:59:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 15:59:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed successfully announced in 80.6625 ms
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 successfully announced in 108.5772 ms
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 all the dispatchers started
2025-07-15 15:59:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed all the dispatchers started
2025-07-15 16:00:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 caught stopping signal...
2025-07-15 16:00:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed caught stopping signal...
2025-07-15 16:00:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed caught stopped signal...
2025-07-15 16:00:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 caught stopped signal...
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 All dispatchers stopped
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed All dispatchers stopped
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 successfully reported itself as stopped in 1.696 ms
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:b46c8008 has been stopped in total 921.2944 ms
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed successfully reported itself as stopped in 0.7287 ms
2025-07-15 16:00:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29784:93ebb7ed has been stopped in total 922.6588 ms
2025-07-15 16:00:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 16:00:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 16:00:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a successfully announced in 111.0819 ms
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae successfully announced in 111.1379 ms
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a all the dispatchers started
2025-07-15 16:00:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae all the dispatchers started
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a caught stopping signal...
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae caught stopping signal...
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a All dispatchers stopped
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a successfully reported itself as stopped in 2.5842 ms
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:ee081e5a has been stopped in total 480.1866 ms
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae All dispatchers stopped
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae successfully reported itself as stopped in 1.2453 ms
2025-07-15 16:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19284:b2e13aae has been stopped in total 494.0377 ms
2025-07-15 16:06:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 16:06:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 16:07:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f successfully announced in 124.1045 ms
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a successfully announced in 123.3072 ms
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a all the dispatchers started
2025-07-15 16:07:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f all the dispatchers started
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a caught stopping signal...
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f caught stopping signal...
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f caught stopped signal...
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a caught stopped signal...
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a All dispatchers stopped
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a successfully reported itself as stopped in 10.974 ms
2025-07-15 16:47:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:1c1b905a has been stopped in total 735.342 ms
2025-07-15 16:48:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f All dispatchers stopped
2025-07-15 16:48:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f successfully reported itself as stopped in 1.8492 ms
2025-07-15 16:48:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25720:c1658b6f has been stopped in total 776.5757 ms
2025-07-15 17:04:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 17:04:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:04:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf successfully announced in 125.6324 ms
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 successfully announced in 125.6423 ms
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 all the dispatchers started
2025-07-15 17:04:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf all the dispatchers started
2025-07-15 17:06:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf caught stopping signal...
2025-07-15 17:06:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 caught stopping signal...
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf All dispatchers stopped
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf successfully reported itself as stopped in 1.5791 ms
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:69420edf has been stopped in total 208.571 ms
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 All dispatchers stopped
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 successfully reported itself as stopped in 0.721 ms
2025-07-15 17:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31412:ec66bf75 has been stopped in total 269.5465 ms
2025-07-15 17:06:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 17:06:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 17:06:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:06:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:06:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:06:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:06:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:06:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:fcb66e14 successfully announced in 268.2179 ms
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:90157ee2 successfully announced in 268.6099 ms
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:fcb66e14 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:90157ee2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:90157ee2 all the dispatchers started
2025-07-15 17:06:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21140:fcb66e14 all the dispatchers started
2025-07-15 17:09:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 17:09:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:09:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:9a5bb52e successfully announced in 228.1834 ms
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:55ffaa39 successfully announced in 228.1607 ms
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:9a5bb52e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:55ffaa39 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:55ffaa39 all the dispatchers started
2025-07-15 17:09:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14412:9a5bb52e all the dispatchers started
2025-07-15 17:17:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-15 17:17:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-15 17:17:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:17:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:17:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:17:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-15 17:17:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-15 17:17:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 successfully announced in 119.7121 ms
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 successfully announced in 124.2016 ms
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-15 17:17:27 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 all the dispatchers started
2025-07-15 17:17:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 all the dispatchers started
2025-07-15 17:17:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 caught stopping signal...
2025-07-15 17:17:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 caught stopping signal...
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 All dispatchers stopped
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 successfully reported itself as stopped in 1.832 ms
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:090bedf9 has been stopped in total 409.2725 ms
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 All dispatchers stopped
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 successfully reported itself as stopped in 0.6675 ms
2025-07-15 17:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16536:6217a663 has been stopped in total 411.6615 ms
