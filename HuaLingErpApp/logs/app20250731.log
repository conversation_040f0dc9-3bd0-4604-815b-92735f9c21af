2025-07-31 08:08:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:08:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:08:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc successfully announced in 107.6546 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 successfully announced in 107.6901 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc all the dispatchers started
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 all the dispatchers started
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 caught stopping signal...
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc caught stopping signal...
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc All dispatchers stopped
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc successfully reported itself as stopped in 2.6934 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc has been stopped in total 122.9174 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 All dispatchers stopped
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 successfully reported itself as stopped in 0.8225 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 has been stopped in total 149.3254 ms
2025-07-31 08:29:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:29:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:29:34 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 successfully announced in 97.1832 ms
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 successfully announced in 98.0787 ms
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 all the dispatchers started
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 all the dispatchers started
2025-07-31 08:31:26 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 192
ClientConnectionId:c2d222bd-9599-4f8e-a5a9-c1f30608a49a
Error Number:8114,State:5,Class:16
2025-07-31 08:32:14 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 192
ClientConnectionId:d82df15b-5b1b-47ee-b101-3c6becf6a967
Error Number:8114,State:5,Class:16
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 caught stopping signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 caught stopping signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 caught stopped signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 caught stopped signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 All dispatchers stopped
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 All dispatchers stopped
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 successfully reported itself as stopped in 1.8536 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 has been stopped in total 931.119 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 successfully reported itself as stopped in 0.6093 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 has been stopped in total 932.3395 ms
