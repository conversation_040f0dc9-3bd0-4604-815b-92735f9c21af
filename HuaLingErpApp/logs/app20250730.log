2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 08:09:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 08:09:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 08:09:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 08:09:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 08:09:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully announced in 191.3047 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully announced in 167.4274 ms
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 all the dispatchers started
2025-07-30 08:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad all the dispatchers started
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopping signal...
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad All dispatchers stopped
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad successfully reported itself as stopped in 2.4706 ms
2025-07-30 09:35:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:8087e0ad has been stopped in total 70.9279 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 caught stopped signal...
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 All dispatchers stopped
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 successfully reported itself as stopped in 0.8874 ms
2025-07-30 09:35:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14296:12f45e65 has been stopped in total 883.6532 ms
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 09:35:45 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 09:35:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully announced in 107.8579 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully announced in 107.8275 ms
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 all the dispatchers started
2025-07-30 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b all the dispatchers started
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopping signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 caught stopped signal...
2025-07-30 09:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b caught stopped signal...
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 All dispatchers stopped
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 successfully reported itself as stopped in 2.401 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:95042561 has been stopped in total 975.3287 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b successfully reported itself as stopped in 1.6788 ms
2025-07-30 09:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2152:499ac32b has been stopped in total 976.2762 ms
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:04:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:04:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:04:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:04:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully announced in 104.3459 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully announced in 103.6919 ms
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 all the dispatchers started
2025-07-30 10:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a all the dispatchers started
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 caught stopping signal...
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 All dispatchers stopped
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 successfully reported itself as stopped in 1.9474 ms
2025-07-30 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:c2d5a295 has been stopped in total 44.872 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a caught stopped signal...
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a All dispatchers stopped
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a successfully reported itself as stopped in 0.9229 ms
2025-07-30 10:13:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16552:7d84a50a has been stopped in total 959.3708 ms
2025-07-30 10:14:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:14:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:14:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:14:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:14:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:14:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 successfully announced in 104.6937 ms
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 successfully announced in 104.8879 ms
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 all the dispatchers started
2025-07-30 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 all the dispatchers started
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 caught stopping signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 caught stopping signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 caught stopped signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 caught stopped signal...
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 All dispatchers stopped
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 All dispatchers stopped
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 successfully reported itself as stopped in 1.8503 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:8ee25579 has been stopped in total 569.6725 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 successfully reported itself as stopped in 1.3604 ms
2025-07-30 10:17:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17184:279c2a10 has been stopped in total 570.8296 ms
2025-07-30 10:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:18:08 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:18:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 successfully announced in 116.7541 ms
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 successfully announced in 117.2033 ms
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 all the dispatchers started
2025-07-30 10:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 all the dispatchers started
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 caught stopping signal...
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 caught stopping signal...
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 All dispatchers stopped
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 successfully reported itself as stopped in 1.8869 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:5504e552 has been stopped in total 374.1453 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 All dispatchers stopped
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 successfully reported itself as stopped in 0.8086 ms
2025-07-30 10:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20372:47401c07 has been stopped in total 600.71 ms
2025-07-30 10:24:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:24:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:24:34 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:24:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:24:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:24:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 successfully announced in 114.7679 ms
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a successfully announced in 114.6675 ms
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a all the dispatchers started
2025-07-30 10:24:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 all the dispatchers started
2025-07-30 10:25:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a caught stopping signal...
2025-07-30 10:25:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 caught stopping signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 caught stopped signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a caught stopped signal...
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a All dispatchers stopped
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 All dispatchers stopped
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 successfully reported itself as stopped in 2.0335 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:c6580925 has been stopped in total 743.7296 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a successfully reported itself as stopped in 1.4787 ms
2025-07-30 10:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11976:9404474a has been stopped in total 745.4231 ms
2025-07-30 10:25:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:25:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:25:53 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:25:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:25:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 successfully announced in 102.7118 ms
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db successfully announced in 102.7202 ms
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 all the dispatchers started
2025-07-30 10:25:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db all the dispatchers started
2025-07-30 10:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 caught stopping signal...
2025-07-30 10:26:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db caught stopping signal...
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db All dispatchers stopped
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 All dispatchers stopped
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db successfully reported itself as stopped in 0.8485 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:c2fcb0db has been stopped in total 460.9908 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 successfully reported itself as stopped in 2.2068 ms
2025-07-30 10:26:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12688:f5051885 has been stopped in total 465.539 ms
2025-07-30 10:26:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 10:26:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 10:26:37 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 10:26:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 10:26:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 10:26:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 10:26:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 successfully announced in 102.0186 ms
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa successfully announced in 102.02 ms
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 all the dispatchers started
2025-07-30 10:26:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa all the dispatchers started
2025-07-30 13:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa caught stopping signal...
2025-07-30 13:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 caught stopping signal...
2025-07-30 13:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 All dispatchers stopped
2025-07-30 13:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 successfully reported itself as stopped in 3.4137 ms
2025-07-30 13:16:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:def299e4 has been stopped in total 25.4707 ms
2025-07-30 13:16:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa All dispatchers stopped
2025-07-30 13:16:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa successfully reported itself as stopped in 1.4428 ms
2025-07-30 13:16:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19124:ab9c4faa has been stopped in total 364.9805 ms
2025-07-30 13:17:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 13:17:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 13:17:01 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 13:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 13:17:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:17:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:17:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:17:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 successfully announced in 113.5159 ms
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 successfully announced in 113.8401 ms
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 all the dispatchers started
2025-07-30 13:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 all the dispatchers started
2025-07-30 13:18:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 caught stopping signal...
2025-07-30 13:18:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 caught stopping signal...
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 caught stopped signal...
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 caught stopped signal...
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 All dispatchers stopped
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 successfully reported itself as stopped in 3.6687 ms
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:f8dd45b5 has been stopped in total 554.624 ms
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 All dispatchers stopped
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 successfully reported itself as stopped in 2.8879 ms
2025-07-30 13:18:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:9bae1995 has been stopped in total 572.4296 ms
2025-07-30 13:18:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 13:18:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 13:18:18 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 13:18:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:18:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f successfully announced in 120.9674 ms
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 successfully announced in 120.9417 ms
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 all the dispatchers started
2025-07-30 13:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f all the dispatchers started
2025-07-30 13:49:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 caught stopping signal...
2025-07-30 13:49:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f caught stopping signal...
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f caught stopped signal...
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 caught stopped signal...
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 All dispatchers stopped
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f All dispatchers stopped
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 successfully reported itself as stopped in 3.2082 ms
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:600981e1 has been stopped in total 817.3998 ms
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f successfully reported itself as stopped in 4.0916 ms
2025-07-30 13:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14236:fd439f1f has been stopped in total 817.8021 ms
2025-07-30 13:49:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 13:49:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 13:49:34 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 13:49:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:49:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 successfully announced in 103.5824 ms
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb successfully announced in 104.2243 ms
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 all the dispatchers started
2025-07-30 13:49:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb all the dispatchers started
2025-07-30 13:50:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 caught stopping signal...
2025-07-30 13:50:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb caught stopping signal...
2025-07-30 13:50:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb caught stopped signal...
2025-07-30 13:50:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 caught stopped signal...
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb All dispatchers stopped
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb successfully reported itself as stopped in 1.5989 ms
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:b9496feb has been stopped in total 768.5145 ms
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 All dispatchers stopped
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 successfully reported itself as stopped in 1.2904 ms
2025-07-30 13:50:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20804:163e4408 has been stopped in total 894.1792 ms
2025-07-30 13:51:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 13:51:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 13:51:09 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 13:51:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 13:51:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 13:51:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 13:51:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 successfully announced in 101.4598 ms
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae successfully announced in 101.2389 ms
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 all the dispatchers started
2025-07-30 13:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae all the dispatchers started
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae caught stopping signal...
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 caught stopping signal...
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 caught stopped signal...
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae caught stopped signal...
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae All dispatchers stopped
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae successfully reported itself as stopped in 1.947 ms
2025-07-30 14:18:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:dc8f88ae has been stopped in total 575.3392 ms
2025-07-30 14:18:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 All dispatchers stopped
2025-07-30 14:18:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 successfully reported itself as stopped in 0.939 ms
2025-07-30 14:18:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19016:3c4026f0 has been stopped in total 805.5822 ms
2025-07-30 14:18:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 14:18:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 14:18:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 14:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 14:18:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b successfully announced in 102.3692 ms
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be successfully announced in 102.4095 ms
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b all the dispatchers started
2025-07-30 14:18:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be all the dispatchers started
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b caught stopping signal...
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be caught stopping signal...
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b All dispatchers stopped
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be All dispatchers stopped
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b successfully reported itself as stopped in 2.9235 ms
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:bd385e8b has been stopped in total 258.4584 ms
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be successfully reported itself as stopped in 2.8553 ms
2025-07-30 15:02:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21832:592a44be has been stopped in total 258.0766 ms
2025-07-30 16:07:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:07:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:07:31 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:07:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:07:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:07:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:07:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 successfully announced in 103.0035 ms
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 successfully announced in 102.9888 ms
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 all the dispatchers started
2025-07-30 16:07:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 all the dispatchers started
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 caught stopping signal...
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 caught stopping signal...
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 caught stopped signal...
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 caught stopped signal...
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 All dispatchers stopped
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 successfully reported itself as stopped in 1.7928 ms
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:84c2db59 has been stopped in total 537.9949 ms
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 All dispatchers stopped
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 successfully reported itself as stopped in 0.721 ms
2025-07-30 16:12:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16908:48e26a86 has been stopped in total 595.1388 ms
2025-07-30 16:28:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:28:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:28:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:28:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:28:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:28:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:28:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:28:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:28:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 successfully announced in 107.5857 ms
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 successfully announced in 107.5069 ms
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 all the dispatchers started
2025-07-30 16:28:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 all the dispatchers started
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 caught stopping signal...
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 caught stopping signal...
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 caught stopped signal...
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 caught stopped signal...
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 All dispatchers stopped
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 All dispatchers stopped
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 successfully reported itself as stopped in 2.6417 ms
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 successfully reported itself as stopped in 2.6549 ms
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:96143be4 has been stopped in total 525.6926 ms
2025-07-30 16:39:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4528:38cf02a1 has been stopped in total 527.7561 ms
2025-07-30 16:39:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:39:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:39:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:39:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:39:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 successfully announced in 102.537 ms
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad successfully announced in 102.5282 ms
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad all the dispatchers started
2025-07-30 16:39:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 all the dispatchers started
2025-07-30 16:40:06 [Error] HuaLingErpApp.Controller.MatlTranAmtController: Failed to retrieve material transaction amounts summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranAmtController.GetSummary() in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranAmtController.cs:line 133
ClientConnectionId:9419a82c-1a00-46a6-8b18-4072950a3312
Error Number:8114,State:5,Class:16
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad caught stopping signal...
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 caught stopping signal...
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 caught stopped signal...
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad caught stopped signal...
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 All dispatchers stopped
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 successfully reported itself as stopped in 1.4648 ms
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:48552877 has been stopped in total 698.3307 ms
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad All dispatchers stopped
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad successfully reported itself as stopped in 0.8233 ms
2025-07-30 16:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20768:c905ecad has been stopped in total 769.0652 ms
2025-07-30 16:41:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:41:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:41:17 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:41:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:41:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc successfully announced in 111.1061 ms
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 successfully announced in 110.2153 ms
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc all the dispatchers started
2025-07-30 16:41:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 all the dispatchers started
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc caught stopping signal...
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 caught stopping signal...
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 caught stopped signal...
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc caught stopped signal...
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 All dispatchers stopped
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 successfully reported itself as stopped in 1.474 ms
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:402f8ac6 has been stopped in total 754.367 ms
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc All dispatchers stopped
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc successfully reported itself as stopped in 0.846 ms
2025-07-30 16:43:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16976:4dbe0dbc has been stopped in total 947.2491 ms
2025-07-30 16:43:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:43:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:43:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:43:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:43:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 successfully announced in 101.7291 ms
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f successfully announced in 101.6738 ms
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 all the dispatchers started
2025-07-30 16:43:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f all the dispatchers started
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 caught stopping signal...
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f caught stopping signal...
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 caught stopped signal...
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f caught stopped signal...
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 All dispatchers stopped
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f All dispatchers stopped
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 successfully reported itself as stopped in 2.6146 ms
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:28b0c675 has been stopped in total 782.7477 ms
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f successfully reported itself as stopped in 0.7086 ms
2025-07-30 16:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18028:5382c96f has been stopped in total 782.6719 ms
2025-07-30 16:44:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:44:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:44:11 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:44:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:44:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 successfully announced in 105.6705 ms
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 successfully announced in 105.6961 ms
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 all the dispatchers started
2025-07-30 16:44:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 all the dispatchers started
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 caught stopping signal...
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 caught stopping signal...
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 All dispatchers stopped
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 successfully reported itself as stopped in 1.7118 ms
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:9be004e1 has been stopped in total 34.6623 ms
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 All dispatchers stopped
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 successfully reported itself as stopped in 0.6499 ms
2025-07-30 16:48:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4400:de630291 has been stopped in total 46.1281 ms
2025-07-30 16:49:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:49:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:49:01 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:49:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:49:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 successfully announced in 104.0481 ms
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec successfully announced in 103.9675 ms
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 all the dispatchers started
2025-07-30 16:49:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec all the dispatchers started
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 caught stopping signal...
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec caught stopping signal...
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec All dispatchers stopped
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec successfully reported itself as stopped in 1.7854 ms
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:a100c6ec has been stopped in total 9.814 ms
2025-07-30 16:51:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 caught stopped signal...
2025-07-30 16:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 All dispatchers stopped
2025-07-30 16:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 successfully reported itself as stopped in 0.9346 ms
2025-07-30 16:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13268:441190f6 has been stopped in total 924.0816 ms
2025-07-30 16:51:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-30 16:51:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-30 16:51:22 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-30 16:51:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-30 16:51:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 successfully announced in 103.4321 ms
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 successfully announced in 103.4277 ms
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 all the dispatchers started
2025-07-30 16:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 all the dispatchers started
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 caught stopping signal...
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 caught stopping signal...
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 All dispatchers stopped
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 successfully reported itself as stopped in 2.649 ms
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:9671ff16 has been stopped in total 163.7294 ms
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 All dispatchers stopped
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 successfully reported itself as stopped in 0.7855 ms
2025-07-30 17:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:185fc455 has been stopped in total 207.1831 ms
