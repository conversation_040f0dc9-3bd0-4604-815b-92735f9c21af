2025-07-11 08:18:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:18:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:18:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 successfully announced in 72.5401 ms
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab successfully announced in 72.6797 ms
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab all the dispatchers started
2025-07-11 08:18:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 all the dispatchers started
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab caught stopping signal...
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 caught stopping signal...
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 caught stopped signal...
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab caught stopped signal...
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab All dispatchers stopped
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab successfully reported itself as stopped in 2.4292 ms
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:55d6dbab has been stopped in total 869.8648 ms
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 All dispatchers stopped
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 successfully reported itself as stopped in 0.9918 ms
2025-07-11 08:20:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34740:0fdc9cd2 has been stopped in total 923.0238 ms
2025-07-11 08:20:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:20:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:20:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 successfully announced in 71.3423 ms
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab successfully announced in 71.8989 ms
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 all the dispatchers started
2025-07-11 08:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab all the dispatchers started
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 caught stopping signal...
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab caught stopping signal...
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 All dispatchers stopped
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab All dispatchers stopped
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab successfully reported itself as stopped in 37.8686 ms
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:6ce28bab has been stopped in total 395.693 ms
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 successfully reported itself as stopped in 1.1823 ms
2025-07-11 08:23:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38404:1cf325c7 has been stopped in total 396.9757 ms
2025-07-11 08:23:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:23:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e successfully announced in 79.6267 ms
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a successfully announced in 79.4655 ms
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e all the dispatchers started
2025-07-11 08:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a all the dispatchers started
2025-07-11 08:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a caught stopping signal...
2025-07-11 08:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e caught stopping signal...
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e caught stopped signal...
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a caught stopped signal...
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e All dispatchers stopped
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e successfully reported itself as stopped in 2.4376 ms
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:05c63b1e has been stopped in total 890.0319 ms
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a All dispatchers stopped
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a successfully reported itself as stopped in 1.0658 ms
2025-07-11 08:30:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31068:7e68cf7a has been stopped in total 1153.7896 ms
2025-07-11 08:30:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:30:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:30:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a successfully announced in 76.0781 ms
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 successfully announced in 76.3415 ms
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a all the dispatchers started
2025-07-11 08:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 all the dispatchers started
2025-07-11 08:32:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a caught stopping signal...
2025-07-11 08:32:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 caught stopping signal...
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a All dispatchers stopped
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 caught stopped signal...
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a successfully reported itself as stopped in 1.696 ms
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:43b4b46a has been stopped in total 501.3159 ms
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 All dispatchers stopped
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 successfully reported itself as stopped in 0.9654 ms
2025-07-11 08:32:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29416:6836db42 has been stopped in total 603.2205 ms
2025-07-11 08:32:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:32:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:32:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 successfully announced in 76.5419 ms
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe successfully announced in 76.7061 ms
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 all the dispatchers started
2025-07-11 08:32:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe all the dispatchers started
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 caught stopping signal...
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe caught stopping signal...
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 All dispatchers stopped
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe All dispatchers stopped
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 successfully reported itself as stopped in 2.0207 ms
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:e7825c47 has been stopped in total 387.494 ms
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe successfully reported itself as stopped in 8.6111 ms
2025-07-11 08:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25724:858c19fe has been stopped in total 394.0427 ms
2025-07-11 08:37:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:37:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba successfully announced in 70.6722 ms
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 successfully announced in 66.4197 ms
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba all the dispatchers started
2025-07-11 08:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 all the dispatchers started
2025-07-11 08:37:46 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:37:46 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:37:46 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"RErbxncqSuq1TxfmhhlsyZ1UnOiCxQ9XxIAp0Uuu9fI"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:38:20 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:38:20 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:38:20 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"rKwCTmhDtC7KZwDwC7_eE-tm_s3G_8QF8xt7LpZfbD4"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:39:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 caught stopping signal...
2025-07-11 08:39:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba caught stopping signal...
2025-07-11 08:39:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba caught stopped signal...
2025-07-11 08:39:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 caught stopped signal...
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba All dispatchers stopped
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba successfully reported itself as stopped in 2.5823 ms
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:a899fdba has been stopped in total 776.8347 ms
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 All dispatchers stopped
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 successfully reported itself as stopped in 0.9152 ms
2025-07-11 08:39:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26336:cdf4f439 has been stopped in total 841.2547 ms
2025-07-11 08:40:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:40:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:40:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a successfully announced in 76.3899 ms
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a successfully announced in 83.2529 ms
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a all the dispatchers started
2025-07-11 08:40:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a all the dispatchers started
2025-07-11 08:40:56 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:40:56 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:40:56 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"e6psewzBcIkZ7Hhzo7j4FbdWB3u5vLTu49-O65hGHCw"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:41:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a caught stopping signal...
2025-07-11 08:41:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a caught stopping signal...
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a caught stopped signal...
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a caught stopped signal...
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a All dispatchers stopped
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a successfully reported itself as stopped in 3.635 ms
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:060a324a has been stopped in total 580.9067 ms
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a All dispatchers stopped
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a successfully reported itself as stopped in 2.5801 ms
2025-07-11 08:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34596:11af208a has been stopped in total 853.8912 ms
2025-07-11 08:42:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:42:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:42:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b successfully announced in 77.8379 ms
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 successfully announced in 79.3563 ms
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 all the dispatchers started
2025-07-11 08:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b all the dispatchers started
2025-07-11 08:42:55 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:42:55 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:42:55 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"c7FfIDnn0tvyOHue890-_Z2lMAk_EerFiFpCVAnwesM"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 102
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 caught stopping signal...
2025-07-11 08:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b caught stopping signal...
2025-07-11 08:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b caught stopped signal...
2025-07-11 08:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 caught stopped signal...
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 All dispatchers stopped
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b All dispatchers stopped
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 successfully reported itself as stopped in 1.8616 ms
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b successfully reported itself as stopped in 2.7146 ms
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:cde37e3b has been stopped in total 798.4158 ms
2025-07-11 08:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:ed79a0f6 has been stopped in total 798.6796 ms
2025-07-11 08:46:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:46:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a successfully announced in 77.1106 ms
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 successfully announced in 76.9966 ms
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 all the dispatchers started
2025-07-11 08:46:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a all the dispatchers started
2025-07-11 08:47:22 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:47:22 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:47:22 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"gElUIPP3wBu5WR2h2pJc7IEo2Zq1s5ZdSHUMovftopM"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:48:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 caught stopping signal...
2025-07-11 08:48:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a caught stopping signal...
2025-07-11 08:48:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 caught stopped signal...
2025-07-11 08:48:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a caught stopped signal...
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 All dispatchers stopped
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 successfully reported itself as stopped in 2.4984 ms
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:3d5f3a52 has been stopped in total 880.4003 ms
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a All dispatchers stopped
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a successfully reported itself as stopped in 1.1325 ms
2025-07-11 08:48:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21444:4066612a has been stopped in total 926.0638 ms
2025-07-11 08:48:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:48:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:48:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a successfully announced in 71.3467 ms
2025-07-11 08:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 successfully announced in 114.3553 ms
2025-07-11 08:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:48:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a all the dispatchers started
2025-07-11 08:48:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 all the dispatchers started
2025-07-11 08:50:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 caught stopping signal...
2025-07-11 08:50:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a caught stopping signal...
2025-07-11 08:50:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a caught stopped signal...
2025-07-11 08:50:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 caught stopped signal...
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 All dispatchers stopped
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 successfully reported itself as stopped in 1.7044 ms
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:64aa4798 has been stopped in total 838.7998 ms
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a All dispatchers stopped
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a successfully reported itself as stopped in 0.8361 ms
2025-07-11 08:50:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24364:2c38707a has been stopped in total 841.8472 ms
2025-07-11 08:51:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 08:51:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 08:51:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 successfully announced in 86.3685 ms
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 successfully announced in 86.9492 ms
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 all the dispatchers started
2025-07-11 08:51:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 all the dispatchers started
2025-07-11 08:51:03 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 08:51:03 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 08:51:03 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"n_Keqs4PdmqQ0vfTe6LOs7UL9ODgK_byYOv3UaW3vBw"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 103
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 caught stopping signal...
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 caught stopping signal...
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 All dispatchers stopped
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 successfully reported itself as stopped in 2.9495 ms
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:020c81f9 has been stopped in total 20.5474 ms
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 All dispatchers stopped
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 successfully reported itself as stopped in 1.1651 ms
2025-07-11 09:01:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20852:207f25a3 has been stopped in total 189.5713 ms
2025-07-11 09:01:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:01:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:01:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce successfully announced in 93.3916 ms
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f successfully announced in 93.8317 ms
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce all the dispatchers started
2025-07-11 09:01:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f all the dispatchers started
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f caught stopping signal...
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce caught stopping signal...
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f All dispatchers stopped
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f successfully reported itself as stopped in 3.7918 ms
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:47b2576f has been stopped in total 323.9857 ms
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce All dispatchers stopped
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce successfully reported itself as stopped in 0.7573 ms
2025-07-11 09:02:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25440:6962cfce has been stopped in total 327.7596 ms
2025-07-11 09:02:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:02:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:02:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 successfully announced in 71.7868 ms
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 successfully announced in 72.1939 ms
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 all the dispatchers started
2025-07-11 09:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 all the dispatchers started
2025-07-11 09:06:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 caught stopping signal...
2025-07-11 09:06:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 caught stopping signal...
2025-07-11 09:06:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 caught stopped signal...
2025-07-11 09:06:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 caught stopped signal...
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 All dispatchers stopped
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 successfully reported itself as stopped in 1.7612 ms
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:57642131 has been stopped in total 766.7935 ms
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 All dispatchers stopped
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 successfully reported itself as stopped in 0.6968 ms
2025-07-11 09:06:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30724:a0570819 has been stopped in total 808.5948 ms
2025-07-11 09:06:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:06:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:06:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee successfully announced in 89.2663 ms
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 successfully announced in 91.8839 ms
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 all the dispatchers started
2025-07-11 09:06:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee all the dispatchers started
2025-07-11 09:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee caught stopping signal...
2025-07-11 09:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 caught stopping signal...
2025-07-11 09:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 All dispatchers stopped
2025-07-11 09:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 successfully reported itself as stopped in 2.501 ms
2025-07-11 09:07:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:9a6b3587 has been stopped in total 51.3896 ms
2025-07-11 09:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee caught stopped signal...
2025-07-11 09:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee All dispatchers stopped
2025-07-11 09:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee successfully reported itself as stopped in 0.9995 ms
2025-07-11 09:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37340:21e6f5ee has been stopped in total 856.7179 ms
2025-07-11 09:08:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:08:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:08:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:08:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e successfully announced in 186.1619 ms
2025-07-11 09:08:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 successfully announced in 75.9224 ms
2025-07-11 09:08:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:08:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:08:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e all the dispatchers started
2025-07-11 09:08:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 all the dispatchers started
2025-07-11 09:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e caught stopping signal...
2025-07-11 09:08:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 caught stopping signal...
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 All dispatchers stopped
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e All dispatchers stopped
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e successfully reported itself as stopped in 2.0229 ms
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:f954629e has been stopped in total 289.5674 ms
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 successfully reported itself as stopped in 1.8056 ms
2025-07-11 09:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20932:307dbe30 has been stopped in total 290.2932 ms
2025-07-11 09:09:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:09:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:09:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f successfully announced in 68.6716 ms
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 successfully announced in 52.6636 ms
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f all the dispatchers started
2025-07-11 09:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 all the dispatchers started
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f caught stopping signal...
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 caught stopping signal...
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 All dispatchers stopped
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f All dispatchers stopped
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 successfully reported itself as stopped in 2.1225 ms
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:9be69ee8 has been stopped in total 234.0477 ms
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f successfully reported itself as stopped in 2.5611 ms
2025-07-11 09:17:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13180:bf56957f has been stopped in total 234.7369 ms
2025-07-11 09:17:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:17:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:17:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 successfully announced in 93.2854 ms
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 successfully announced in 93.6053 ms
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 all the dispatchers started
2025-07-11 09:17:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 all the dispatchers started
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 caught stopping signal...
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 caught stopping signal...
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 caught stopped signal...
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 caught stopped signal...
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 All dispatchers stopped
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 successfully reported itself as stopped in 2.7762 ms
2025-07-11 09:21:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:82347a79 has been stopped in total 641.5883 ms
2025-07-11 09:21:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 All dispatchers stopped
2025-07-11 09:21:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 successfully reported itself as stopped in 1.0163 ms
2025-07-11 09:21:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:f2321396 has been stopped in total 834.787 ms
2025-07-11 09:27:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:27:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:27:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 successfully announced in 68.6072 ms
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 successfully announced in 79.2244 ms
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 all the dispatchers started
2025-07-11 09:27:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 all the dispatchers started
2025-07-11 09:27:08 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Cannot provide a value for property 'Api' on type 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage'. There is no registered service of type 'HuaLingErpApp.Client.ApiService'."
System.InvalidOperationException: Cannot provide a value for property 'Api' on type 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage'. There is no registered service of type 'HuaLingErpApp.Client.ApiService'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-11 09:27:08 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"J8Q_47vFNF_MfwT2krHiwLSiDL_flMZ7SzPR0wt_2dc"'.
System.InvalidOperationException: Cannot provide a value for property 'Api' on type 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage'. There is no registered service of type 'HuaLingErpApp.Client.ApiService'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-11 09:27:19 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Cannot provide a value for property 'Api' on type 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage'. There is no registered service of type 'HuaLingErpApp.Client.ApiService'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 09:27:30 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Cannot provide a value for property 'Api' on type 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage'. There is no registered service of type 'HuaLingErpApp.Client.ApiService'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 caught stopping signal...
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 caught stopping signal...
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 caught stopped signal...
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 caught stopped signal...
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 All dispatchers stopped
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 successfully reported itself as stopped in 2.6135 ms
2025-07-11 09:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:9e7b6d53 has been stopped in total 530.7672 ms
2025-07-11 09:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 All dispatchers stopped
2025-07-11 09:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 successfully reported itself as stopped in 1.168 ms
2025-07-11 09:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36980:dc402912 has been stopped in total 698.5886 ms
2025-07-11 09:29:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:29:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:29:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:29:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 successfully announced in 50.345 ms
2025-07-11 09:29:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd successfully announced in 138.5012 ms
2025-07-11 09:29:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:29:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:29:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd all the dispatchers started
2025-07-11 09:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 all the dispatchers started
2025-07-11 09:38:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd caught stopping signal...
2025-07-11 09:38:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 caught stopping signal...
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 caught stopped signal...
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd caught stopped signal...
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 All dispatchers stopped
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd All dispatchers stopped
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 successfully reported itself as stopped in 5.8459 ms
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd successfully reported itself as stopped in 3.5218 ms
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:4889a1a0 has been stopped in total 966.3603 ms
2025-07-11 09:38:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21768:8223ebbd has been stopped in total 967.5731 ms
2025-07-11 09:38:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:38:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:38:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 successfully announced in 78.5004 ms
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 successfully announced in 79.1522 ms
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 all the dispatchers started
2025-07-11 09:38:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 all the dispatchers started
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 caught stopping signal...
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 caught stopping signal...
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 All dispatchers stopped
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 successfully reported itself as stopped in 3.7871 ms
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:307e6356 has been stopped in total 114.7114 ms
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 All dispatchers stopped
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 successfully reported itself as stopped in 1.2571 ms
2025-07-11 09:43:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14356:1dc77c90 has been stopped in total 173.3445 ms
2025-07-11 09:57:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:57:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:57:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 successfully announced in 127.1537 ms
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff successfully announced in 138.2989 ms
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 all the dispatchers started
2025-07-11 09:57:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff all the dispatchers started
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff caught stopping signal...
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 caught stopping signal...
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 All dispatchers stopped
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 successfully reported itself as stopped in 4.8163 ms
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:7fc668a9 has been stopped in total 57.0963 ms
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff All dispatchers stopped
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff successfully reported itself as stopped in 1.2695 ms
2025-07-11 09:58:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9660:475d8dff has been stopped in total 70.4534 ms
2025-07-11 09:58:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:58:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:58:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab successfully announced in 103.5237 ms
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 successfully announced in 103.3057 ms
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 all the dispatchers started
2025-07-11 09:58:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab all the dispatchers started
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 caught stopping signal...
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab caught stopping signal...
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 All dispatchers stopped
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 successfully reported itself as stopped in 2.5728 ms
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:b8121082 has been stopped in total 135.7 ms
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab All dispatchers stopped
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab successfully reported itself as stopped in 0.6844 ms
2025-07-11 09:59:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27924:6dcafaab has been stopped in total 146.5015 ms
2025-07-11 09:59:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 09:59:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 09:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb successfully announced in 116.8439 ms
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 successfully announced in 90.8455 ms
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb all the dispatchers started
2025-07-11 09:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 all the dispatchers started
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb caught stopping signal...
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 caught stopping signal...
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 All dispatchers stopped
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 successfully reported itself as stopped in 2.7553 ms
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:67126013 has been stopped in total 278.8025 ms
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb All dispatchers stopped
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb successfully reported itself as stopped in 0.9962 ms
2025-07-11 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25448:01d61bbb has been stopped in total 285.1394 ms
2025-07-11 10:00:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:00:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:00:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb successfully announced in 114.2956 ms
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f successfully announced in 83.7655 ms
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb all the dispatchers started
2025-07-11 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f all the dispatchers started
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb caught stopping signal...
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f caught stopping signal...
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f All dispatchers stopped
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb All dispatchers stopped
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f successfully reported itself as stopped in 3.1437 ms
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:13b5443f has been stopped in total 301.5179 ms
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb successfully reported itself as stopped in 1.0204 ms
2025-07-11 10:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32448:c47aa8cb has been stopped in total 302.6065 ms
2025-07-11 10:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 successfully announced in 119.6245 ms
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 successfully announced in 119.7337 ms
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 all the dispatchers started
2025-07-11 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 all the dispatchers started
2025-07-11 10:03:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 caught stopping signal...
2025-07-11 10:03:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 caught stopping signal...
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 caught stopped signal...
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 caught stopped signal...
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 All dispatchers stopped
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 successfully reported itself as stopped in 2.4918 ms
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:896b02c6 has been stopped in total 615.3961 ms
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 All dispatchers stopped
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 successfully reported itself as stopped in 1.6927 ms
2025-07-11 10:03:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37792:425851e9 has been stopped in total 664.5979 ms
2025-07-11 10:04:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:04:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 successfully announced in 88.8226 ms
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c successfully announced in 163.2736 ms
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 all the dispatchers started
2025-07-11 10:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c all the dispatchers started
2025-07-11 10:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c caught stopping signal...
2025-07-11 10:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 caught stopping signal...
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 caught stopped signal...
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c caught stopped signal...
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 All dispatchers stopped
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 successfully reported itself as stopped in 2.0591 ms
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:1853c632 has been stopped in total 813.0249 ms
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c All dispatchers stopped
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c successfully reported itself as stopped in 1.0885 ms
2025-07-11 10:04:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18532:cb763a2c has been stopped in total 830.6507 ms
2025-07-11 10:04:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:04:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:04:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 successfully announced in 109.2103 ms
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e successfully announced in 21.9774 ms
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 all the dispatchers started
2025-07-11 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e all the dispatchers started
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 caught stopping signal...
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e caught stopping signal...
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e All dispatchers stopped
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e successfully reported itself as stopped in 2.0588 ms
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:b914334e has been stopped in total 274.8798 ms
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 All dispatchers stopped
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 successfully reported itself as stopped in 1.3593 ms
2025-07-11 10:05:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37184:cb0085c2 has been stopped in total 277.1401 ms
2025-07-11 10:05:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:05:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 successfully announced in 111.1236 ms
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 successfully announced in 111.1357 ms
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 all the dispatchers started
2025-07-11 10:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 all the dispatchers started
2025-07-11 10:05:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 caught stopping signal...
2025-07-11 10:05:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 caught stopping signal...
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 All dispatchers stopped
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 successfully reported itself as stopped in 2.4673 ms
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:753c64a9 has been stopped in total 90.6656 ms
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 All dispatchers stopped
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 successfully reported itself as stopped in 1.4857 ms
2025-07-11 10:05:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:37368:00b464c8 has been stopped in total 275.2337 ms
2025-07-11 10:06:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:06:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:06:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 successfully announced in 108.6614 ms
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 successfully announced in 105.2008 ms
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 all the dispatchers started
2025-07-11 10:06:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 all the dispatchers started
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 caught stopping signal...
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 caught stopping signal...
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 All dispatchers stopped
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 successfully reported itself as stopped in 2.5926 ms
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:b3cceb53 has been stopped in total 467.6176 ms
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 All dispatchers stopped
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 successfully reported itself as stopped in 1.7074 ms
2025-07-11 10:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29400:35991c10 has been stopped in total 490.8493 ms
2025-07-11 10:23:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:23:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:23:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed successfully announced in 107.9063 ms
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 successfully announced in 107.9246 ms
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed all the dispatchers started
2025-07-11 10:23:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 all the dispatchers started
2025-07-11 10:24:01 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "MessageService not registered. refer doc https://www.blazor.zone/install-webapp step 7 for BootstrapBlazorRoot"
System.InvalidOperationException: MessageService not registered. refer doc https://www.blazor.zone/install-webapp step 7 for BootstrapBlazorRoot
   at BootstrapBlazor.Components.BootstrapServiceBase`1.Invoke(TOption option, ComponentBase component)
   at BootstrapBlazor.Components.MessageService.Show(MessageOption option, Message message)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnDeleteAsync(IEnumerable`1 items) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 223
   at BootstrapBlazor.Components.Table`1.InternalOnDeleteAsync()
   at BootstrapBlazor.Components.Table`1.<DeleteAsync>g__DeleteItemsAsync|1549_0()
   at BootstrapBlazor.Components.Table`1.DeleteAsync()
   at BootstrapBlazor.Components.TableToolbar`1.OnConfirm(TableToolbarPopConfirmButton`1 button)
   at BootstrapBlazor.Components.PopConfirmButton.OnClickConfirm()
   at BootstrapBlazor.Components.PopConfirmButtonContent.OnConfirmClick()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 10:24:01 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"6UIixq4_-oJHiFuc3d6U8jgRgVu6KkIps1V6tN4MgBU"'.
System.InvalidOperationException: MessageService not registered. refer doc https://www.blazor.zone/install-webapp step 7 for BootstrapBlazorRoot
   at BootstrapBlazor.Components.BootstrapServiceBase`1.Invoke(TOption option, ComponentBase component)
   at BootstrapBlazor.Components.MessageService.Show(MessageOption option, Message message)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnDeleteAsync(IEnumerable`1 items) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 223
   at BootstrapBlazor.Components.Table`1.InternalOnDeleteAsync()
   at BootstrapBlazor.Components.Table`1.<DeleteAsync>g__DeleteItemsAsync|1549_0()
   at BootstrapBlazor.Components.Table`1.DeleteAsync()
   at BootstrapBlazor.Components.TableToolbar`1.OnConfirm(TableToolbarPopConfirmButton`1 button)
   at BootstrapBlazor.Components.PopConfirmButton.OnClickConfirm()
   at BootstrapBlazor.Components.PopConfirmButtonContent.OnConfirmClick()
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed caught stopping signal...
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 caught stopping signal...
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed All dispatchers stopped
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed successfully reported itself as stopped in 2.8586 ms
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:595a6fed has been stopped in total 229.2789 ms
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 All dispatchers stopped
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 successfully reported itself as stopped in 1.5601 ms
2025-07-11 10:26:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19404:aa950cc5 has been stopped in total 319.2232 ms
2025-07-11 10:26:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:26:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:26:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba successfully announced in 106.7939 ms
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac successfully announced in 106.8005 ms
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba all the dispatchers started
2025-07-11 10:26:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac all the dispatchers started
2025-07-11 10:27:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac caught stopping signal...
2025-07-11 10:27:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba caught stopping signal...
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac All dispatchers stopped
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac successfully reported itself as stopped in 1.8276 ms
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:61aae1ac has been stopped in total 341.0962 ms
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba All dispatchers stopped
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba successfully reported itself as stopped in 1.2919 ms
2025-07-11 10:27:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:d38643ba has been stopped in total 353.9152 ms
2025-07-11 10:27:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:27:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 successfully announced in 113.9735 ms
2025-07-11 10:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 successfully announced in 5.1552 ms
2025-07-11 10:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 all the dispatchers started
2025-07-11 10:27:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 all the dispatchers started
2025-07-11 10:33:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 caught stopping signal...
2025-07-11 10:33:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 caught stopping signal...
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 All dispatchers stopped
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 successfully reported itself as stopped in 3.716 ms
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:c6ecf3e2 has been stopped in total 67.7692 ms
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 caught stopped signal...
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 All dispatchers stopped
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 successfully reported itself as stopped in 1.8184 ms
2025-07-11 10:33:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:a26ae0f3 has been stopped in total 570.2085 ms
2025-07-11 10:33:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:33:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:33:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 successfully announced in 107.1566 ms
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c successfully announced in 107.113 ms
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c all the dispatchers started
2025-07-11 10:33:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 all the dispatchers started
2025-07-11 10:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c caught stopping signal...
2025-07-11 10:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 caught stopping signal...
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 caught stopped signal...
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c caught stopped signal...
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 All dispatchers stopped
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 successfully reported itself as stopped in 3.8157 ms
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:912b4f53 has been stopped in total 738.8173 ms
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c All dispatchers stopped
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c successfully reported itself as stopped in 0.8603 ms
2025-07-11 10:39:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11488:8a0f682c has been stopped in total 762.3791 ms
2025-07-11 10:39:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:39:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:39:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f successfully announced in 115.7616 ms
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be successfully announced in 115.8286 ms
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be all the dispatchers started
2025-07-11 10:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f all the dispatchers started
2025-07-11 10:46:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f caught stopping signal...
2025-07-11 10:46:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be caught stopping signal...
2025-07-11 10:46:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f All dispatchers stopped
2025-07-11 10:46:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f successfully reported itself as stopped in 3.4122 ms
2025-07-11 10:46:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:3529ef3f has been stopped in total 52.151 ms
2025-07-11 10:46:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be caught stopped signal...
2025-07-11 10:46:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be All dispatchers stopped
2025-07-11 10:46:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be successfully reported itself as stopped in 1.1908 ms
2025-07-11 10:46:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29716:eeefd5be has been stopped in total 720.6998 ms
2025-07-11 10:46:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:46:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf successfully announced in 104.5713 ms
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 successfully announced in 104.9817 ms
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf all the dispatchers started
2025-07-11 10:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 all the dispatchers started
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 caught stopping signal...
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf caught stopping signal...
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf All dispatchers stopped
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf successfully reported itself as stopped in 3.6552 ms
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:05bb9dcf has been stopped in total 182.6038 ms
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 caught stopped signal...
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 All dispatchers stopped
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 successfully reported itself as stopped in 1.74 ms
2025-07-11 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11620:5e484595 has been stopped in total 570.4899 ms
2025-07-11 10:58:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 10:58:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 10:58:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b successfully announced in 110.9965 ms
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 successfully announced in 110.821 ms
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 all the dispatchers started
2025-07-11 10:58:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b all the dispatchers started
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 caught stopping signal...
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b caught stopping signal...
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 All dispatchers stopped
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 successfully reported itself as stopped in 4.6474 ms
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:7f4cd950 has been stopped in total 78.9206 ms
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b All dispatchers stopped
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b successfully reported itself as stopped in 0.8441 ms
2025-07-11 11:04:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12296:ad1f1b8b has been stopped in total 156.5065 ms
2025-07-11 11:04:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:04:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:04:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 successfully announced in 110.6066 ms
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a successfully announced in 110.6008 ms
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a all the dispatchers started
2025-07-11 11:04:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 all the dispatchers started
2025-07-11 11:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 caught stopping signal...
2025-07-11 11:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a caught stopping signal...
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 All dispatchers stopped
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 successfully reported itself as stopped in 2.0177 ms
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:9cdf2f24 has been stopped in total 415.3273 ms
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a All dispatchers stopped
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a successfully reported itself as stopped in 1.5092 ms
2025-07-11 11:07:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13760:63ffed3a has been stopped in total 494.2821 ms
2025-07-11 11:08:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:08:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:08:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 successfully announced in 148.1511 ms
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 successfully announced in 128.2489 ms
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 all the dispatchers started
2025-07-11 11:08:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 all the dispatchers started
2025-07-11 11:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 caught stopping signal...
2025-07-11 11:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 caught stopping signal...
2025-07-11 11:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 All dispatchers stopped
2025-07-11 11:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 successfully reported itself as stopped in 4.5426 ms
2025-07-11 11:10:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:ef0abf42 has been stopped in total 145.6727 ms
2025-07-11 11:10:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 caught stopped signal...
2025-07-11 11:10:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 All dispatchers stopped
2025-07-11 11:10:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 successfully reported itself as stopped in 1.1384 ms
2025-07-11 11:10:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:d743bb84 has been stopped in total 765.4514 ms
2025-07-11 11:10:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:10:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:10:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 successfully announced in 146.1465 ms
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 successfully announced in 146.1351 ms
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 all the dispatchers started
2025-07-11 11:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 all the dispatchers started
2025-07-11 11:13:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 caught stopping signal...
2025-07-11 11:13:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 caught stopping signal...
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 caught stopped signal...
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 caught stopped signal...
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 All dispatchers stopped
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 successfully reported itself as stopped in 2.7633 ms
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:f7149947 has been stopped in total 922.8274 ms
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 All dispatchers stopped
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 successfully reported itself as stopped in 1.5451 ms
2025-07-11 11:13:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13936:283bdc97 has been stopped in total 925.9715 ms
2025-07-11 11:13:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:13:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:13:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 successfully announced in 108.3251 ms
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf successfully announced in 108.3687 ms
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf all the dispatchers started
2025-07-11 11:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 all the dispatchers started
2025-07-11 11:16:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf caught stopping signal...
2025-07-11 11:16:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 caught stopping signal...
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 All dispatchers stopped
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 successfully reported itself as stopped in 3.3947 ms
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:2294fbe8 has been stopped in total 481.1733 ms
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf caught stopped signal...
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf All dispatchers stopped
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf successfully reported itself as stopped in 1.3468 ms
2025-07-11 11:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33624:76e70bcf has been stopped in total 545.8765 ms
2025-07-11 11:33:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:33:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:33:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 successfully announced in 111.8751 ms
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 successfully announced in 113.6214 ms
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 all the dispatchers started
2025-07-11 11:33:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 all the dispatchers started
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 caught stopping signal...
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 caught stopping signal...
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 All dispatchers stopped
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 All dispatchers stopped
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 successfully reported itself as stopped in 2.6941 ms
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2c4e9479 has been stopped in total 485.1228 ms
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 successfully reported itself as stopped in 0.6892 ms
2025-07-11 11:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31332:2a3ea428 has been stopped in total 485.9596 ms
2025-07-11 11:37:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:37:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 successfully announced in 112.4936 ms
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e successfully announced in 112.4812 ms
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e all the dispatchers started
2025-07-11 11:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 all the dispatchers started
2025-07-11 11:37:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e caught stopping signal...
2025-07-11 11:37:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 caught stopping signal...
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e All dispatchers stopped
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e successfully reported itself as stopped in 7.5566 ms
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:1bc0806e has been stopped in total 331.1587 ms
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 All dispatchers stopped
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 successfully reported itself as stopped in 2.1537 ms
2025-07-11 11:37:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33856:89fe4015 has been stopped in total 341.1098 ms
2025-07-11 11:38:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:38:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:38:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 successfully announced in 213.734 ms
2025-07-11 11:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 successfully announced in 33.6623 ms
2025-07-11 11:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 all the dispatchers started
2025-07-11 11:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 all the dispatchers started
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 caught stopping signal...
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 caught stopping signal...
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 All dispatchers stopped
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 successfully reported itself as stopped in 2.5336 ms
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:1ca86eb0 has been stopped in total 398.0533 ms
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 caught stopped signal...
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 All dispatchers stopped
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 successfully reported itself as stopped in 0.651 ms
2025-07-11 11:41:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31492:59bd4919 has been stopped in total 558.6614 ms
2025-07-11 11:41:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:41:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:41:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee successfully announced in 117.0058 ms
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 successfully announced in 170.5957 ms
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 all the dispatchers started
2025-07-11 11:41:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee all the dispatchers started
2025-07-11 11:44:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 caught stopping signal...
2025-07-11 11:44:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee caught stopping signal...
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 All dispatchers stopped
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 successfully reported itself as stopped in 3.177 ms
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:a107bd18 has been stopped in total 179.297 ms
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee caught stopped signal...
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee All dispatchers stopped
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee successfully reported itself as stopped in 1.1171 ms
2025-07-11 11:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5532:99747cee has been stopped in total 974.9135 ms
2025-07-11 11:44:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 11:44:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 11:44:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a successfully announced in 109.9079 ms
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 successfully announced in 109.9299 ms
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a all the dispatchers started
2025-07-11 11:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 all the dispatchers started
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a caught stopping signal...
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 caught stopping signal...
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 caught stopped signal...
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a caught stopped signal...
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a All dispatchers stopped
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a successfully reported itself as stopped in 14.0159 ms
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:36f1232a has been stopped in total 576.792 ms
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 All dispatchers stopped
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 successfully reported itself as stopped in 0.791 ms
2025-07-11 13:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27764:c065b4b8 has been stopped in total 584.9364 ms
2025-07-11 14:00:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:00:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:00:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 successfully announced in 82.9269 ms
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 successfully announced in 109.1414 ms
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 all the dispatchers started
2025-07-11 14:00:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 all the dispatchers started
2025-07-11 14:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 caught stopping signal...
2025-07-11 14:01:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 caught stopping signal...
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 caught stopped signal...
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 caught stopped signal...
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 All dispatchers stopped
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 All dispatchers stopped
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 successfully reported itself as stopped in 1.8558 ms
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:cb7adb74 has been stopped in total 701.2337 ms
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 successfully reported itself as stopped in 0.8877 ms
2025-07-11 14:01:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32632:d89c0265 has been stopped in total 699.0792 ms
2025-07-11 14:01:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:01:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:01:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d successfully announced in 140.3768 ms
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f successfully announced in 140.4442 ms
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f all the dispatchers started
2025-07-11 14:01:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d all the dispatchers started
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f caught stopping signal...
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d caught stopping signal...
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d All dispatchers stopped
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d successfully reported itself as stopped in 2.523 ms
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:be9d3d7d has been stopped in total 109.3052 ms
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f All dispatchers stopped
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f successfully reported itself as stopped in 0.7346 ms
2025-07-11 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35600:3e5d860f has been stopped in total 129.0649 ms
2025-07-11 14:05:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:05:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:05:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:05:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 successfully announced in 120.4782 ms
2025-07-11 14:05:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed successfully announced in 120.5347 ms
2025-07-11 14:05:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:05:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:05:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 all the dispatchers started
2025-07-11 14:05:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed all the dispatchers started
2025-07-11 14:09:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 caught stopping signal...
2025-07-11 14:09:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed caught stopping signal...
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed All dispatchers stopped
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed successfully reported itself as stopped in 1.7499 ms
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:f09ce0ed has been stopped in total 498.9467 ms
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 caught stopped signal...
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 All dispatchers stopped
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 successfully reported itself as stopped in 1.241 ms
2025-07-11 14:09:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29472:93fc3e63 has been stopped in total 640.1483 ms
2025-07-11 14:10:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:10:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:10:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da successfully announced in 107.5058 ms
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 successfully announced in 107.6458 ms
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da all the dispatchers started
2025-07-11 14:10:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 all the dispatchers started
2025-07-11 14:11:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da caught stopping signal...
2025-07-11 14:11:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 caught stopping signal...
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 caught stopped signal...
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da caught stopped signal...
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da All dispatchers stopped
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da successfully reported itself as stopped in 2.7993 ms
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:c29281da has been stopped in total 647.2396 ms
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 All dispatchers stopped
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 successfully reported itself as stopped in 0.8423 ms
2025-07-11 14:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16088:23cefc54 has been stopped in total 773.8771 ms
2025-07-11 14:12:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:12:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:12:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 successfully announced in 111.4845 ms
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 successfully announced in 111.5857 ms
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 all the dispatchers started
2025-07-11 14:12:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 all the dispatchers started
2025-07-11 14:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 caught stopping signal...
2025-07-11 14:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 caught stopping signal...
2025-07-11 14:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 caught stopped signal...
2025-07-11 14:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 caught stopped signal...
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 All dispatchers stopped
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 All dispatchers stopped
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 successfully reported itself as stopped in 1.9023 ms
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 successfully reported itself as stopped in 2.2321 ms
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:5c671a72 has been stopped in total 776.3493 ms
2025-07-11 14:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33228:19e35297 has been stopped in total 775.3863 ms
2025-07-11 14:15:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:15:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:15:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d successfully announced in 111.6282 ms
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 successfully announced in 111.4596 ms
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 all the dispatchers started
2025-07-11 14:15:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d all the dispatchers started
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 caught stopping signal...
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d caught stopping signal...
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 All dispatchers stopped
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d All dispatchers stopped
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 successfully reported itself as stopped in 1.9631 ms
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:276cf3e9 has been stopped in total 373.7532 ms
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d successfully reported itself as stopped in 4.7386 ms
2025-07-11 14:18:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1048:41fd823d has been stopped in total 377.0944 ms
2025-07-11 14:18:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:18:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:18:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:18:52 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Object of type 'BootstrapBlazor.Components.BootstrapBlazorRoot' does not have a property matching the name 'Culture'.
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.ThrowForUnknownIncomingParameterName(Type targetType, String parameterName)
   at Microsoft.AspNetCore.Components.Reflection.ComponentProperties.SetProperties(ParameterView& parameters, Object target)
   at Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(ParameterView parameters)
   at BootstrapBlazor.Components.BootstrapBlazorRoot.<>n__0(ParameterView parameters)
   at BootstrapBlazor.Components.BootstrapBlazorRoot.SetParametersAsync(ParameterView parameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 successfully announced in 105.9794 ms
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 successfully announced in 106.5539 ms
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 all the dispatchers started
2025-07-11 14:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 all the dispatchers started
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 caught stopping signal...
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 caught stopping signal...
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 All dispatchers stopped
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 successfully reported itself as stopped in 5.8565 ms
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:ff0a6974 has been stopped in total 185.1594 ms
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 All dispatchers stopped
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 successfully reported itself as stopped in 0.7921 ms
2025-07-11 14:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35976:d9fe70f5 has been stopped in total 226.5254 ms
2025-07-11 14:25:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:25:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:25:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:25:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:25:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 successfully announced in 96.8853 ms
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 successfully announced in 130.0593 ms
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 all the dispatchers started
2025-07-11 14:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 all the dispatchers started
2025-07-11 14:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 caught stopping signal...
2025-07-11 14:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 caught stopping signal...
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 caught stopped signal...
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 caught stopped signal...
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 All dispatchers stopped
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 successfully reported itself as stopped in 1.7477 ms
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:59455815 has been stopped in total 689.282 ms
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 All dispatchers stopped
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 successfully reported itself as stopped in 0.8269 ms
2025-07-11 14:28:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19020:7d0fa552 has been stopped in total 718.4794 ms
2025-07-11 14:28:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:28:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:28:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 successfully announced in 104.4449 ms
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 successfully announced in 104.8446 ms
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 all the dispatchers started
2025-07-11 14:28:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 all the dispatchers started
2025-07-11 14:29:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 caught stopping signal...
2025-07-11 14:29:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 caught stopping signal...
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 All dispatchers stopped
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 All dispatchers stopped
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 successfully reported itself as stopped in 2.039 ms
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:14c81b41 has been stopped in total 94.5462 ms
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 successfully reported itself as stopped in 0.9951 ms
2025-07-11 14:29:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33644:4eeb95c3 has been stopped in total 95.2727 ms
2025-07-11 14:29:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:29:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e successfully announced in 110.0362 ms
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be successfully announced in 110.4018 ms
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be all the dispatchers started
2025-07-11 14:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e all the dispatchers started
2025-07-11 14:30:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be caught stopping signal...
2025-07-11 14:30:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e caught stopping signal...
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e All dispatchers stopped
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e successfully reported itself as stopped in 2.5413 ms
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:78d0443e has been stopped in total 349.6931 ms
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be caught stopped signal...
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be All dispatchers stopped
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be successfully reported itself as stopped in 1.7675 ms
2025-07-11 14:30:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33016:7ed152be has been stopped in total 680.3044 ms
2025-07-11 14:31:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:31:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:31:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:31:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd successfully announced in 108.9798 ms
2025-07-11 14:31:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 successfully announced in 109.1634 ms
2025-07-11 14:31:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:31:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd all the dispatchers started
2025-07-11 14:31:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 all the dispatchers started
2025-07-11 14:36:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd caught stopping signal...
2025-07-11 14:36:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 caught stopping signal...
2025-07-11 14:36:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 caught stopped signal...
2025-07-11 14:36:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd caught stopped signal...
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 All dispatchers stopped
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 successfully reported itself as stopped in 2.8315 ms
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:10490eb8 has been stopped in total 898.155 ms
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd All dispatchers stopped
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd successfully reported itself as stopped in 1.2666 ms
2025-07-11 14:36:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15816:ccef05bd has been stopped in total 904.7418 ms
2025-07-11 14:37:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:37:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:37:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 successfully announced in 109.4734 ms
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 successfully announced in 109.558 ms
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 all the dispatchers started
2025-07-11 14:37:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 all the dispatchers started
2025-07-11 14:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 caught stopping signal...
2025-07-11 14:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 caught stopping signal...
2025-07-11 14:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 caught stopped signal...
2025-07-11 14:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 caught stopped signal...
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 All dispatchers stopped
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 successfully reported itself as stopped in 2.0892 ms
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:6b57c4f8 has been stopped in total 629.0768 ms
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 All dispatchers stopped
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 successfully reported itself as stopped in 0.7954 ms
2025-07-11 14:40:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19464:55815b86 has been stopped in total 703.291 ms
2025-07-11 14:40:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:40:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:40:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 successfully announced in 109.4466 ms
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd successfully announced in 109.5833 ms
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 all the dispatchers started
2025-07-11 14:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd all the dispatchers started
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 caught stopping signal...
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd caught stopping signal...
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd All dispatchers stopped
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 All dispatchers stopped
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd successfully reported itself as stopped in 2.5373 ms
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:311648dd has been stopped in total 36.3876 ms
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 successfully reported itself as stopped in 0.7672 ms
2025-07-11 14:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25056:32af9495 has been stopped in total 37.1325 ms
2025-07-11 14:43:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:43:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:43:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 successfully announced in 105.809 ms
2025-07-11 14:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 successfully announced in 108.402 ms
2025-07-11 14:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 all the dispatchers started
2025-07-11 14:43:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 all the dispatchers started
2025-07-11 14:43:22 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Value cannot be null. (Parameter 'source')"
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Linq.Queryable.Count[TSource](IQueryable`1 source)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderLinesPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderLinesPage.razor:line 134
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-11 14:43:22 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Value cannot be null. (Parameter 'source')"
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Linq.Queryable.Count[TSource](IQueryable`1 source)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderLinesPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderLinesPage.razor:line 134
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 14:43:22 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"CNSSO1KNPoDBPg6HaP44Et_fJ6n-Y_gsRRq5Zg-OUpI"'.
System.ArgumentNullException: Value cannot be null. (Parameter 'source')
   at System.ArgumentNullException.Throw(String paramName)
   at System.ArgumentNullException.ThrowIfNull(Object argument, String paramName)
   at System.Linq.Queryable.Count[TSource](IQueryable`1 source)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderLinesPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderLinesPage.razor:line 134
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-11 14:46:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 caught stopping signal...
2025-07-11 14:46:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 caught stopping signal...
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 All dispatchers stopped
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 successfully reported itself as stopped in 2.6047 ms
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e96c57d4 has been stopped in total 322.6772 ms
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 All dispatchers stopped
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 successfully reported itself as stopped in 0.8346 ms
2025-07-11 14:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:48cc1629 has been stopped in total 332.798 ms
2025-07-11 14:46:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:46:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:46:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b successfully announced in 114.8466 ms
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b successfully announced in 114.9188 ms
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b all the dispatchers started
2025-07-11 14:46:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b all the dispatchers started
2025-07-11 14:51:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b caught stopping signal...
2025-07-11 14:51:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b caught stopping signal...
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b caught stopped signal...
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b caught stopped signal...
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b All dispatchers stopped
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b successfully reported itself as stopped in 2.5856 ms
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:f1820d0b has been stopped in total 705.6268 ms
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b All dispatchers stopped
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b successfully reported itself as stopped in 0.6862 ms
2025-07-11 14:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24308:8a57168b has been stopped in total 707.6596 ms
2025-07-11 14:51:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:51:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:51:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 successfully announced in 107.0331 ms
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 successfully announced in 106.9851 ms
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 all the dispatchers started
2025-07-11 14:51:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 all the dispatchers started
2025-07-11 14:52:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 caught stopping signal...
2025-07-11 14:52:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 caught stopping signal...
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 caught stopped signal...
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 caught stopped signal...
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 All dispatchers stopped
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 successfully reported itself as stopped in 2.4402 ms
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:04b35688 has been stopped in total 760.324 ms
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 All dispatchers stopped
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 successfully reported itself as stopped in 0.9332 ms
2025-07-11 14:52:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38180:11777d34 has been stopped in total 765.4609 ms
2025-07-11 14:52:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 14:52:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 14:52:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda successfully announced in 167.9869 ms
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 successfully announced in 171.408 ms
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda all the dispatchers started
2025-07-11 14:52:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 all the dispatchers started
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda caught stopping signal...
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 caught stopping signal...
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 caught stopped signal...
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda caught stopped signal...
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 All dispatchers stopped
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 successfully reported itself as stopped in 6.1049 ms
2025-07-11 15:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:90331218 has been stopped in total 643.5782 ms
2025-07-11 15:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda All dispatchers stopped
2025-07-11 15:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda successfully reported itself as stopped in 1.1241 ms
2025-07-11 15:01:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14532:147a6eda has been stopped in total 792.7776 ms
2025-07-11 15:06:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 15:06:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:06:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 successfully announced in 111.2193 ms
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b successfully announced in 114.0585 ms
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b all the dispatchers started
2025-07-11 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 all the dispatchers started
2025-07-11 15:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b caught stopping signal...
2025-07-11 15:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 caught stopping signal...
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 caught stopped signal...
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b caught stopped signal...
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b All dispatchers stopped
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 All dispatchers stopped
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b successfully reported itself as stopped in 16.3422 ms
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 successfully reported itself as stopped in 12.4466 ms
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:9b8d244b has been stopped in total 987.5262 ms
2025-07-11 15:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34064:951e82c9 has been stopped in total 986.0672 ms
2025-07-11 15:45:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 15:45:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:45:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 successfully announced in 111.9341 ms
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e successfully announced in 112.7681 ms
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e all the dispatchers started
2025-07-11 15:45:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 all the dispatchers started
2025-07-11 15:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e caught stopping signal...
2025-07-11 15:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 caught stopping signal...
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e All dispatchers stopped
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 All dispatchers stopped
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 successfully reported itself as stopped in 2.217 ms
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:4f35dd96 has been stopped in total 297.7857 ms
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e successfully reported itself as stopped in 2.7967 ms
2025-07-11 15:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21204:b62d648e has been stopped in total 298.2943 ms
2025-07-11 15:47:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 15:47:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 15:47:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff successfully announced in 104.3067 ms
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 successfully announced in 104.3053 ms
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 all the dispatchers started
2025-07-11 15:47:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff all the dispatchers started
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 caught stopping signal...
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff caught stopping signal...
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 All dispatchers stopped
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff All dispatchers stopped
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 successfully reported itself as stopped in 2.4032 ms
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:830895e5 has been stopped in total 243.0069 ms
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff successfully reported itself as stopped in 0.7599 ms
2025-07-11 15:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20460:2648c9ff has been stopped in total 243.5968 ms
2025-07-11 16:11:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 16:11:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:12:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 successfully announced in 103.3471 ms
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 successfully announced in 103.3131 ms
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 all the dispatchers started
2025-07-11 16:12:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 all the dispatchers started
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 caught stopping signal...
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 caught stopping signal...
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 All dispatchers stopped
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 successfully reported itself as stopped in 2.4145 ms
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:ce41b535 has been stopped in total 493.1429 ms
2025-07-11 16:52:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 caught stopped signal...
2025-07-11 16:52:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 All dispatchers stopped
2025-07-11 16:52:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 successfully reported itself as stopped in 0.9434 ms
2025-07-11 16:52:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15008:a5344b47 has been stopped in total 797.0103 ms
2025-07-11 16:52:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 16:52:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 successfully announced in 130.7807 ms
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce successfully announced in 130.7884 ms
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce all the dispatchers started
2025-07-11 16:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 all the dispatchers started
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce caught stopping signal...
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 caught stopping signal...
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 caught stopped signal...
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce All dispatchers stopped
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce caught stopped signal...
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce successfully reported itself as stopped in 2.7817 ms
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:a71c48ce has been stopped in total 557.4621 ms
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 All dispatchers stopped
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 successfully reported itself as stopped in 0.7478 ms
2025-07-11 16:52:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6660:55a879b3 has been stopped in total 599.809 ms
2025-07-11 16:53:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 16:53:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:53:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 successfully announced in 110.1901 ms
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 successfully announced in 109.9098 ms
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 all the dispatchers started
2025-07-11 16:53:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 all the dispatchers started
2025-07-11 16:56:50 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidCastException: Unable to cast object of type 'HuaLingErpApp.Shared.Models.EnumStatus' to type 'System.String'.
   at System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(Object value)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(Object value, ValidationContext validationContext)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(Object value, ValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.DataAnnotations.DataAnnotationsModelValidator.Validate(ModelValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitChildren(IValidationStrategy strategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 16:57:44 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidCastException: Unable to cast object of type 'HuaLingErpApp.Shared.Models.EnumStatus' to type 'System.String'.
   at System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(Object value)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(Object value, ValidationContext validationContext)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(Object value, ValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.DataAnnotations.DataAnnotationsModelValidator.Validate(ModelValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitChildren(IValidationStrategy strategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 caught stopping signal...
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 caught stopping signal...
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 caught stopped signal...
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 caught stopped signal...
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 All dispatchers stopped
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 All dispatchers stopped
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 successfully reported itself as stopped in 2.7461 ms
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:8afec172 has been stopped in total 816.2467 ms
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 successfully reported itself as stopped in 1.0501 ms
2025-07-11 16:58:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11556:0a7763b0 has been stopped in total 817.5859 ms
2025-07-11 16:59:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-11 16:59:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-11 16:59:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c successfully announced in 134.2938 ms
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 successfully announced in 140.0067 ms
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c all the dispatchers started
2025-07-11 16:59:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 all the dispatchers started
2025-07-11 16:59:53 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidCastException: Unable to cast object of type 'HuaLingErpApp.Shared.Models.EnumStatus' to type 'System.String'.
   at System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(Object value)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(Object value, ValidationContext validationContext)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(Object value, ValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.DataAnnotations.DataAnnotationsModelValidator.Validate(ModelValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitChildren(IValidationStrategy strategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-11 16:59:53 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"pNRdYibf49ooIFJvkIXfXpJOKkYBmq-etKvOVQE7y3k"'.
System.InvalidCastException: Unable to cast object of type 'HuaLingErpApp.Shared.Models.EnumStatus' to type 'System.String'.
   at System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(Object value)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(Object value, ValidationContext validationContext)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(Object value, ValidationContext validationContext)
   at BootstrapBlazor.Components.ValidateForm.ValidateDataAnnotations(Object value, ValidationContext context, List`1 results, PropertyInfo propertyInfo, String memberName)
   at BootstrapBlazor.Components.ValidateForm.ValidateAsync(IValidateComponent validator, ValidationContext context, List`1 messages, PropertyInfo pi, Object propertyValue)
   at BootstrapBlazor.Components.ValidateForm.ValidateObject(ValidationContext context, List`1 results)
   at BootstrapBlazor.Components.BootstrapBlazorEditContextDataAnnotationsExtensions.ValidateModel(ValidateForm editForm, EditContext editContext, ValidationMessageStore messages, IServiceProvider provider)
   at BootstrapBlazor.Components.BootstrapBlazorEditContextDataAnnotationsExtensions.<>c__DisplayClass0_0.<<AddEditContextDataAnnotationsValidation>b__0>d.MoveNext()
--- End of stack trace from previous location ---
   at System.Threading.Tasks.Task.<>c.<ThrowAsync>b__128_0(Object state)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.PostAsync[TState](Task antecedent, Action`1 callback, TState state)
2025-07-11 17:02:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 caught stopping signal...
2025-07-11 17:02:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c caught stopping signal...
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c caught stopped signal...
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 caught stopped signal...
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 All dispatchers stopped
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c All dispatchers stopped
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 successfully reported itself as stopped in 3.4921 ms
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:98066b77 has been stopped in total 994.4241 ms
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c successfully reported itself as stopped in 8.1161 ms
2025-07-11 17:02:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14216:c84e0a3c has been stopped in total 999.051 ms
