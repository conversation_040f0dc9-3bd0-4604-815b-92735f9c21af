2025-07-28 14:17:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:17:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:17:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:17:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:17:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e successfully announced in 103.0342 ms
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 successfully announced in 103.0456 ms
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 all the dispatchers started
2025-07-28 14:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e all the dispatchers started
2025-07-28 14:18:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e caught stopping signal...
2025-07-28 14:18:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 caught stopping signal...
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 caught stopped signal...
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e caught stopped signal...
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e All dispatchers stopped
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 All dispatchers stopped
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 successfully reported itself as stopped in 2.1082 ms
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:7ca082f2 has been stopped in total 877.9901 ms
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e successfully reported itself as stopped in 1.9441 ms
2025-07-28 14:18:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3292:29057a0e has been stopped in total 879.2652 ms
2025-07-28 14:18:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:18:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:18:37 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:18:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:18:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 successfully announced in 107.0339 ms
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 successfully announced in 107.2218 ms
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 all the dispatchers started
2025-07-28 14:18:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 all the dispatchers started
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 caught stopping signal...
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 caught stopping signal...
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 All dispatchers stopped
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 All dispatchers stopped
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 successfully reported itself as stopped in 1.906 ms
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:48e3c169 has been stopped in total 249.5856 ms
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 successfully reported itself as stopped in 0.8518 ms
2025-07-28 14:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15492:8c31eda8 has been stopped in total 250.949 ms
2025-07-28 14:20:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:20:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:20:01 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf successfully announced in 106.3524 ms
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 successfully announced in 106.3956 ms
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 all the dispatchers started
2025-07-28 14:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf all the dispatchers started
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 caught stopping signal...
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf caught stopping signal...
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf All dispatchers stopped
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf successfully reported itself as stopped in 2.6395 ms
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:fa8fc5bf has been stopped in total 416.6478 ms
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 All dispatchers stopped
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 successfully reported itself as stopped in 0.7738 ms
2025-07-28 14:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21664:04b4b407 has been stopped in total 418.6634 ms
2025-07-28 14:31:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:31:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:31:14 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:31:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 successfully announced in 60.8541 ms
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 successfully announced in 56.4774 ms
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 all the dispatchers started
2025-07-28 14:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 all the dispatchers started
2025-07-28 14:31:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 caught stopping signal...
2025-07-28 14:31:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 caught stopping signal...
2025-07-28 14:31:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 caught stopped signal...
2025-07-28 14:31:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 caught stopped signal...
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 All dispatchers stopped
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 All dispatchers stopped
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 successfully reported itself as stopped in 2.2548 ms
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 successfully reported itself as stopped in 2.2786 ms
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:487e17e5 has been stopped in total 698.634 ms
2025-07-28 14:31:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23172:c73a2fd7 has been stopped in total 698.4728 ms
2025-07-28 14:31:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:31:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:31:51 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:31:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:31:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 successfully announced in 101.3792 ms
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 successfully announced in 101.4565 ms
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 all the dispatchers started
2025-07-28 14:31:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 all the dispatchers started
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 caught stopping signal...
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 caught stopping signal...
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 All dispatchers stopped
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 successfully reported itself as stopped in 1.7422 ms
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:3ece79f1 has been stopped in total 78.6231 ms
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 All dispatchers stopped
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 successfully reported itself as stopped in 0.724 ms
2025-07-28 14:36:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10260:5e8205d9 has been stopped in total 95.0317 ms
2025-07-28 14:36:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:36:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:36:15 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:36:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:36:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 successfully announced in 107.1738 ms
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 successfully announced in 107.2174 ms
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 all the dispatchers started
2025-07-28 14:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 all the dispatchers started
2025-07-28 14:37:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 caught stopping signal...
2025-07-28 14:37:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 caught stopping signal...
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 All dispatchers stopped
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 All dispatchers stopped
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 successfully reported itself as stopped in 2.2251 ms
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:a27d2775 has been stopped in total 176.8011 ms
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 successfully reported itself as stopped in 1.0497 ms
2025-07-28 14:37:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21508:86be7bd5 has been stopped in total 178.052 ms
2025-07-28 14:37:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:37:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:37:40 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:37:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:37:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda successfully announced in 103.9528 ms
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 successfully announced in 103.9125 ms
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda all the dispatchers started
2025-07-28 14:37:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 all the dispatchers started
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda caught stopping signal...
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 caught stopping signal...
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 caught stopped signal...
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda caught stopped signal...
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 All dispatchers stopped
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda All dispatchers stopped
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda successfully reported itself as stopped in 18.069 ms
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:dcd48dda has been stopped in total 661.4908 ms
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 successfully reported itself as stopped in 18.3325 ms
2025-07-28 14:55:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20476:0ba9e151 has been stopped in total 655.9681 ms
2025-07-28 15:02:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:02:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:02:19 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:02:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:02:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:02:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:02:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e successfully announced in 112.9857 ms
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 successfully announced in 112.9971 ms
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e all the dispatchers started
2025-07-28 15:02:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 all the dispatchers started
2025-07-28 15:03:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 caught stopping signal...
2025-07-28 15:03:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e caught stopping signal...
2025-07-28 15:03:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e All dispatchers stopped
2025-07-28 15:03:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e successfully reported itself as stopped in 1.8144 ms
2025-07-28 15:03:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:2250e46e has been stopped in total 44.7632 ms
2025-07-28 15:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 caught stopped signal...
2025-07-28 15:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 All dispatchers stopped
2025-07-28 15:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 successfully reported itself as stopped in 1.2963 ms
2025-07-28 15:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18580:3ee01ba4 has been stopped in total 974.5614 ms
2025-07-28 15:04:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:04:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:04:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:04:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 successfully announced in 104.2947 ms
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f successfully announced in 104.3547 ms
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f all the dispatchers started
2025-07-28 15:04:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 all the dispatchers started
2025-07-28 15:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f caught stopping signal...
2025-07-28 15:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 caught stopping signal...
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f All dispatchers stopped
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f successfully reported itself as stopped in 2.265 ms
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:84414d7f has been stopped in total 385.3722 ms
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 All dispatchers stopped
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 successfully reported itself as stopped in 1.5575 ms
2025-07-28 15:06:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:93996020 has been stopped in total 657.3501 ms
2025-07-28 15:08:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:08:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:08:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:08:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:08:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:08:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:08:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:08:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:08:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc successfully announced in 108.0528 ms
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa successfully announced in 108.0613 ms
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa all the dispatchers started
2025-07-28 15:08:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc all the dispatchers started
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc caught stopping signal...
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa caught stopping signal...
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa All dispatchers stopped
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa successfully reported itself as stopped in 1.9188 ms
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:30c27faa has been stopped in total 151.2556 ms
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc All dispatchers stopped
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc successfully reported itself as stopped in 1.1354 ms
2025-07-28 15:10:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23644:43bd7cbc has been stopped in total 219.3594 ms
2025-07-28 15:10:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:10:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:10:44 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:10:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 successfully announced in 101.3378 ms
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 successfully announced in 101.7159 ms
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 all the dispatchers started
2025-07-28 15:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 all the dispatchers started
2025-07-28 15:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 caught stopping signal...
2025-07-28 15:11:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 caught stopping signal...
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 All dispatchers stopped
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 successfully reported itself as stopped in 3.0118 ms
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:cc3a21e8 has been stopped in total 409.4363 ms
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 All dispatchers stopped
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 successfully reported itself as stopped in 1.4102 ms
2025-07-28 15:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9720:17dcdaf9 has been stopped in total 464.2771 ms
2025-07-28 15:13:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:13:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:13:06 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:13:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:13:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:13:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:13:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:13:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:13:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c successfully announced in 112.4614 ms
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef successfully announced in 112.3866 ms
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef all the dispatchers started
2025-07-28 15:13:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c all the dispatchers started
2025-07-28 15:29:55 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 15:29:55 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 15:29:55 [Information] HuaLingErpApp.Controller.ItemController: Successfully imported 16 items
2025-07-28 15:29:55 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 15:30:05 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 15:30:05 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 15:30:05 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 16 items
2025-07-28 15:30:05 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 15:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c caught stopping signal...
2025-07-28 15:38:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef caught stopping signal...
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef caught stopped signal...
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c caught stopped signal...
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c All dispatchers stopped
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c successfully reported itself as stopped in 6.0921 ms
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:4fc0b43c has been stopped in total 818.828 ms
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef All dispatchers stopped
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef successfully reported itself as stopped in 1.8415 ms
2025-07-28 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23460:78755fef has been stopped in total 938.8677 ms
2025-07-28 15:38:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:38:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:38:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:38:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:38:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 successfully announced in 127.5373 ms
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd successfully announced in 127.5091 ms
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 all the dispatchers started
2025-07-28 15:38:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd all the dispatchers started
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 caught stopping signal...
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd caught stopping signal...
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd All dispatchers stopped
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd successfully reported itself as stopped in 2.6703 ms
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:62d800bd has been stopped in total 156.5908 ms
2025-07-28 15:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 caught stopped signal...
2025-07-28 15:51:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 All dispatchers stopped
2025-07-28 15:51:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 successfully reported itself as stopped in 1.2512 ms
2025-07-28 15:51:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18060:3fd8f2a9 has been stopped in total 928.6312 ms
2025-07-28 15:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:51:46 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:51:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:51:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf successfully announced in 113.8317 ms
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad successfully announced in 113.8152 ms
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad all the dispatchers started
2025-07-28 15:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf all the dispatchers started
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad caught stopping signal...
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf caught stopping signal...
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf All dispatchers stopped
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf successfully reported itself as stopped in 1.9716 ms
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:96fcbfbf has been stopped in total 25.0237 ms
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad All dispatchers stopped
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad successfully reported itself as stopped in 0.662 ms
2025-07-28 15:54:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11916:8d7015ad has been stopped in total 27.1433 ms
2025-07-28 15:55:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 15:55:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 15:55:10 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 15:55:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 15:55:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 successfully announced in 108.328 ms
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 successfully announced in 108.3877 ms
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 all the dispatchers started
2025-07-28 15:55:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 all the dispatchers started
2025-07-28 16:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 caught stopping signal...
2025-07-28 16:11:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 caught stopping signal...
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 All dispatchers stopped
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 successfully reported itself as stopped in 3.4976 ms
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:69927560 has been stopped in total 173.0576 ms
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 caught stopped signal...
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 All dispatchers stopped
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 successfully reported itself as stopped in 0.9881 ms
2025-07-28 16:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16020:33916cd1 has been stopped in total 972.1446 ms
2025-07-28 16:30:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 16:30:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 16:30:58 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 16:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 16:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 16:30:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 16:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 successfully announced in 110.5997 ms
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 successfully announced in 110.6154 ms
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 all the dispatchers started
2025-07-28 16:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 all the dispatchers started
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 caught stopping signal...
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 caught stopping signal...
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 All dispatchers stopped
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 successfully reported itself as stopped in 1.976 ms
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:0a963911 has been stopped in total 61.6356 ms
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 All dispatchers stopped
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 successfully reported itself as stopped in 1.0867 ms
2025-07-28 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22448:e2daeea0 has been stopped in total 78.9712 ms
2025-07-28 16:32:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 16:32:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 16:32:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 16:32:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 16:32:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 16:32:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 16:32:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:ec54458f successfully announced in 102.5553 ms
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:4f267c63 successfully announced in 102.5953 ms
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:4f267c63 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:ec54458f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:ec54458f all the dispatchers started
2025-07-28 16:32:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:4f267c63 all the dispatchers started
2025-07-28 17:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:ec54458f caught stopping signal...
2025-07-28 17:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19172:4f267c63 caught stopping signal...
