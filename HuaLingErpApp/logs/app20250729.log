2025-07-29 09:33:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 09:33:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 09:33:59 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 09:33:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 09:33:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 09:34:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 09:34:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa successfully announced in 109.4514 ms
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d successfully announced in 109.7419 ms
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 09:34:00 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa all the dispatchers started
2025-07-29 09:34:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d all the dispatchers started
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa caught stopping signal...
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d caught stopping signal...
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d caught stopped signal...
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa caught stopped signal...
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa All dispatchers stopped
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d All dispatchers stopped
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d successfully reported itself as stopped in 2.4204 ms
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:de41663d has been stopped in total 640.5678 ms
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa successfully reported itself as stopped in 26.6077 ms
2025-07-29 10:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21496:57d5dffa has been stopped in total 664.9614 ms
2025-07-29 10:03:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:03:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:03:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 successfully announced in 109.1484 ms
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 successfully announced in 109.1861 ms
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 all the dispatchers started
2025-07-29 10:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 all the dispatchers started
2025-07-29 10:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 caught stopping signal...
2025-07-29 10:04:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 caught stopping signal...
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 All dispatchers stopped
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 All dispatchers stopped
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 successfully reported itself as stopped in 0.7584 ms
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:f6ebb2a2 has been stopped in total 454.3967 ms
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 successfully reported itself as stopped in 1.7261 ms
2025-07-29 10:04:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9528:22e12d79 has been stopped in total 455.0566 ms
2025-07-29 10:04:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:04:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:04:49 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:04:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:04:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 successfully announced in 103.1954 ms
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a successfully announced in 103.1713 ms
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a all the dispatchers started
2025-07-29 10:04:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 all the dispatchers started
2025-07-29 10:06:27 [Error] HuaLingErpApp.Controller.VendorsController: Failed to create vendor
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.vendor'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.VendorsController.Create(Vendor model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\VendorsController.cs:line 90
ClientConnectionId:942844a1-6ae4-48f9-acda-21a40d4d82d0
Error Number:515,State:2,Class:16
2025-07-29 10:06:41 [Error] HuaLingErpApp.Controller.VendorsController: Failed to create vendor
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.vendor'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.VendorsController.Create(Vendor model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\VendorsController.cs:line 90
ClientConnectionId:942844a1-6ae4-48f9-acda-21a40d4d82d0
Error Number:515,State:2,Class:16
2025-07-29 10:06:51 [Error] HuaLingErpApp.Controller.VendorsController: Failed to create vendor
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.vendor'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.VendorsController.Create(Vendor model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\VendorsController.cs:line 90
ClientConnectionId:3202d8d5-2afd-45f7-a092-d4c87088f848
Error Number:515,State:2,Class:16
2025-07-29 10:06:56 [Error] HuaLingErpApp.Controller.VendorsController: Failed to create vendor
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.vendor'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.VendorsController.Create(Vendor model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\VendorsController.cs:line 90
ClientConnectionId:3202d8d5-2afd-45f7-a092-d4c87088f848
Error Number:515,State:2,Class:16
2025-07-29 10:07:29 [Error] HuaLingErpApp.Controller.VendorsController: Failed to create vendor
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.vendor'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.VendorsController.Create(Vendor model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\VendorsController.cs:line 90
ClientConnectionId:da2e0327-755d-46c1-b602-ebdf7a94a401
Error Number:515,State:2,Class:16
2025-07-29 10:40:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 caught stopping signal...
2025-07-29 10:40:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a caught stopping signal...
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a caught stopped signal...
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 caught stopped signal...
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a All dispatchers stopped
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a successfully reported itself as stopped in 26.3926 ms
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:58930c5a has been stopped in total 777.4188 ms
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 All dispatchers stopped
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 successfully reported itself as stopped in 1.1856 ms
2025-07-29 10:40:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8260:10f29664 has been stopped in total 962.7476 ms
2025-07-29 10:41:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:41:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:41:06 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:41:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:41:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 successfully announced in 106.6268 ms
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c successfully announced in 106.6546 ms
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 all the dispatchers started
2025-07-29 10:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c all the dispatchers started
2025-07-29 10:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c caught stopping signal...
2025-07-29 10:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 caught stopping signal...
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 caught stopped signal...
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c caught stopped signal...
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c All dispatchers stopped
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c successfully reported itself as stopped in 1.928 ms
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:8350851c has been stopped in total 800.0994 ms
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 All dispatchers stopped
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 successfully reported itself as stopped in 0.7639 ms
2025-07-29 10:42:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23244:616545d8 has been stopped in total 855.5784 ms
2025-07-29 10:42:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:42:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:42:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:42:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:42:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:42:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:42:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 successfully announced in 102.0787 ms
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e successfully announced in 102.0574 ms
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 all the dispatchers started
2025-07-29 10:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e all the dispatchers started
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e caught stopping signal...
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 caught stopping signal...
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 All dispatchers stopped
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 successfully reported itself as stopped in 1.6198 ms
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:1a4a5fe8 has been stopped in total 318.3042 ms
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e All dispatchers stopped
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e successfully reported itself as stopped in 0.6862 ms
2025-07-29 10:45:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16564:21542e8e has been stopped in total 364.2403 ms
2025-07-29 10:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:46:03 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:46:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:46:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 successfully announced in 104.0008 ms
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 successfully announced in 103.6861 ms
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 all the dispatchers started
2025-07-29 10:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 all the dispatchers started
2025-07-29 10:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 caught stopping signal...
2025-07-29 10:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 caught stopping signal...
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 caught stopped signal...
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 caught stopped signal...
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 All dispatchers stopped
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 successfully reported itself as stopped in 1.9353 ms
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:92d75bf8 has been stopped in total 927.2913 ms
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 All dispatchers stopped
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 successfully reported itself as stopped in 0.6672 ms
2025-07-29 10:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21604:2bff8323 has been stopped in total 928.1904 ms
2025-07-29 10:47:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 10:47:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 10:47:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 10:47:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 10:47:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 successfully announced in 108.5662 ms
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 successfully announced in 108.8882 ms
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 all the dispatchers started
2025-07-29 10:47:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 all the dispatchers started
2025-07-29 11:01:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 caught stopping signal...
2025-07-29 11:01:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 caught stopping signal...
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 caught stopped signal...
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 caught stopped signal...
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 All dispatchers stopped
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 successfully reported itself as stopped in 3.2745 ms
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:0ef4c9d4 has been stopped in total 698.0643 ms
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 All dispatchers stopped
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 successfully reported itself as stopped in 2.5776 ms
2025-07-29 11:01:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7444:010299b8 has been stopped in total 916.1981 ms
2025-07-29 11:01:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:01:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:01:57 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:01:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:01:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 successfully announced in 103.8561 ms
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a successfully announced in 103.9169 ms
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a all the dispatchers started
2025-07-29 11:01:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 all the dispatchers started
2025-07-29 11:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a caught stopping signal...
2025-07-29 11:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 caught stopping signal...
2025-07-29 11:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 All dispatchers stopped
2025-07-29 11:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 successfully reported itself as stopped in 14.8165 ms
2025-07-29 11:29:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e4d37716 has been stopped in total 365.6653 ms
2025-07-29 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a caught stopped signal...
2025-07-29 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a All dispatchers stopped
2025-07-29 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a successfully reported itself as stopped in 0.7837 ms
2025-07-29 11:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:e35e771a has been stopped in total 631.3539 ms
2025-07-29 11:29:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:29:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:29:52 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:29:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:29:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:29:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 successfully announced in 114.0402 ms
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f successfully announced in 114.0669 ms
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f all the dispatchers started
2025-07-29 11:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 all the dispatchers started
2025-07-29 11:37:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f caught stopping signal...
2025-07-29 11:37:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 caught stopping signal...
2025-07-29 11:37:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f All dispatchers stopped
2025-07-29 11:37:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f successfully reported itself as stopped in 1.8422 ms
2025-07-29 11:37:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:e85fb73f has been stopped in total 159.153 ms
2025-07-29 11:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 caught stopped signal...
2025-07-29 11:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 All dispatchers stopped
2025-07-29 11:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 successfully reported itself as stopped in 0.8108 ms
2025-07-29 11:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23736:d032b781 has been stopped in total 868.5054 ms
2025-07-29 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:37:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:37:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e successfully announced in 108.9201 ms
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec successfully announced in 108.9131 ms
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e all the dispatchers started
2025-07-29 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec all the dispatchers started
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec caught stopping signal...
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e caught stopping signal...
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e All dispatchers stopped
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec All dispatchers stopped
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e successfully reported itself as stopped in 1.7513 ms
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:bbdb7a3e has been stopped in total 112.0961 ms
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec successfully reported itself as stopped in 1.773 ms
2025-07-29 11:39:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18804:3a8632ec has been stopped in total 112.7673 ms
2025-07-29 11:39:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:39:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:39:21 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:39:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:39:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba successfully announced in 106.3373 ms
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 successfully announced in 108.5405 ms
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba all the dispatchers started
2025-07-29 11:39:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 all the dispatchers started
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba caught stopping signal...
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 caught stopping signal...
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 All dispatchers stopped
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 successfully reported itself as stopped in 1.6473 ms
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:6022b332 has been stopped in total 401.7452 ms
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba All dispatchers stopped
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba successfully reported itself as stopped in 0.6162 ms
2025-07-29 11:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20780:f2bcedba has been stopped in total 403.4266 ms
2025-07-29 11:42:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:42:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:42:40 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:42:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:42:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c successfully announced in 106.0471 ms
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 successfully announced in 87.191 ms
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 all the dispatchers started
2025-07-29 11:42:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c all the dispatchers started
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c caught stopping signal...
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 caught stopping signal...
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c All dispatchers stopped
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c successfully reported itself as stopped in 1.5421 ms
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:a325959c has been stopped in total 48.0736 ms
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 All dispatchers stopped
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 successfully reported itself as stopped in 0.9039 ms
2025-07-29 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8760:bc8e12e8 has been stopped in total 132.1833 ms
2025-07-29 11:46:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:46:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:46:25 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:46:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:46:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 successfully announced in 105.4598 ms
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f successfully announced in 105.4624 ms
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f all the dispatchers started
2025-07-29 11:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 all the dispatchers started
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f caught stopping signal...
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 caught stopping signal...
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 All dispatchers stopped
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f All dispatchers stopped
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f successfully reported itself as stopped in 1.6447 ms
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:c854109f has been stopped in total 41.7675 ms
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 successfully reported itself as stopped in 1.6872 ms
2025-07-29 11:48:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24668:8d33eb09 has been stopped in total 41.6239 ms
2025-07-29 11:48:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:48:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:48:56 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:48:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:48:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 successfully announced in 109.9618 ms
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 successfully announced in 109.9794 ms
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 all the dispatchers started
2025-07-29 11:48:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 all the dispatchers started
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 caught stopping signal...
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 caught stopping signal...
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 All dispatchers stopped
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 All dispatchers stopped
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 successfully reported itself as stopped in 1.5454 ms
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:f90ef141 has been stopped in total 79.4208 ms
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 successfully reported itself as stopped in 0.6093 ms
2025-07-29 11:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19288:9d042819 has been stopped in total 79.4131 ms
2025-07-29 11:52:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 11:52:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 11:52:10 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 11:52:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 11:52:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 11:52:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 11:52:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 successfully announced in 108.2031 ms
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 successfully announced in 108.0635 ms
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 all the dispatchers started
2025-07-29 11:52:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 all the dispatchers started
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 caught stopping signal...
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 caught stopping signal...
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 All dispatchers stopped
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 successfully reported itself as stopped in 2.7447 ms
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:5fd80fe1 has been stopped in total 235.9405 ms
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 All dispatchers stopped
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 successfully reported itself as stopped in 0.6957 ms
2025-07-29 13:06:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17012:4ac0cf96 has been stopped in total 242.9373 ms
2025-07-29 13:12:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 13:12:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 13:12:40 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 13:12:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 13:12:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 13:12:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:12:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb successfully announced in 111.6128 ms
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 successfully announced in 111.5992 ms
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 all the dispatchers started
2025-07-29 13:12:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb all the dispatchers started
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb caught stopping signal...
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 caught stopping signal...
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 All dispatchers stopped
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 successfully reported itself as stopped in 2.2464 ms
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ea7aabe8 has been stopped in total 348.7632 ms
2025-07-29 13:21:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb caught stopped signal...
2025-07-29 13:21:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb All dispatchers stopped
2025-07-29 13:21:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb successfully reported itself as stopped in 0.8954 ms
2025-07-29 13:21:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13840:ab4ceeeb has been stopped in total 936.4506 ms
2025-07-29 13:21:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 13:21:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 13:21:55 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 13:21:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:21:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 successfully announced in 105.0546 ms
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 successfully announced in 105.0974 ms
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 all the dispatchers started
2025-07-29 13:21:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 all the dispatchers started
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 caught stopping signal...
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 caught stopping signal...
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 caught stopped signal...
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 caught stopped signal...
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 All dispatchers stopped
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 successfully reported itself as stopped in 1.7561 ms
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:08b22174 has been stopped in total 805.5049 ms
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 All dispatchers stopped
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 successfully reported itself as stopped in 0.6408 ms
2025-07-29 13:24:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13452:7c301f37 has been stopped in total 832.8084 ms
2025-07-29 13:42:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 13:42:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 13:42:49 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 13:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 13:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 successfully announced in 108.3174 ms
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce successfully announced in 108.3075 ms
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 all the dispatchers started
2025-07-29 13:42:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce all the dispatchers started
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce caught stopping signal...
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 caught stopping signal...
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 All dispatchers stopped
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 successfully reported itself as stopped in 1.8745 ms
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c994bf9 has been stopped in total 73.5862 ms
2025-07-29 14:01:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce caught stopped signal...
2025-07-29 14:01:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce All dispatchers stopped
2025-07-29 14:01:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce successfully reported itself as stopped in 0.8218 ms
2025-07-29 14:01:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16248:0c5937ce has been stopped in total 850.9614 ms
2025-07-29 14:01:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:01:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:01:35 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:01:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:01:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac successfully announced in 108.4181 ms
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b successfully announced in 108.3086 ms
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac all the dispatchers started
2025-07-29 14:01:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b all the dispatchers started
2025-07-29 14:05:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac caught stopping signal...
2025-07-29 14:05:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b caught stopping signal...
2025-07-29 14:05:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b caught stopped signal...
2025-07-29 14:05:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac caught stopped signal...
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac All dispatchers stopped
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac successfully reported itself as stopped in 2.1181 ms
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:3e2d89ac has been stopped in total 901.5266 ms
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b All dispatchers stopped
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b successfully reported itself as stopped in 1.1856 ms
2025-07-29 14:05:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22440:96670a4b has been stopped in total 939.743 ms
2025-07-29 14:05:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:05:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:05:55 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:05:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:05:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:05:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:05:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 successfully announced in 107.3299 ms
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c successfully announced in 107.3017 ms
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c all the dispatchers started
2025-07-29 14:05:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 all the dispatchers started
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c caught stopping signal...
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 caught stopping signal...
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c caught stopped signal...
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 caught stopped signal...
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c All dispatchers stopped
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 All dispatchers stopped
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c successfully reported itself as stopped in 2.3709 ms
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:330f291c has been stopped in total 706.7806 ms
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 successfully reported itself as stopped in 1.8353 ms
2025-07-29 14:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22364:ba1027a0 has been stopped in total 706.467 ms
2025-07-29 14:07:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:07:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:07:10 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:07:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:07:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c successfully announced in 108.7582 ms
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 successfully announced in 108.7483 ms
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 all the dispatchers started
2025-07-29 14:07:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c all the dispatchers started
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 caught stopping signal...
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c caught stopping signal...
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c caught stopped signal...
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 caught stopped signal...
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 All dispatchers stopped
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c All dispatchers stopped
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 successfully reported itself as stopped in 1.1083 ms
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c successfully reported itself as stopped in 2.0709 ms
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:28db9b44 has been stopped in total 668.5257 ms
2025-07-29 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21272:e3c90c2c has been stopped in total 668.3304 ms
2025-07-29 14:08:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:08:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:08:16 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:08:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:08:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 successfully announced in 102.0677 ms
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 successfully announced in 102.1695 ms
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 all the dispatchers started
2025-07-29 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 all the dispatchers started
2025-07-29 14:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 caught stopping signal...
2025-07-29 14:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 caught stopping signal...
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 caught stopped signal...
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 caught stopped signal...
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 All dispatchers stopped
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 successfully reported itself as stopped in 2.5856 ms
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:05b0ccd2 has been stopped in total 963.2184 ms
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 All dispatchers stopped
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 successfully reported itself as stopped in 0.7965 ms
2025-07-29 14:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12448:34dbd712 has been stopped in total 975.5533 ms
2025-07-29 14:09:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:09:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:09:24 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:09:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:09:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:09:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:09:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 successfully announced in 116.753 ms
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 successfully announced in 116.7435 ms
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 all the dispatchers started
2025-07-29 14:09:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 all the dispatchers started
2025-07-29 14:14:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 caught stopping signal...
2025-07-29 14:14:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 caught stopping signal...
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 caught stopped signal...
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 caught stopped signal...
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 All dispatchers stopped
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 All dispatchers stopped
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 successfully reported itself as stopped in 1.0746 ms
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:3260c8f2 has been stopped in total 972.0344 ms
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 successfully reported itself as stopped in 2.5105 ms
2025-07-29 14:14:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:240f9b83 has been stopped in total 971.9069 ms
2025-07-29 14:14:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:14:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:14:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:14:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:14:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 successfully announced in 108.1888 ms
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 successfully announced in 108.2012 ms
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 all the dispatchers started
2025-07-29 14:14:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 all the dispatchers started
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 caught stopping signal...
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 caught stopping signal...
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 All dispatchers stopped
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 successfully reported itself as stopped in 3.0228 ms
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:d8514f48 has been stopped in total 84.8871 ms
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 All dispatchers stopped
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 successfully reported itself as stopped in 1.0559 ms
2025-07-29 14:20:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24408:c83f3874 has been stopped in total 355.095 ms
2025-07-29 14:20:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:20:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:20:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:20:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:20:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:20:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:20:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 successfully announced in 110.6338 ms
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 successfully announced in 110.6913 ms
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 all the dispatchers started
2025-07-29 14:20:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 all the dispatchers started
2025-07-29 14:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 caught stopping signal...
2025-07-29 14:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 caught stopping signal...
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 caught stopped signal...
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 caught stopped signal...
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 All dispatchers stopped
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 successfully reported itself as stopped in 1.6312 ms
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:ca62e222 has been stopped in total 864.8231 ms
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 All dispatchers stopped
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 successfully reported itself as stopped in 0.7386 ms
2025-07-29 14:22:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17496:665d4e72 has been stopped in total 890.727 ms
2025-07-29 14:22:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:22:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:22:15 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:22:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:22:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:22:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:22:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:22:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:22:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 successfully announced in 106.1358 ms
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 successfully announced in 106.1322 ms
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 all the dispatchers started
2025-07-29 14:22:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 all the dispatchers started
2025-07-29 14:39:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 caught stopping signal...
2025-07-29 14:39:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 caught stopping signal...
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 All dispatchers stopped
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 successfully reported itself as stopped in 1.9499 ms
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:b671d153 has been stopped in total 110.4824 ms
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 caught stopped signal...
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 All dispatchers stopped
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 successfully reported itself as stopped in 0.8298 ms
2025-07-29 14:39:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22472:e21508f2 has been stopped in total 625.7271 ms
2025-07-29 14:40:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 14:40:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 14:40:13 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 14:40:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 14:40:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 14:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 14:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 successfully announced in 109.8262 ms
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd successfully announced in 109.8354 ms
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd all the dispatchers started
2025-07-29 14:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 all the dispatchers started
2025-07-29 14:40:57 [Error] HuaLingErpApp.Controller.CustomerController: Failed to create customer
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.customer'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.CustomerController.Create(Customer model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerController.cs:line 100
ClientConnectionId:157667d9-9b49-4015-a1a7-07d18485bc8b
Error Number:515,State:2,Class:16
2025-07-29 14:41:06 [Error] HuaLingErpApp.Controller.CustomerController: Failed to create customer
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.customer'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.CustomerController.Create(Customer model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerController.cs:line 100
ClientConnectionId:ee7a16cc-83ef-49a6-8fcd-d52d261ff051
Error Number:515,State:2,Class:16
2025-07-29 14:41:16 [Error] HuaLingErpApp.Controller.CustomerController: Failed to create customer
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.customer'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.CustomerController.Create(Customer model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerController.cs:line 100
ClientConnectionId:157667d9-9b49-4015-a1a7-07d18485bc8b
Error Number:515,State:2,Class:16
2025-07-29 14:41:18 [Error] HuaLingErpApp.Controller.CustomerController: Failed to create customer
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'id'，表 '0412.dbo.customer'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.AuditableRepository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 98
   at HuaLingErpApp.Controller.CustomerController.Create(Customer model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerController.cs:line 100
ClientConnectionId:157667d9-9b49-4015-a1a7-07d18485bc8b
Error Number:515,State:2,Class:16
2025-07-29 14:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd caught stopping signal...
2025-07-29 14:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 caught stopping signal...
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd All dispatchers stopped
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 All dispatchers stopped
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd successfully reported itself as stopped in 1.5975 ms
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 successfully reported itself as stopped in 0.7731 ms
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:e09ddcbd has been stopped in total 280.5913 ms
2025-07-29 14:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9692:bb4e30a9 has been stopped in total 280.4381 ms
2025-07-29 15:01:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:01:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:01:44 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:01:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:01:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae successfully announced in 106.4212 ms
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 successfully announced in 106.5297 ms
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 all the dispatchers started
2025-07-29 15:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae all the dispatchers started
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae caught stopping signal...
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 caught stopping signal...
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 All dispatchers stopped
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 successfully reported itself as stopped in 2.213 ms
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:03ebb801 has been stopped in total 240.2336 ms
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae All dispatchers stopped
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae successfully reported itself as stopped in 0.6206 ms
2025-07-29 15:33:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:d73092ae has been stopped in total 250.775 ms
2025-07-29 15:42:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:42:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:42:51 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:42:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:42:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:42:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:42:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 successfully announced in 156.1668 ms
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 successfully announced in 63.2826 ms
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 all the dispatchers started
2025-07-29 15:42:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 all the dispatchers started
2025-07-29 15:43:06 [Error] HuaLingErpApp.Controller.CustomerOrderController: Failed to retrieve customer order list
System.ArgumentException: Requested value 'A' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value)
   at SqlSugar.DbConvert.EnumToStringConvert.QueryConverter[T](IDataRecord dr, Int32 i)
   at SqlSugarEntity(IDataRecord)
   at SqlSugar.IDataReaderEntityBuilder`1.Build(IDataRecord dataRecord)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.AuditableRepository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 81
   at HuaLingErpApp.Controller.CustomerOrderController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerOrderController.cs:line 24
2025-07-29 15:43:17 [Error] HuaLingErpApp.Controller.CustomerOrderController: Failed to retrieve customer order list
System.ArgumentException: Requested value 'A' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value)
   at SqlSugar.DbConvert.EnumToStringConvert.QueryConverter[T](IDataRecord dr, Int32 i)
   at SqlSugarEntity(IDataRecord)
   at SqlSugar.IDataReaderEntityBuilder`1.Build(IDataRecord dataRecord)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.AuditableRepository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 81
   at HuaLingErpApp.Controller.CustomerOrderController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerOrderController.cs:line 24
2025-07-29 15:44:07 [Error] HuaLingErpApp.Controller.CustomerOrderController: Failed to retrieve customer order list
System.ArgumentException: Requested value 'A' was not found.
   at System.Enum.TryParseByName[TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TStorage& result)
   at System.Enum.TryParseByValueOrName[TUnderlying,TStorage](RuntimeType enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, TUnderlying& result)
   at System.Enum.TryParse(Type enumType, ReadOnlySpan`1 value, Boolean ignoreCase, Boolean throwOnFailure, Object& result)
   at System.Enum.Parse(Type enumType, String value)
   at SqlSugar.DbConvert.EnumToStringConvert.QueryConverter[T](IDataRecord dr, Int32 i)
   at SqlSugarEntity(IDataRecord)
   at SqlSugar.IDataReaderEntityBuilder`1.Build(IDataRecord dataRecord)
   at SqlSugar.DbBindAccessory.GetEntityListAsync[T](SqlSugarProvider context, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.AuditableRepository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\AuditableRepository.cs:line 81
   at HuaLingErpApp.Controller.CustomerOrderController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\CustomerOrderController.cs:line 24
2025-07-29 15:47:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 caught stopping signal...
2025-07-29 15:47:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 caught stopping signal...
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 All dispatchers stopped
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 successfully reported itself as stopped in 2.3354 ms
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:f913b290 has been stopped in total 254.528 ms
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 All dispatchers stopped
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 successfully reported itself as stopped in 0.7192 ms
2025-07-29 15:47:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9564:a279b820 has been stopped in total 384.4199 ms
2025-07-29 15:47:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:47:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:47:52 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:47:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb successfully announced in 105.6547 ms
2025-07-29 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 successfully announced in 105.6617 ms
2025-07-29 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 all the dispatchers started
2025-07-29 15:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb all the dispatchers started
2025-07-29 15:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 caught stopping signal...
2025-07-29 15:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb caught stopping signal...
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb caught stopped signal...
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 caught stopped signal...
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb All dispatchers stopped
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb successfully reported itself as stopped in 1.7583 ms
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:d2ee13cb has been stopped in total 698.634 ms
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 All dispatchers stopped
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 successfully reported itself as stopped in 1.0838 ms
2025-07-29 15:55:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7980:9546a616 has been stopped in total 841.8585 ms
2025-07-29 15:55:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:55:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:55:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:55:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:55:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 successfully announced in 105.6855 ms
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd successfully announced in 105.6551 ms
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 all the dispatchers started
2025-07-29 15:55:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd all the dispatchers started
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd caught stopping signal...
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 caught stopping signal...
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 caught stopped signal...
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd caught stopped signal...
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 All dispatchers stopped
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd All dispatchers stopped
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 successfully reported itself as stopped in 1.9708 ms
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3819c015 has been stopped in total 769.7115 ms
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd successfully reported itself as stopped in 4.2872 ms
2025-07-29 15:56:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7748:3a9d8ecd has been stopped in total 772.6244 ms
2025-07-29 15:56:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:56:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:56:19 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:56:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:56:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 successfully announced in 111.272 ms
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d successfully announced in 112.0554 ms
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d all the dispatchers started
2025-07-29 15:56:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 all the dispatchers started
2025-07-29 15:57:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d caught stopping signal...
2025-07-29 15:57:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 caught stopping signal...
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d All dispatchers stopped
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d successfully reported itself as stopped in 4.8709 ms
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:5954c80d has been stopped in total 42.0013 ms
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 caught stopped signal...
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 All dispatchers stopped
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 successfully reported itself as stopped in 4.6936 ms
2025-07-29 15:57:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20484:b935ac84 has been stopped in total 977.7147 ms
2025-07-29 15:57:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 15:57:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 15:57:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 15:57:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 15:57:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 successfully announced in 113.0516 ms
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 successfully announced in 113.0447 ms
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 all the dispatchers started
2025-07-29 15:57:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 all the dispatchers started
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 caught stopping signal...
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 caught stopping signal...
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 All dispatchers stopped
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 successfully reported itself as stopped in 3.0653 ms
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:d8c00f71 has been stopped in total 275.4866 ms
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 All dispatchers stopped
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 successfully reported itself as stopped in 0.7503 ms
2025-07-29 16:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21408:5be48e56 has been stopped in total 445.5617 ms
2025-07-29 16:37:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 16:37:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 16:37:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 16:37:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 16:37:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:37:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:37:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:37:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:37:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 successfully announced in 107.981 ms
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a successfully announced in 107.9924 ms
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a all the dispatchers started
2025-07-29 16:37:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 all the dispatchers started
2025-07-29 16:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 caught stopping signal...
2025-07-29 16:41:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a caught stopping signal...
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a caught stopped signal...
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 caught stopped signal...
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a All dispatchers stopped
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 All dispatchers stopped
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 successfully reported itself as stopped in 2.1379 ms
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:757cc9a6 has been stopped in total 898.3192 ms
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a successfully reported itself as stopped in 0.6844 ms
2025-07-29 16:41:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9020:f700b46a has been stopped in total 898.0814 ms
2025-07-29 16:41:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 16:42:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 16:42:00 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 16:42:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 16:42:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 16:42:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 16:42:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 16:42:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:42:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b successfully announced in 112.9307 ms
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e successfully announced in 113.0183 ms
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b all the dispatchers started
2025-07-29 16:42:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e all the dispatchers started
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b caught stopping signal...
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e caught stopping signal...
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e All dispatchers stopped
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e successfully reported itself as stopped in 1.9049 ms
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:4abcb17e has been stopped in total 63.2848 ms
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b caught stopped signal...
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b All dispatchers stopped
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b successfully reported itself as stopped in 0.7903 ms
2025-07-29 16:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5748:a10d633b has been stopped in total 833.3701 ms
2025-07-29 16:56:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-29 16:56:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-29 16:56:53 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-29 16:56:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-29 16:56:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a successfully announced in 109.8753 ms
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 successfully announced in 109.8618 ms
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a all the dispatchers started
2025-07-29 16:56:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 all the dispatchers started
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a caught stopping signal...
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 caught stopping signal...
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 caught stopped signal...
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a caught stopped signal...
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 All dispatchers stopped
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 successfully reported itself as stopped in 2.4402 ms
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:172819b7 has been stopped in total 646.7996 ms
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a All dispatchers stopped
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a successfully reported itself as stopped in 0.9973 ms
2025-07-29 17:02:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11020:84c55b6a has been stopped in total 662.2181 ms
