2025-07-14 08:03:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:03:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:03:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 successfully announced in 117.1066 ms
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d successfully announced in 219.6869 ms
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 all the dispatchers started
2025-07-14 08:03:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d all the dispatchers started
2025-07-14 08:04:07 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidCastException: Unable to cast object of type 'HuaLingErpApp.Shared.Models.EnumStatus' to type 'System.String'.
   at System.ComponentModel.DataAnnotations.StringLengthAttribute.IsValid(Object value)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.IsValid(Object value, ValidationContext validationContext)
   at System.ComponentModel.DataAnnotations.ValidationAttribute.GetValidationResult(Object value, ValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.DataAnnotations.DataAnnotationsModelValidator.Validate(ModelValidationContext validationContext)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.ValidateNode()
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitChildren(IValidationStrategy strategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitComplexType(IValidationStrategy defaultStrategy)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.VisitImplementation(ModelMetadata& metadata, String& key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.Validation.ValidationVisitor.Visit(ModelMetadata metadata, String key, Object model)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ObjectModelValidator.Validate(ActionContext actionContext, ValidationStateDictionary validationState, String prefix, Object model, ModelMetadata metadata, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.EnforceBindRequiredAndValidate(ObjectModelValidator baseObjectValidator, ActionContext actionContext, ParameterDescriptor parameter, ModelMetadata metadata, ModelBindingContext modelBindingContext, ModelBindingResult modelBindingResult, Object container)
   at Microsoft.AspNetCore.Mvc.ModelBinding.ParameterBinder.BindModelAsync(ActionContext actionContext, IModelBinder modelBinder, IValueProvider valueProvider, ParameterDescriptor parameter, ModelMetadata metadata, Object value, Object container)
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerBinderDelegateProvider.<>c__DisplayClass0_0.<<CreateBinderDelegate>g__Bind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d caught stopping signal...
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 caught stopping signal...
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 caught stopped signal...
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d caught stopped signal...
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 All dispatchers stopped
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 successfully reported itself as stopped in 3.8655 ms
2025-07-14 08:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:d70909e6 has been stopped in total 765.492 ms
2025-07-14 08:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d All dispatchers stopped
2025-07-14 08:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d successfully reported itself as stopped in 1.2582 ms
2025-07-14 08:08:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19692:f6854f0d has been stopped in total 993.7147 ms
2025-07-14 08:08:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:08:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:08:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 successfully announced in 107.9264 ms
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 successfully announced in 107.9198 ms
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 all the dispatchers started
2025-07-14 08:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 all the dispatchers started
2025-07-14 08:20:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 caught stopping signal...
2025-07-14 08:20:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 caught stopping signal...
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 All dispatchers stopped
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 successfully reported itself as stopped in 2.4878 ms
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:846c39b1 has been stopped in total 433.536 ms
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 All dispatchers stopped
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 successfully reported itself as stopped in 0.7361 ms
2025-07-14 08:20:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17276:07c26eb3 has been stopped in total 558.6068 ms
2025-07-14 08:27:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:27:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f successfully announced in 138.847 ms
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 successfully announced in 140.0448 ms
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f all the dispatchers started
2025-07-14 08:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 all the dispatchers started
2025-07-14 08:27:34 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "类型 PurchaseOrder 未找到  属性，无法获取其值"
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-14 08:27:34 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "类型 PurchaseOrder 未找到  属性，无法获取其值"
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-14 08:27:34 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"zqyvKQu7OMHFSHN0hswy0fF7Ziq4ddPVlpmTCpNkMJY"'.
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-14 08:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 caught stopping signal...
2025-07-14 08:28:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f caught stopping signal...
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f caught stopped signal...
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 caught stopped signal...
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f All dispatchers stopped
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 All dispatchers stopped
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f successfully reported itself as stopped in 2.0683 ms
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:d624d26f has been stopped in total 583.2663 ms
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 successfully reported itself as stopped in 2.3838 ms
2025-07-14 08:28:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20528:40778138 has been stopped in total 583.828 ms
2025-07-14 08:28:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:28:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:28:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 successfully announced in 104.9564 ms
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 successfully announced in 99.1507 ms
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 all the dispatchers started
2025-07-14 08:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 all the dispatchers started
2025-07-14 08:28:28 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "类型 PurchaseOrder 未找到  属性，无法获取其值"
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-14 08:28:28 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "类型 PurchaseOrder 未找到  属性，无法获取其值"
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-14 08:28:28 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"JPKnZV7bxgRqrNSYMP9AhapubJYncHL0wN6lsieBKmo"'.
System.InvalidOperationException: 类型 PurchaseOrder 未找到  属性，无法获取其值
   at System.Linq.LambdaExtensions.<GetPropertyValueLambda>g__GetSimplePropertyExpression|36_0[TModel,TResult](<>c__DisplayClass36_0`2&)
   at System.Linq.LambdaExtensions.GetPropertyValueLambda[TModel,TResult](TModel model, String propertyName)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_1`2.<GetPropertyValue>b__1(ICacheEntry entry)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass13_0`1.<GetOrCreate>b__0(ICacheEntry entry)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory, MemoryCacheEntryOptions createOptions)
   at Microsoft.Extensions.Caching.Memory.CacheExtensions.GetOrCreate[TItem](IMemoryCache cache, Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.GetOrCreate[TItem](Object key, Func`2 factory)
   at BootstrapBlazor.Components.CacheManager.<>c__DisplayClass35_0`2.<GetPropertyValue>g__GetValue|0()
   at BootstrapBlazor.Components.CacheManager.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.Utility.GetPropertyValue[TModel,TResult](TModel model, String fieldName)
   at BootstrapBlazor.Components.TableColumn`2.<>c__DisplayClass185_0.<BootstrapBlazor.Components.ITableColumn.get_Template>b__1(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass481_0.<GetValue>b__0(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<>c__DisplayClass1615_2.<get_RenderRow>b__9(RenderTreeBuilder __builder5)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.DynamicElement.BuildRenderTree(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 caught stopping signal...
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 caught stopping signal...
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 caught stopped signal...
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 caught stopped signal...
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 All dispatchers stopped
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 successfully reported itself as stopped in 2.0452 ms
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:1dce6db2 has been stopped in total 930.7548 ms
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 All dispatchers stopped
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 successfully reported itself as stopped in 0.8606 ms
2025-07-14 08:29:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2144:5b1bdd15 has been stopped in total 945.2695 ms
2025-07-14 08:43:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:43:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:43:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 successfully announced in 99.9469 ms
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b successfully announced in 80.5921 ms
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 all the dispatchers started
2025-07-14 08:43:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b all the dispatchers started
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 caught stopping signal...
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b caught stopping signal...
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b caught stopped signal...
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 caught stopped signal...
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 All dispatchers stopped
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 successfully reported itself as stopped in 2.6699 ms
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:3c218e13 has been stopped in total 531.4018 ms
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b All dispatchers stopped
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b successfully reported itself as stopped in 0.9028 ms
2025-07-14 08:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20612:8288c88b has been stopped in total 564.9935 ms
2025-07-14 08:45:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:45:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:45:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 successfully announced in 128.0268 ms
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 successfully announced in 125.6657 ms
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 all the dispatchers started
2025-07-14 08:45:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 all the dispatchers started
2025-07-14 08:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 caught stopping signal...
2025-07-14 08:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 caught stopping signal...
2025-07-14 08:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 caught stopped signal...
2025-07-14 08:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 caught stopped signal...
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 All dispatchers stopped
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 successfully reported itself as stopped in 2.7663 ms
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:4e039782 has been stopped in total 750.7605 ms
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 All dispatchers stopped
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 successfully reported itself as stopped in 0.8412 ms
2025-07-14 08:51:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10504:104e2d26 has been stopped in total 799.5784 ms
2025-07-14 08:51:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:51:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:51:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 successfully announced in 95.8707 ms
2025-07-14 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b successfully announced in 104.2668 ms
2025-07-14 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b all the dispatchers started
2025-07-14 08:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 all the dispatchers started
2025-07-14 08:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 caught stopping signal...
2025-07-14 08:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b caught stopping signal...
2025-07-14 08:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b caught stopped signal...
2025-07-14 08:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 caught stopped signal...
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b All dispatchers stopped
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b successfully reported itself as stopped in 2.4827 ms
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:44d90c7b has been stopped in total 871.1538 ms
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 All dispatchers stopped
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 successfully reported itself as stopped in 1.8386 ms
2025-07-14 08:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20940:729fc3e4 has been stopped in total 913.8865 ms
2025-07-14 08:54:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:54:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:54:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df successfully announced in 106.4656 ms
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 successfully announced in 106.4165 ms
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 all the dispatchers started
2025-07-14 08:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df all the dispatchers started
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df caught stopping signal...
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 caught stopping signal...
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 All dispatchers stopped
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 successfully reported itself as stopped in 2.1134 ms
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:13715388 has been stopped in total 253.1467 ms
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df All dispatchers stopped
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df successfully reported itself as stopped in 0.8042 ms
2025-07-14 08:56:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16680:b34415df has been stopped in total 315.5086 ms
2025-07-14 08:56:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:56:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:56:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 successfully announced in 119.5531 ms
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 successfully announced in 121.1608 ms
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 all the dispatchers started
2025-07-14 08:56:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 all the dispatchers started
2025-07-14 08:57:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 caught stopping signal...
2025-07-14 08:57:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 caught stopping signal...
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 All dispatchers stopped
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 successfully reported itself as stopped in 1.7513 ms
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:18bad4c6 has been stopped in total 234.4763 ms
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 All dispatchers stopped
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 successfully reported itself as stopped in 0.8822 ms
2025-07-14 08:57:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23948:cf1b6950 has been stopped in total 237.328 ms
2025-07-14 08:57:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 08:57:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 08:57:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 08:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 successfully announced in 101.0007 ms
2025-07-14 08:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 successfully announced in 104.4965 ms
2025-07-14 08:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 08:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 all the dispatchers started
2025-07-14 08:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 all the dispatchers started
2025-07-14 09:04:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 caught stopping signal...
2025-07-14 09:04:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 caught stopping signal...
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 caught stopped signal...
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 caught stopped signal...
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 All dispatchers stopped
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 All dispatchers stopped
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 successfully reported itself as stopped in 1.829 ms
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 successfully reported itself as stopped in 2.3508 ms
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:cc60e9e3 has been stopped in total 562.871 ms
2025-07-14 09:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3748:84c63a04 has been stopped in total 562.7438 ms
2025-07-14 09:05:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:05:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 successfully announced in 87.121 ms
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd successfully announced in 109.7093 ms
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 all the dispatchers started
2025-07-14 09:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd all the dispatchers started
2025-07-14 09:10:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd caught stopping signal...
2025-07-14 09:10:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 caught stopping signal...
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 caught stopped signal...
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd caught stopped signal...
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 All dispatchers stopped
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd All dispatchers stopped
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 successfully reported itself as stopped in 1.5905 ms
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:062c19e2 has been stopped in total 664.3037 ms
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd successfully reported itself as stopped in 0.964 ms
2025-07-14 09:10:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18556:85444abd has been stopped in total 665.0347 ms
2025-07-14 09:10:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:10:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:10:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 successfully announced in 101.82 ms
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 successfully announced in 101.8196 ms
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 all the dispatchers started
2025-07-14 09:10:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 all the dispatchers started
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 caught stopping signal...
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 caught stopping signal...
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 All dispatchers stopped
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 All dispatchers stopped
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 successfully reported itself as stopped in 2.4193 ms
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:c6ed95d3 has been stopped in total 48.2957 ms
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 successfully reported itself as stopped in 1.2831 ms
2025-07-14 09:11:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20856:16ab3561 has been stopped in total 48.2532 ms
2025-07-14 09:11:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:11:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:4dffdcfb successfully announced in 249.4347 ms
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:837e2e7b successfully announced in 258.3932 ms
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:837e2e7b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:4dffdcfb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:4dffdcfb all the dispatchers started
2025-07-14 09:11:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10648:837e2e7b all the dispatchers started
2025-07-14 09:13:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:13:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:13:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 successfully announced in 102.3073 ms
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 successfully announced in 90.9459 ms
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 all the dispatchers started
2025-07-14 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 all the dispatchers started
2025-07-14 09:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 caught stopping signal...
2025-07-14 09:16:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 caught stopping signal...
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 All dispatchers stopped
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 successfully reported itself as stopped in 3.1506 ms
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:6ffa9ab7 has been stopped in total 464.1727 ms
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 All dispatchers stopped
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 successfully reported itself as stopped in 0.6884 ms
2025-07-14 09:16:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8448:47facff1 has been stopped in total 473.6855 ms
2025-07-14 09:17:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:17:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:17:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e successfully announced in 110.5898 ms
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc successfully announced in 110.563 ms
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e all the dispatchers started
2025-07-14 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc all the dispatchers started
2025-07-14 09:22:56 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-14 09:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc caught stopping signal...
2025-07-14 09:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e caught stopping signal...
2025-07-14 09:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e caught stopped signal...
2025-07-14 09:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc caught stopped signal...
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc All dispatchers stopped
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc successfully reported itself as stopped in 1.9763 ms
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:3ee680cc has been stopped in total 850.1117 ms
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e All dispatchers stopped
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e successfully reported itself as stopped in 0.7899 ms
2025-07-14 09:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23036:7cab968e has been stopped in total 854.3796 ms
2025-07-14 09:25:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:25:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:25:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d successfully announced in 97.8819 ms
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 successfully announced in 111.2449 ms
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 all the dispatchers started
2025-07-14 09:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d all the dispatchers started
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 caught stopping signal...
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d caught stopping signal...
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d caught stopped signal...
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 caught stopped signal...
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 All dispatchers stopped
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 successfully reported itself as stopped in 1.796 ms
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:84664458 has been stopped in total 676.7514 ms
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d All dispatchers stopped
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d successfully reported itself as stopped in 0.7119 ms
2025-07-14 09:28:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14880:d038591d has been stopped in total 696.4408 ms
2025-07-14 09:28:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:28:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 successfully announced in 105.8705 ms
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f successfully announced in 106.1256 ms
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f all the dispatchers started
2025-07-14 09:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 all the dispatchers started
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f caught stopping signal...
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 caught stopping signal...
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 All dispatchers stopped
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 successfully reported itself as stopped in 2.3504 ms
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:4c059d03 has been stopped in total 114.585 ms
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f All dispatchers stopped
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f successfully reported itself as stopped in 1.1303 ms
2025-07-14 09:30:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22680:c4e4460f has been stopped in total 161.5255 ms
2025-07-14 09:30:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:30:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:30:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 successfully announced in 103.6571 ms
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 successfully announced in 103.6923 ms
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 all the dispatchers started
2025-07-14 09:30:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 all the dispatchers started
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 caught stopping signal...
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 caught stopping signal...
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 All dispatchers stopped
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 successfully reported itself as stopped in 2.5765 ms
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:b4f5ea96 has been stopped in total 193.8846 ms
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 All dispatchers stopped
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 successfully reported itself as stopped in 0.7247 ms
2025-07-14 09:32:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17540:eeaff891 has been stopped in total 199.969 ms
2025-07-14 09:33:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:33:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:33:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:33:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d successfully announced in 103.5743 ms
2025-07-14 09:33:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 successfully announced in 97.8496 ms
2025-07-14 09:33:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:33:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:33:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d all the dispatchers started
2025-07-14 09:33:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 all the dispatchers started
2025-07-14 09:35:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d caught stopping signal...
2025-07-14 09:35:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 caught stopping signal...
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d All dispatchers stopped
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d successfully reported itself as stopped in 2.752 ms
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:c6cb948d has been stopped in total 160.9366 ms
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 All dispatchers stopped
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 successfully reported itself as stopped in 4.6144 ms
2025-07-14 09:35:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18100:e0fcb333 has been stopped in total 259.8661 ms
2025-07-14 09:37:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:37:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:37:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 successfully announced in 107.6941 ms
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 successfully announced in 108.1386 ms
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 all the dispatchers started
2025-07-14 09:37:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 all the dispatchers started
2025-07-14 09:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 caught stopping signal...
2025-07-14 09:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 caught stopping signal...
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 All dispatchers stopped
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 successfully reported itself as stopped in 2.7084 ms
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:42feec82 has been stopped in total 487.8231 ms
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 All dispatchers stopped
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 successfully reported itself as stopped in 0.7298 ms
2025-07-14 09:37:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:720fba13 has been stopped in total 492.0067 ms
2025-07-14 09:38:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:38:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:38:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 successfully announced in 109.1311 ms
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 successfully announced in 104.9443 ms
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 all the dispatchers started
2025-07-14 09:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 all the dispatchers started
2025-07-14 09:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 caught stopping signal...
2025-07-14 09:39:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 caught stopping signal...
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 caught stopped signal...
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 caught stopped signal...
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 All dispatchers stopped
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 successfully reported itself as stopped in 3.0557 ms
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:620e9c51 has been stopped in total 750.5015 ms
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 All dispatchers stopped
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 successfully reported itself as stopped in 0.8357 ms
2025-07-14 09:39:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9244:d35ecf57 has been stopped in total 781.9251 ms
2025-07-14 09:39:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:39:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb successfully announced in 107.6274 ms
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d successfully announced in 107.9678 ms
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d all the dispatchers started
2025-07-14 09:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb all the dispatchers started
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb caught stopping signal...
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d caught stopping signal...
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb All dispatchers stopped
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d All dispatchers stopped
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb successfully reported itself as stopped in 1.9397 ms
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:99abc2cb has been stopped in total 215.0856 ms
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d successfully reported itself as stopped in 0.8229 ms
2025-07-14 09:40:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23312:2422348d has been stopped in total 214.9061 ms
2025-07-14 09:45:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:45:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:45:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 successfully announced in 114.3901 ms
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 successfully announced in 114.4905 ms
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 all the dispatchers started
2025-07-14 09:45:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 all the dispatchers started
2025-07-14 09:45:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 caught stopping signal...
2025-07-14 09:45:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 caught stopping signal...
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 All dispatchers stopped
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 successfully reported itself as stopped in 13.5612 ms
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:ea995448 has been stopped in total 207.839 ms
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 All dispatchers stopped
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 successfully reported itself as stopped in 0.8368 ms
2025-07-14 09:45:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24400:2dbf81d6 has been stopped in total 262.0147 ms
2025-07-14 09:46:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:46:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:46:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f successfully announced in 103.3123 ms
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 successfully announced in 103.2259 ms
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 all the dispatchers started
2025-07-14 09:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f all the dispatchers started
2025-07-14 09:47:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 caught stopping signal...
2025-07-14 09:47:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f caught stopping signal...
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f All dispatchers stopped
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f successfully reported itself as stopped in 2.9913 ms
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:2166fb1f has been stopped in total 480.2907 ms
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 caught stopped signal...
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 All dispatchers stopped
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 successfully reported itself as stopped in 0.953 ms
2025-07-14 09:47:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18876:4381aee8 has been stopped in total 538.3697 ms
2025-07-14 09:49:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:49:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:49:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b successfully announced in 116.8824 ms
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 successfully announced in 116.7336 ms
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 all the dispatchers started
2025-07-14 09:49:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b all the dispatchers started
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 caught stopping signal...
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b caught stopping signal...
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b All dispatchers stopped
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b successfully reported itself as stopped in 2.5069 ms
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:c2c6ec9b has been stopped in total 377.555 ms
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 All dispatchers stopped
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 successfully reported itself as stopped in 1.2003 ms
2025-07-14 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16676:48bed741 has been stopped in total 448.191 ms
2025-07-14 09:53:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:53:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:53:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 successfully announced in 102.5704 ms
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 successfully announced in 101.7108 ms
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 all the dispatchers started
2025-07-14 09:53:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 all the dispatchers started
2025-07-14 09:54:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 caught stopping signal...
2025-07-14 09:54:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 caught stopping signal...
2025-07-14 09:54:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 caught stopped signal...
2025-07-14 09:54:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 caught stopped signal...
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 All dispatchers stopped
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 All dispatchers stopped
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 successfully reported itself as stopped in 2.7241 ms
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:ab38d2a9 has been stopped in total 916.1776 ms
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 successfully reported itself as stopped in 0.6455 ms
2025-07-14 09:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1940:c085a452 has been stopped in total 917.342 ms
2025-07-14 09:54:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:54:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 successfully announced in 115.9796 ms
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 successfully announced in 115.8319 ms
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 all the dispatchers started
2025-07-14 09:54:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 all the dispatchers started
2025-07-14 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 caught stopping signal...
2025-07-14 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 caught stopping signal...
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 caught stopped signal...
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 caught stopped signal...
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 All dispatchers stopped
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 successfully reported itself as stopped in 2.9909 ms
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:03f0b7e7 has been stopped in total 657.4956 ms
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 All dispatchers stopped
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 successfully reported itself as stopped in 1.0607 ms
2025-07-14 09:57:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23444:f26acee8 has been stopped in total 664.4543 ms
2025-07-14 09:57:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:57:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:57:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a successfully announced in 105.2894 ms
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee successfully announced in 105.5745 ms
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee all the dispatchers started
2025-07-14 09:57:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a all the dispatchers started
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee caught stopping signal...
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a caught stopping signal...
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a All dispatchers stopped
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a successfully reported itself as stopped in 1.8305 ms
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:dc75723a has been stopped in total 156.2405 ms
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee All dispatchers stopped
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee successfully reported itself as stopped in 0.9324 ms
2025-07-14 09:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25596:a720ddee has been stopped in total 183.577 ms
2025-07-14 09:58:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 09:58:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 09:58:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 successfully announced in 102.4971 ms
2025-07-14 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 successfully announced in 72.5863 ms
2025-07-14 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:58:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 all the dispatchers started
2025-07-14 09:58:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 all the dispatchers started
2025-07-14 10:02:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 caught stopping signal...
2025-07-14 10:02:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 caught stopping signal...
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 All dispatchers stopped
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 successfully reported itself as stopped in 1.7726 ms
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:01683255 has been stopped in total 438.5055 ms
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 caught stopped signal...
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 All dispatchers stopped
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 successfully reported itself as stopped in 0.9867 ms
2025-07-14 10:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23548:0a1a6472 has been stopped in total 826.7511 ms
2025-07-14 10:03:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:03:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:03:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 successfully announced in 102.7821 ms
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 successfully announced in 101.9768 ms
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 all the dispatchers started
2025-07-14 10:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 all the dispatchers started
2025-07-14 10:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 caught stopping signal...
2025-07-14 10:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 caught stopping signal...
2025-07-14 10:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 caught stopped signal...
2025-07-14 10:04:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 caught stopped signal...
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 All dispatchers stopped
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 successfully reported itself as stopped in 2.6663 ms
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:b0c2ad51 has been stopped in total 899.8199 ms
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 All dispatchers stopped
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 successfully reported itself as stopped in 0.7797 ms
2025-07-14 10:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13192:66e3cc90 has been stopped in total 929.0375 ms
2025-07-14 10:05:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:05:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:05:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 successfully announced in 103.6128 ms
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 successfully announced in 103.8759 ms
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 all the dispatchers started
2025-07-14 10:05:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 all the dispatchers started
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 caught stopping signal...
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 caught stopping signal...
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 All dispatchers stopped
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 successfully reported itself as stopped in 1.4498 ms
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:d430a1e3 has been stopped in total 134.9925 ms
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 All dispatchers stopped
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 successfully reported itself as stopped in 0.6892 ms
2025-07-14 10:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5056:dacbe4e1 has been stopped in total 170.1648 ms
2025-07-14 10:06:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:06:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:06:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 successfully announced in 98.0263 ms
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c successfully announced in 98.496 ms
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c all the dispatchers started
2025-07-14 10:06:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 all the dispatchers started
2025-07-14 10:09:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c caught stopping signal...
2025-07-14 10:09:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 caught stopping signal...
2025-07-14 10:09:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 caught stopped signal...
2025-07-14 10:09:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c caught stopped signal...
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 All dispatchers stopped
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 successfully reported itself as stopped in 2.5717 ms
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:579e2d10 has been stopped in total 870.3704 ms
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c All dispatchers stopped
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c successfully reported itself as stopped in 0.7262 ms
2025-07-14 10:09:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20784:f012018c has been stopped in total 916.5337 ms
2025-07-14 10:09:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:09:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:09:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb successfully announced in 105.4019 ms
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb successfully announced in 105.17 ms
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb all the dispatchers started
2025-07-14 10:09:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb all the dispatchers started
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb caught stopping signal...
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb caught stopping signal...
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb All dispatchers stopped
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb All dispatchers stopped
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb successfully reported itself as stopped in 1.7689 ms
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:e374cbcb has been stopped in total 160.1947 ms
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb successfully reported itself as stopped in 0.865 ms
2025-07-14 10:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24872:7e370abb has been stopped in total 160.8861 ms
2025-07-14 10:10:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:10:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:10:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 successfully announced in 97.787 ms
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 successfully announced in 72.6922 ms
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 all the dispatchers started
2025-07-14 10:10:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 all the dispatchers started
2025-07-14 10:11:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 caught stopping signal...
2025-07-14 10:11:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 caught stopping signal...
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 caught stopped signal...
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 caught stopped signal...
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 All dispatchers stopped
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 successfully reported itself as stopped in 2.3922 ms
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:10c09509 has been stopped in total 756.2327 ms
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 All dispatchers stopped
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 successfully reported itself as stopped in 1.2168 ms
2025-07-14 10:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16640:6b4b42f4 has been stopped in total 920.0907 ms
2025-07-14 10:11:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:11:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:11:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 successfully announced in 108.8721 ms
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 successfully announced in 110.3165 ms
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 all the dispatchers started
2025-07-14 10:11:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 all the dispatchers started
2025-07-14 10:13:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 caught stopping signal...
2025-07-14 10:13:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 caught stopping signal...
2025-07-14 10:13:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 All dispatchers stopped
2025-07-14 10:13:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 successfully reported itself as stopped in 2.2445 ms
2025-07-14 10:13:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:a3553c11 has been stopped in total 215.5609 ms
2025-07-14 10:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 caught stopped signal...
2025-07-14 10:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 All dispatchers stopped
2025-07-14 10:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 successfully reported itself as stopped in 1.0896 ms
2025-07-14 10:13:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22144:d020f470 has been stopped in total 589.7362 ms
2025-07-14 10:13:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:13:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:13:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde successfully announced in 122.5803 ms
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 successfully announced in 125.8907 ms
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 all the dispatchers started
2025-07-14 10:13:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde all the dispatchers started
2025-07-14 10:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 caught stopping signal...
2025-07-14 10:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde caught stopping signal...
2025-07-14 10:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde caught stopped signal...
2025-07-14 10:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 caught stopped signal...
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde All dispatchers stopped
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 All dispatchers stopped
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde successfully reported itself as stopped in 2.4021 ms
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:a20a9dde has been stopped in total 950.8065 ms
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 successfully reported itself as stopped in 1.0446 ms
2025-07-14 10:15:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18284:75e6f842 has been stopped in total 951.9277 ms
2025-07-14 10:15:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:15:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:15:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 successfully announced in 126.359 ms
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c successfully announced in 126.3586 ms
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 all the dispatchers started
2025-07-14 10:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c all the dispatchers started
2025-07-14 10:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c caught stopping signal...
2025-07-14 10:16:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 caught stopping signal...
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 caught stopped signal...
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c caught stopped signal...
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 All dispatchers stopped
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c All dispatchers stopped
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 successfully reported itself as stopped in 2.575 ms
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:d60cbd53 has been stopped in total 836.3625 ms
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c successfully reported itself as stopped in 1.2893 ms
2025-07-14 10:16:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3476:12f5cf5c has been stopped in total 836.8821 ms
2025-07-14 10:17:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:17:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:17:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd successfully announced in 104.812 ms
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 successfully announced in 104.7977 ms
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 all the dispatchers started
2025-07-14 10:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd all the dispatchers started
2025-07-14 10:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 caught stopping signal...
2025-07-14 10:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd caught stopping signal...
2025-07-14 10:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd caught stopped signal...
2025-07-14 10:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 caught stopped signal...
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd All dispatchers stopped
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd successfully reported itself as stopped in 1.7114 ms
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:ebd863dd has been stopped in total 915.2846 ms
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 All dispatchers stopped
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 successfully reported itself as stopped in 1.1428 ms
2025-07-14 10:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16916:8f885bb1 has been stopped in total 990.5039 ms
2025-07-14 10:18:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:18:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:18:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 successfully announced in 114.8837 ms
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 successfully announced in 117.3228 ms
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 all the dispatchers started
2025-07-14 10:18:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 all the dispatchers started
2025-07-14 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 caught stopping signal...
2025-07-14 10:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 caught stopping signal...
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 caught stopped signal...
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 caught stopped signal...
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 All dispatchers stopped
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 All dispatchers stopped
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 successfully reported itself as stopped in 1.8624 ms
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:e5935885 has been stopped in total 932.3611 ms
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 successfully reported itself as stopped in 2.036 ms
2025-07-14 10:19:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11168:eb824d96 has been stopped in total 932.3546 ms
2025-07-14 10:20:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:20:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:20:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:20:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d successfully announced in 117.8013 ms
2025-07-14 10:20:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:20:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e successfully announced in 130.1992 ms
2025-07-14 10:20:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:20:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d all the dispatchers started
2025-07-14 10:20:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e all the dispatchers started
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e caught stopping signal...
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d caught stopping signal...
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d All dispatchers stopped
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d successfully reported itself as stopped in 1.6326 ms
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:5d84910d has been stopped in total 143.8535 ms
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e All dispatchers stopped
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e successfully reported itself as stopped in 0.8841 ms
2025-07-14 10:20:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25216:8d6b8c1e has been stopped in total 152.3324 ms
2025-07-14 10:45:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:45:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:45:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b successfully announced in 128.7234 ms
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 successfully announced in 141.6537 ms
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b all the dispatchers started
2025-07-14 10:45:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 all the dispatchers started
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 caught stopping signal...
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b caught stopping signal...
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b All dispatchers stopped
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b successfully reported itself as stopped in 8.0164 ms
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8775190b has been stopped in total 417.9108 ms
2025-07-14 10:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 caught stopped signal...
2025-07-14 10:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 All dispatchers stopped
2025-07-14 10:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 successfully reported itself as stopped in 2.2768 ms
2025-07-14 10:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27236:8b864026 has been stopped in total 644.3447 ms
2025-07-14 10:58:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 10:58:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 10:58:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 10:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a successfully announced in 118.2124 ms
2025-07-14 10:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 successfully announced in 119.6198 ms
2025-07-14 10:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 10:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 all the dispatchers started
2025-07-14 10:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a all the dispatchers started
2025-07-14 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a caught stopping signal...
2025-07-14 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 caught stopping signal...
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 caught stopped signal...
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a caught stopped signal...
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a All dispatchers stopped
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 All dispatchers stopped
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a successfully reported itself as stopped in 4.0322 ms
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 successfully reported itself as stopped in 1.6957 ms
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:7e4b2d2a has been stopped in total 608.4176 ms
2025-07-14 11:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16576:2c6f4564 has been stopped in total 608.2366 ms
2025-07-14 11:06:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:06:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:06:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 successfully announced in 122.7591 ms
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 successfully announced in 123.6802 ms
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 all the dispatchers started
2025-07-14 11:06:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 all the dispatchers started
2025-07-14 11:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 caught stopping signal...
2025-07-14 11:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 caught stopping signal...
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 All dispatchers stopped
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 caught stopped signal...
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 caught stopped signal...
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 successfully reported itself as stopped in 21.4978 ms
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 All dispatchers stopped
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:3034e976 has been stopped in total 521.605 ms
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 successfully reported itself as stopped in 1.3923 ms
2025-07-14 11:07:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28464:64fd7778 has been stopped in total 522.6705 ms
2025-07-14 11:14:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:14:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:14:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 successfully announced in 111.2706 ms
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb successfully announced in 112.04 ms
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 all the dispatchers started
2025-07-14 11:14:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb all the dispatchers started
2025-07-14 11:14:51 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Failed to compare two elements in the array."
System.InvalidOperationException: Failed to compare two elements in the array.
 ---> System.InvalidOperationException: The following routes are ambiguous:
                    'procurement/purchaseorderlines' in 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrdersPage'
                    'procurement/purchaseorderlines' in 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderLinesPage'
                    
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.RouteComparison(RouteEntry x, RouteEntry y)
   at System.Collections.Generic.ArraySortHelper`1.InsertionSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntroSort(Span`1 keys, Int32 depthLimit, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntrospectiveSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   --- End of inner exception stack trace ---
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   at System.Array.Sort[T](T[] array, Int32 index, Int32 length, IComparer`1 comparer)
   at System.Collections.Generic.List`1.Sort(Int32 index, Int32 count, IComparer`1 comparer)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(Dictionary`2 templatesByHandler)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(List`1 componentTypes)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(RouteKey routeKey)
   at BootstrapBlazor.Components.RouteTableFactory.RefreshRouteTable(IEnumerable`1 assemblies)
   at BootstrapBlazor.Components.RouteTableFactory.Create(IEnumerable`1 assemblies, String url)
   at BootstrapBlazor.Components.Tab.AddTabItem(String url)
   at BootstrapBlazor.Components.Tab.AddTabByUrl()
   at BootstrapBlazor.Components.Tab.OnParametersSet()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
2025-07-14 11:14:51 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Failed to compare two elements in the array.
 ---> System.InvalidOperationException: The following routes are ambiguous:
                    'procurement/purchaseorderlines' in 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrdersPage'
                    'procurement/purchaseorderlines' in 'HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderLinesPage'
                    
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.RouteComparison(RouteEntry x, RouteEntry y)
   at System.Collections.Generic.ArraySortHelper`1.InsertionSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntroSort(Span`1 keys, Int32 depthLimit, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.IntrospectiveSort(Span`1 keys, Comparison`1 comparer)
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   --- End of inner exception stack trace ---
   at System.Collections.Generic.ArraySortHelper`1.Sort(Span`1 keys, IComparer`1 comparer)
   at System.Array.Sort[T](T[] array, Int32 index, Int32 length, IComparer`1 comparer)
   at System.Collections.Generic.List`1.Sort(Int32 index, Int32 count, IComparer`1 comparer)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(Dictionary`2 templatesByHandler)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(List`1 componentTypes)
   at Microsoft.AspNetCore.Components.Routing.RouteTableFactory.Create(RouteKey routeKey)
   at BootstrapBlazor.Components.RouteTableFactory.RefreshRouteTable(IEnumerable`1 assemblies)
   at BootstrapBlazor.Components.RouteTableFactory.Create(IEnumerable`1 assemblies, String url)
   at BootstrapBlazor.Components.Tab.AddTabItem(String url)
   at BootstrapBlazor.Components.Tab.AddTabByUrl()
   at BootstrapBlazor.Components.Tab.OnParametersSet()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-14 11:14:57 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

/procurement/purchaseorderlines/{PoNum} (/procurement/purchaseorderlines/{PoNum})
/procurement/purchaseorderlines/{PoNum} (/procurement/purchaseorderlines/{PoNum})
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-14 11:14:57 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

/procurement/purchaseorderlines (/procurement/purchaseorderlines)
/procurement/purchaseorderlines (/procurement/purchaseorderlines)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-14 11:15:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb caught stopping signal...
2025-07-14 11:15:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 caught stopping signal...
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb All dispatchers stopped
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb successfully reported itself as stopped in 2.4116 ms
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:4371d7cb has been stopped in total 338.9275 ms
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 caught stopped signal...
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 All dispatchers stopped
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 successfully reported itself as stopped in 1.5542 ms
2025-07-14 11:15:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26616:dc0df207 has been stopped in total 913.4189 ms
2025-07-14 11:16:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:16:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:16:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 successfully announced in 100.2217 ms
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 successfully announced in 100.2302 ms
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 all the dispatchers started
2025-07-14 11:16:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 all the dispatchers started
2025-07-14 11:20:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 caught stopping signal...
2025-07-14 11:20:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 caught stopping signal...
2025-07-14 11:20:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 caught stopped signal...
2025-07-14 11:20:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 caught stopped signal...
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 All dispatchers stopped
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 successfully reported itself as stopped in 3.1587 ms
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:b0659316 has been stopped in total 746.688 ms
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 All dispatchers stopped
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 successfully reported itself as stopped in 0.7199 ms
2025-07-14 11:20:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24816:dc4246a8 has been stopped in total 751.4541 ms
2025-07-14 11:20:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:20:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:20:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c successfully announced in 104.9036 ms
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 successfully announced in 99.9312 ms
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c all the dispatchers started
2025-07-14 11:20:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 all the dispatchers started
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c caught stopping signal...
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 caught stopping signal...
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 All dispatchers stopped
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 successfully reported itself as stopped in 1.4908 ms
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:81caae10 has been stopped in total 51.2514 ms
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c All dispatchers stopped
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c successfully reported itself as stopped in 0.7262 ms
2025-07-14 11:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28988:23eea13c has been stopped in total 63.8025 ms
2025-07-14 11:25:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:25:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:25:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 successfully announced in 110.6678 ms
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 successfully announced in 110.5528 ms
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 all the dispatchers started
2025-07-14 11:25:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 all the dispatchers started
2025-07-14 11:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 caught stopping signal...
2025-07-14 11:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 caught stopping signal...
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 caught stopped signal...
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 caught stopped signal...
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 All dispatchers stopped
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 successfully reported itself as stopped in 2.9927 ms
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:0af9f5f8 has been stopped in total 824.1595 ms
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 All dispatchers stopped
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 successfully reported itself as stopped in 1.2307 ms
2025-07-14 11:27:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27468:87b8c1f3 has been stopped in total 962.826 ms
2025-07-14 11:27:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:27:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:27:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 successfully announced in 103.593 ms
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 successfully announced in 103.5919 ms
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 all the dispatchers started
2025-07-14 11:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 all the dispatchers started
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 caught stopping signal...
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 caught stopping signal...
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 caught stopped signal...
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 caught stopped signal...
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 All dispatchers stopped
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 successfully reported itself as stopped in 2.6721 ms
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:cba80431 has been stopped in total 654.4028 ms
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 All dispatchers stopped
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 successfully reported itself as stopped in 0.9632 ms
2025-07-14 11:37:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25712:58a5ad38 has been stopped in total 684.5426 ms
2025-07-14 11:37:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:37:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:37:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:37:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 successfully announced in 98.3956 ms
2025-07-14 11:37:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a successfully announced in 115.6469 ms
2025-07-14 11:37:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:37:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:37:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a all the dispatchers started
2025-07-14 11:37:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 all the dispatchers started
2025-07-14 11:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a caught stopping signal...
2025-07-14 11:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 caught stopping signal...
2025-07-14 11:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a caught stopped signal...
2025-07-14 11:38:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 caught stopped signal...
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a All dispatchers stopped
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 All dispatchers stopped
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a successfully reported itself as stopped in 1.7594 ms
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:a1ab203a has been stopped in total 872.183 ms
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 successfully reported itself as stopped in 1.8283 ms
2025-07-14 11:38:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29500:6f583fa1 has been stopped in total 871.8412 ms
2025-07-14 11:38:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:38:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:38:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:cdb9b1d8 successfully announced in 230.6365 ms
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:27143d98 successfully announced in 203.9122 ms
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:cdb9b1d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:27143d98 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:cdb9b1d8 all the dispatchers started
2025-07-14 11:38:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22452:27143d98 all the dispatchers started
2025-07-14 11:46:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:46:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:46:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad successfully announced in 107.2574 ms
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 successfully announced in 107.2321 ms
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 all the dispatchers started
2025-07-14 11:46:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad all the dispatchers started
2025-07-14 11:51:13 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad caught stopping signal...
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 caught stopping signal...
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 All dispatchers stopped
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 successfully reported itself as stopped in 2.1716 ms
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:45b9d783 has been stopped in total 217.3852 ms
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad caught stopped signal...
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad All dispatchers stopped
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad successfully reported itself as stopped in 1.1911 ms
2025-07-14 11:51:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28304:fae572ad has been stopped in total 854.9823 ms
2025-07-14 11:51:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:51:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 successfully announced in 104.5068 ms
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 successfully announced in 104.7252 ms
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 all the dispatchers started
2025-07-14 11:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 all the dispatchers started
2025-07-14 11:53:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 caught stopping signal...
2025-07-14 11:53:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 caught stopping signal...
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 All dispatchers stopped
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 All dispatchers stopped
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 successfully reported itself as stopped in 1.8844 ms
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:b5c2aec1 has been stopped in total 210.994 ms
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 successfully reported itself as stopped in 40.7599 ms
2025-07-14 11:53:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30392:2b763a39 has been stopped in total 249.5999 ms
2025-07-14 11:53:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:53:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:53:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 successfully announced in 104.9186 ms
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 successfully announced in 104.9208 ms
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 all the dispatchers started
2025-07-14 11:53:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 all the dispatchers started
2025-07-14 11:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 caught stopping signal...
2025-07-14 11:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 caught stopping signal...
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 All dispatchers stopped
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 successfully reported itself as stopped in 2.9073 ms
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:8d4074f4 has been stopped in total 399.3995 ms
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 All dispatchers stopped
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 successfully reported itself as stopped in 0.8126 ms
2025-07-14 11:54:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13084:7b5b4879 has been stopped in total 427.3501 ms
2025-07-14 11:54:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:54:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:54:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 successfully announced in 101.5968 ms
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 successfully announced in 101.595 ms
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 all the dispatchers started
2025-07-14 11:54:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 all the dispatchers started
2025-07-14 11:56:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 caught stopping signal...
2025-07-14 11:56:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 caught stopping signal...
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 caught stopped signal...
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 caught stopped signal...
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 All dispatchers stopped
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 successfully reported itself as stopped in 1.7327 ms
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:349ad726 has been stopped in total 511.0391 ms
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 All dispatchers stopped
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 successfully reported itself as stopped in 0.7251 ms
2025-07-14 11:56:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30040:60a04fd8 has been stopped in total 518.3334 ms
2025-07-14 11:56:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:56:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:56:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 successfully announced in 104.0854 ms
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 successfully announced in 104.2672 ms
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 all the dispatchers started
2025-07-14 11:56:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 all the dispatchers started
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 caught stopping signal...
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 caught stopping signal...
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 All dispatchers stopped
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 successfully reported itself as stopped in 2.2918 ms
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:6ea3b4e5 has been stopped in total 259.0857 ms
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 All dispatchers stopped
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 successfully reported itself as stopped in 0.8218 ms
2025-07-14 11:57:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16184:37654cc3 has been stopped in total 283.6998 ms
2025-07-14 11:57:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:57:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:57:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a successfully announced in 111.6615 ms
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 successfully announced in 111.6538 ms
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a all the dispatchers started
2025-07-14 11:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 all the dispatchers started
2025-07-14 11:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 caught stopping signal...
2025-07-14 11:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a caught stopping signal...
2025-07-14 11:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a caught stopped signal...
2025-07-14 11:58:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 caught stopped signal...
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 All dispatchers stopped
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a All dispatchers stopped
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 successfully reported itself as stopped in 2.9055 ms
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:ef766806 has been stopped in total 921.1218 ms
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a successfully reported itself as stopped in 1.0578 ms
2025-07-14 11:58:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17236:dcdac93a has been stopped in total 921.4816 ms
2025-07-14 11:58:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 11:58:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 11:58:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 successfully announced in 106.33 ms
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 successfully announced in 106.4887 ms
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 all the dispatchers started
2025-07-14 11:58:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 all the dispatchers started
2025-07-14 12:24:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 caught stopping signal...
2025-07-14 12:24:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 caught stopping signal...
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 caught stopped signal...
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 caught stopped signal...
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 All dispatchers stopped
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 All dispatchers stopped
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 successfully reported itself as stopped in 2.394 ms
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:26b56135 has been stopped in total 953.822 ms
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 successfully reported itself as stopped in 2.4424 ms
2025-07-14 12:24:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22556:e879f4e7 has been stopped in total 954.3185 ms
2025-07-14 12:24:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 12:24:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:24:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c successfully announced in 110.5619 ms
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 successfully announced in 109.2246 ms
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 all the dispatchers started
2025-07-14 12:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c all the dispatchers started
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c caught stopping signal...
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 caught stopping signal...
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c All dispatchers stopped
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c successfully reported itself as stopped in 2.4109 ms
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:68c6976c has been stopped in total 244.0189 ms
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 All dispatchers stopped
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 successfully reported itself as stopped in 0.7229 ms
2025-07-14 12:26:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27844:7e55c546 has been stopped in total 251.979 ms
2025-07-14 12:27:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 12:27:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:27:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 successfully announced in 101.0897 ms
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 successfully announced in 100.9428 ms
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 all the dispatchers started
2025-07-14 12:27:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 all the dispatchers started
2025-07-14 12:28:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 caught stopping signal...
2025-07-14 12:28:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 caught stopping signal...
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 caught stopped signal...
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 caught stopped signal...
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 All dispatchers stopped
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 successfully reported itself as stopped in 2.819 ms
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:f91d2d57 has been stopped in total 935.6734 ms
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 All dispatchers stopped
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 successfully reported itself as stopped in 1.3919 ms
2025-07-14 12:28:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31020:6e9402d2 has been stopped in total 957.489 ms
2025-07-14 12:28:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 12:28:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 12:28:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 12:28:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e successfully announced in 100.3269 ms
2025-07-14 12:28:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 successfully announced in 100.3657 ms
2025-07-14 12:28:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:28:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 12:28:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 all the dispatchers started
2025-07-14 12:28:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e all the dispatchers started
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 caught stopping signal...
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e caught stopping signal...
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e caught stopped signal...
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 caught stopped signal...
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 All dispatchers stopped
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e All dispatchers stopped
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 successfully reported itself as stopped in 2.3167 ms
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:dc19dbd5 has been stopped in total 763.0082 ms
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e successfully reported itself as stopped in 1.1428 ms
2025-07-14 13:00:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27564:2870ea7e has been stopped in total 763.5615 ms
2025-07-14 13:12:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:12:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:12:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 successfully announced in 118.6462 ms
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a successfully announced in 118.64 ms
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a all the dispatchers started
2025-07-14 13:12:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 all the dispatchers started
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 caught stopping signal...
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a caught stopping signal...
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a All dispatchers stopped
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a successfully reported itself as stopped in 1.8649 ms
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:12d0990a has been stopped in total 391.1778 ms
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 All dispatchers stopped
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 successfully reported itself as stopped in 0.6485 ms
2025-07-14 13:27:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14408:601c5972 has been stopped in total 395.0763 ms
2025-07-14 13:27:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:27:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:27:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a successfully announced in 109.2008 ms
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 successfully announced in 109.1828 ms
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a all the dispatchers started
2025-07-14 13:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 all the dispatchers started
2025-07-14 13:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a caught stopping signal...
2025-07-14 13:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 caught stopping signal...
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 All dispatchers stopped
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 successfully reported itself as stopped in 1.5707 ms
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:429f4696 has been stopped in total 433.458 ms
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a All dispatchers stopped
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a successfully reported itself as stopped in 0.7401 ms
2025-07-14 13:31:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22068:6718d78a has been stopped in total 442.6012 ms
2025-07-14 13:31:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:31:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 successfully announced in 102.7558 ms
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 successfully announced in 102.9042 ms
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 all the dispatchers started
2025-07-14 13:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 all the dispatchers started
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 caught stopping signal...
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 caught stopping signal...
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 All dispatchers stopped
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 successfully reported itself as stopped in 1.9547 ms
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:c3b62390 has been stopped in total 96.8223 ms
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 caught stopped signal...
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 All dispatchers stopped
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 successfully reported itself as stopped in 1.0043 ms
2025-07-14 13:38:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18640:6dbbb760 has been stopped in total 630.8549 ms
2025-07-14 13:39:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:39:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:39:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb successfully announced in 116.8036 ms
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 successfully announced in 116.804 ms
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb all the dispatchers started
2025-07-14 13:39:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 all the dispatchers started
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 caught stopping signal...
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb caught stopping signal...
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 All dispatchers stopped
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 successfully reported itself as stopped in 2.7604 ms
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:e8b23892 has been stopped in total 281.911 ms
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb All dispatchers stopped
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb successfully reported itself as stopped in 0.8053 ms
2025-07-14 13:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23620:a1c9b6eb has been stopped in total 328.4774 ms
2025-07-14 13:41:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:41:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:41:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 successfully announced in 104.816 ms
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 successfully announced in 104.7944 ms
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 all the dispatchers started
2025-07-14 13:41:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 all the dispatchers started
2025-07-14 13:42:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 caught stopping signal...
2025-07-14 13:42:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 caught stopping signal...
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 All dispatchers stopped
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 All dispatchers stopped
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 successfully reported itself as stopped in 0.817 ms
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:c4c0acf8 has been stopped in total 492.0954 ms
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 successfully reported itself as stopped in 4.0879 ms
2025-07-14 13:42:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17308:599edf95 has been stopped in total 494.5378 ms
2025-07-14 13:42:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:42:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:42:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 successfully announced in 104.9692 ms
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 successfully announced in 105.096 ms
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 all the dispatchers started
2025-07-14 13:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 all the dispatchers started
2025-07-14 13:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 caught stopping signal...
2025-07-14 13:44:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 caught stopping signal...
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 caught stopped signal...
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 caught stopped signal...
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 All dispatchers stopped
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 successfully reported itself as stopped in 1.7814 ms
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:308e9913 has been stopped in total 598.9542 ms
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 All dispatchers stopped
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 successfully reported itself as stopped in 1.1856 ms
2025-07-14 13:44:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23656:a90ff2c8 has been stopped in total 702.486 ms
2025-07-14 13:44:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:44:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:44:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e successfully announced in 102.7543 ms
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 successfully announced in 102.7649 ms
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 all the dispatchers started
2025-07-14 13:44:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e all the dispatchers started
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 caught stopping signal...
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e caught stopping signal...
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e caught stopped signal...
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 caught stopped signal...
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e All dispatchers stopped
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 All dispatchers stopped
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e successfully reported itself as stopped in 1.9466 ms
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:15abad4e has been stopped in total 664.0373 ms
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 successfully reported itself as stopped in 2.2126 ms
2025-07-14 13:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17272:5ceac5b3 has been stopped in total 664.5565 ms
2025-07-14 13:45:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 13:45:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 13:45:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 successfully announced in 107.6168 ms
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 successfully announced in 108.9916 ms
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 all the dispatchers started
2025-07-14 13:45:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 all the dispatchers started
2025-07-14 14:02:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 caught stopping signal...
2025-07-14 14:02:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 caught stopping signal...
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 All dispatchers stopped
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 successfully reported itself as stopped in 2.1588 ms
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:aa369f73 has been stopped in total 403.9616 ms
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 caught stopped signal...
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 All dispatchers stopped
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 successfully reported itself as stopped in 0.8665 ms
2025-07-14 14:02:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18772:05d7d955 has been stopped in total 691.5669 ms
2025-07-14 14:04:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:04:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:04:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 successfully announced in 108.956 ms
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 successfully announced in 108.9619 ms
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 all the dispatchers started
2025-07-14 14:04:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 all the dispatchers started
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 caught stopping signal...
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 caught stopping signal...
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 All dispatchers stopped
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 successfully reported itself as stopped in 1.493 ms
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:9faedf31 has been stopped in total 454.551 ms
2025-07-14 14:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 caught stopped signal...
2025-07-14 14:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 All dispatchers stopped
2025-07-14 14:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 successfully reported itself as stopped in 0.9925 ms
2025-07-14 14:04:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25508:aa49e2e9 has been stopped in total 613.1724 ms
2025-07-14 14:05:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:05:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:05:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 successfully announced in 101.9489 ms
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 successfully announced in 104.3914 ms
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 all the dispatchers started
2025-07-14 14:05:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 all the dispatchers started
2025-07-14 14:06:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 caught stopping signal...
2025-07-14 14:06:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 caught stopping signal...
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 caught stopped signal...
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 caught stopped signal...
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 All dispatchers stopped
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 successfully reported itself as stopped in 1.5769 ms
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:adfc5f07 has been stopped in total 868.2365 ms
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 All dispatchers stopped
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 successfully reported itself as stopped in 0.761 ms
2025-07-14 14:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21972:8f4307c7 has been stopped in total 923.6664 ms
2025-07-14 14:06:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:06:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f successfully announced in 105.2616 ms
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 successfully announced in 105.2726 ms
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f all the dispatchers started
2025-07-14 14:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 all the dispatchers started
2025-07-14 14:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 caught stopping signal...
2025-07-14 14:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f caught stopping signal...
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f caught stopped signal...
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 caught stopped signal...
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f All dispatchers stopped
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 All dispatchers stopped
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f successfully reported itself as stopped in 2.6864 ms
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 successfully reported itself as stopped in 0.7826 ms
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:f377188f has been stopped in total 890.5844 ms
2025-07-14 14:06:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7456:311ef629 has been stopped in total 890.9131 ms
2025-07-14 14:06:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:06:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:06:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c successfully announced in 113.4935 ms
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf successfully announced in 113.5111 ms
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf all the dispatchers started
2025-07-14 14:06:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c all the dispatchers started
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf caught stopping signal...
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c caught stopping signal...
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c caught stopped signal...
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf caught stopped signal...
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c All dispatchers stopped
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c successfully reported itself as stopped in 1.6378 ms
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:02b24d0c has been stopped in total 503.2366 ms
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf All dispatchers stopped
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf successfully reported itself as stopped in 0.6379 ms
2025-07-14 14:09:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21324:a8a1adcf has been stopped in total 506.4195 ms
2025-07-14 14:10:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:10:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:10:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce successfully announced in 110.6961 ms
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 successfully announced in 97.444 ms
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce all the dispatchers started
2025-07-14 14:10:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 all the dispatchers started
2025-07-14 14:12:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce caught stopping signal...
2025-07-14 14:12:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 caught stopping signal...
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 caught stopped signal...
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce caught stopped signal...
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 All dispatchers stopped
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce All dispatchers stopped
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 successfully reported itself as stopped in 1.5414 ms
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:1317c9e2 has been stopped in total 702.667 ms
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce successfully reported itself as stopped in 0.8145 ms
2025-07-14 14:12:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19392:0ad53dce has been stopped in total 703.0833 ms
2025-07-14 14:12:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:12:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:12:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d successfully announced in 107.5325 ms
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 successfully announced in 107.5534 ms
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d all the dispatchers started
2025-07-14 14:12:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 all the dispatchers started
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 caught stopping signal...
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d caught stopping signal...
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 All dispatchers stopped
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d All dispatchers stopped
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 successfully reported itself as stopped in 2.926 ms
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:b338bbb7 has been stopped in total 58.1519 ms
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d successfully reported itself as stopped in 1.8199 ms
2025-07-14 14:12:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9636:cfc7ee5d has been stopped in total 58.0914 ms
2025-07-14 14:13:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:13:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:13:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 successfully announced in 104.2591 ms
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 successfully announced in 104.1899 ms
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 all the dispatchers started
2025-07-14 14:13:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 all the dispatchers started
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 caught stopping signal...
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 caught stopping signal...
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 All dispatchers stopped
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 successfully reported itself as stopped in 1.6586 ms
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:489b6428 has been stopped in total 9.7547 ms
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 All dispatchers stopped
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 successfully reported itself as stopped in 0.6892 ms
2025-07-14 14:26:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22724:81085a88 has been stopped in total 40.2272 ms
2025-07-14 14:28:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 14:28:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 14:28:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab successfully announced in 106.744 ms
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 successfully announced in 107.0646 ms
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab all the dispatchers started
2025-07-14 14:28:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 all the dispatchers started
2025-07-14 14:38:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 caught stopping signal...
2025-07-14 14:38:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab caught stopping signal...
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 All dispatchers stopped
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 successfully reported itself as stopped in 2.1071 ms
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:5108ed64 has been stopped in total 453.3913 ms
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab All dispatchers stopped
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab successfully reported itself as stopped in 0.7284 ms
2025-07-14 14:38:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2464:546b8bab has been stopped in total 471.3516 ms
2025-07-14 15:12:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 15:12:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:12:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 successfully announced in 106.7166 ms
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 successfully announced in 106.7177 ms
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 all the dispatchers started
2025-07-14 15:12:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 all the dispatchers started
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 caught stopping signal...
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 caught stopping signal...
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 All dispatchers stopped
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 successfully reported itself as stopped in 2.83 ms
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:432d0d59 has been stopped in total 100.3994 ms
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 All dispatchers stopped
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 successfully reported itself as stopped in 1.5601 ms
2025-07-14 15:15:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14864:c2035034 has been stopped in total 109.4653 ms
2025-07-14 15:31:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 15:31:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:31:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c successfully announced in 111.6513 ms
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 successfully announced in 111.6524 ms
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c all the dispatchers started
2025-07-14 15:31:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 all the dispatchers started
2025-07-14 15:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c caught stopping signal...
2025-07-14 15:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 caught stopping signal...
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c All dispatchers stopped
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c successfully reported itself as stopped in 1.8877 ms
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:53303d4c has been stopped in total 66.9349 ms
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 All dispatchers stopped
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 successfully reported itself as stopped in 1.6726 ms
2025-07-14 15:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18528:432febe3 has been stopped in total 149.5932 ms
2025-07-14 15:38:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 15:38:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 15:38:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 successfully announced in 108.7955 ms
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df successfully announced in 108.3756 ms
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df all the dispatchers started
2025-07-14 15:38:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 all the dispatchers started
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df caught stopping signal...
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 caught stopping signal...
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 All dispatchers stopped
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 successfully reported itself as stopped in 2.778 ms
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:64f3c527 has been stopped in total 90.6792 ms
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df All dispatchers stopped
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df successfully reported itself as stopped in 0.8496 ms
2025-07-14 15:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27264:954443df has been stopped in total 128.4782 ms
2025-07-14 16:01:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:01:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:01:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 successfully announced in 107.6923 ms
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 successfully announced in 107.6905 ms
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 all the dispatchers started
2025-07-14 16:01:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 all the dispatchers started
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 caught stopping signal...
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 caught stopping signal...
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 All dispatchers stopped
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 All dispatchers stopped
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 successfully reported itself as stopped in 2.9297 ms
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:c4c5bf02 has been stopped in total 69.0794 ms
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 successfully reported itself as stopped in 1.637 ms
2025-07-14 16:01:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28424:497eca85 has been stopped in total 69.6672 ms
2025-07-14 16:12:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:12:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:12:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d successfully announced in 107.608 ms
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 successfully announced in 107.608 ms
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 all the dispatchers started
2025-07-14 16:12:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d all the dispatchers started
2025-07-14 16:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d caught stopping signal...
2025-07-14 16:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 caught stopping signal...
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d All dispatchers stopped
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d successfully reported itself as stopped in 1.8375 ms
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3b1c659d has been stopped in total 270.779 ms
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 All dispatchers stopped
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 successfully reported itself as stopped in 2.1126 ms
2025-07-14 16:14:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4544:3d386657 has been stopped in total 313.0237 ms
2025-07-14 16:14:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:14:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:14:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc successfully announced in 109.2033 ms
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 successfully announced in 109.1905 ms
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc all the dispatchers started
2025-07-14 16:14:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 all the dispatchers started
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 caught stopping signal...
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc caught stopping signal...
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 All dispatchers stopped
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 successfully reported itself as stopped in 1.5817 ms
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:0778ffa0 has been stopped in total 427.8436 ms
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc All dispatchers stopped
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc successfully reported itself as stopped in 1.227 ms
2025-07-14 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25764:f06797cc has been stopped in total 457.4774 ms
2025-07-14 16:15:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:15:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:15:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a successfully announced in 108.4749 ms
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 successfully announced in 108.4709 ms
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 all the dispatchers started
2025-07-14 16:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a all the dispatchers started
2025-07-14 16:17:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a caught stopping signal...
2025-07-14 16:17:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 caught stopping signal...
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 caught stopped signal...
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a caught stopped signal...
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 All dispatchers stopped
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 successfully reported itself as stopped in 1.5344 ms
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:3bc16b93 has been stopped in total 910.0938 ms
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a All dispatchers stopped
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a successfully reported itself as stopped in 0.7558 ms
2025-07-14 16:17:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:19e91d4a has been stopped in total 918.0836 ms
2025-07-14 16:17:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:17:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:17:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e successfully announced in 99.4369 ms
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 successfully announced in 117.2436 ms
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e all the dispatchers started
2025-07-14 16:17:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 all the dispatchers started
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 caught stopping signal...
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e caught stopping signal...
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e All dispatchers stopped
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e successfully reported itself as stopped in 2.05 ms
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:d683518e has been stopped in total 389.5887 ms
2025-07-14 16:20:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 caught stopped signal...
2025-07-14 16:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 All dispatchers stopped
2025-07-14 16:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 successfully reported itself as stopped in 1.0772 ms
2025-07-14 16:20:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24828:76eb49e4 has been stopped in total 516.2621 ms
2025-07-14 16:20:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:20:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:20:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 successfully announced in 105.9959 ms
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be successfully announced in 107.2163 ms
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 all the dispatchers started
2025-07-14 16:20:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be all the dispatchers started
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 caught stopping signal...
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be caught stopping signal...
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be All dispatchers stopped
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 All dispatchers stopped
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 successfully reported itself as stopped in 2.2823 ms
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be successfully reported itself as stopped in 2.3127 ms
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:a78e8c06 has been stopped in total 189.3727 ms
2025-07-14 16:20:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28644:39a782be has been stopped in total 189.2928 ms
2025-07-14 16:21:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:21:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:21:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:288df09f successfully announced in 274.2005 ms
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:a2feb9c8 successfully announced in 275.2784 ms
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:a2feb9c8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:288df09f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:288df09f all the dispatchers started
2025-07-14 16:21:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26056:a2feb9c8 all the dispatchers started
2025-07-14 16:26:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:26:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:26:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d successfully announced in 108.0697 ms
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 successfully announced in 108.0807 ms
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 all the dispatchers started
2025-07-14 16:26:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d all the dispatchers started
2025-07-14 16:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 caught stopping signal...
2025-07-14 16:27:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d caught stopping signal...
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d All dispatchers stopped
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 All dispatchers stopped
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 successfully reported itself as stopped in 2.1097 ms
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:ca5df7b7 has been stopped in total 481.8625 ms
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d successfully reported itself as stopped in 6.1386 ms
2025-07-14 16:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3920:8729d22d has been stopped in total 484.8707 ms
2025-07-14 16:27:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:27:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:27:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 successfully announced in 99.6388 ms
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 successfully announced in 113.7438 ms
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 all the dispatchers started
2025-07-14 16:27:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 all the dispatchers started
2025-07-14 16:28:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 caught stopping signal...
2025-07-14 16:28:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 caught stopping signal...
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 caught stopped signal...
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 caught stopped signal...
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 All dispatchers stopped
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 successfully reported itself as stopped in 2.0038 ms
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:ab9e9c37 has been stopped in total 778.4795 ms
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 All dispatchers stopped
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 successfully reported itself as stopped in 0.9687 ms
2025-07-14 16:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:10d24d67 has been stopped in total 791.1867 ms
2025-07-14 16:29:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:29:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:29:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 successfully announced in 118.5795 ms
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 successfully announced in 118.5792 ms
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 all the dispatchers started
2025-07-14 16:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 all the dispatchers started
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 caught stopping signal...
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 caught stopping signal...
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 All dispatchers stopped
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 successfully reported itself as stopped in 2.0313 ms
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:b74fe105 has been stopped in total 497.2008 ms
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 All dispatchers stopped
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 successfully reported itself as stopped in 0.6888 ms
2025-07-14 16:30:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29116:34a09ef0 has been stopped in total 501.0169 ms
2025-07-14 16:30:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:30:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:30:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 successfully announced in 105.2792 ms
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf successfully announced in 105.1773 ms
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf all the dispatchers started
2025-07-14 16:30:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 all the dispatchers started
2025-07-14 16:30:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 caught stopping signal...
2025-07-14 16:30:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf caught stopping signal...
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf caught stopped signal...
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 caught stopped signal...
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf All dispatchers stopped
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 All dispatchers stopped
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf successfully reported itself as stopped in 3.1334 ms
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:ff3d41cf has been stopped in total 600.311 ms
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 successfully reported itself as stopped in 1.0182 ms
2025-07-14 16:30:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24740:9c5b8837 has been stopped in total 601.8751 ms
2025-07-14 16:31:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:31:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:31:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 successfully announced in 120.5343 ms
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 successfully announced in 120.3035 ms
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:31:04 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 all the dispatchers started
2025-07-14 16:31:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 all the dispatchers started
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 caught stopping signal...
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 caught stopping signal...
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 caught stopped signal...
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 caught stopped signal...
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 All dispatchers stopped
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 successfully reported itself as stopped in 3.4005 ms
2025-07-14 16:35:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:d0893db7 has been stopped in total 722.3508 ms
2025-07-14 16:35:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 All dispatchers stopped
2025-07-14 16:35:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 successfully reported itself as stopped in 1.0354 ms
2025-07-14 16:35:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21304:139eea51 has been stopped in total 940.4418 ms
2025-07-14 16:37:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:37:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:37:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e successfully announced in 125.2165 ms
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 successfully announced in 122.4836 ms
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 all the dispatchers started
2025-07-14 16:37:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e all the dispatchers started
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e caught stopping signal...
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 caught stopping signal...
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 All dispatchers stopped
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 successfully reported itself as stopped in 1.747 ms
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:84e9c830 has been stopped in total 489.0854 ms
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e All dispatchers stopped
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e successfully reported itself as stopped in 0.7064 ms
2025-07-14 16:41:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14752:d900284e has been stopped in total 493.1631 ms
2025-07-14 16:41:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:41:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:41:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 successfully announced in 108.1118 ms
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 successfully announced in 108.088 ms
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 all the dispatchers started
2025-07-14 16:41:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 all the dispatchers started
2025-07-14 16:45:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 caught stopping signal...
2025-07-14 16:45:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 caught stopping signal...
2025-07-14 16:45:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 All dispatchers stopped
2025-07-14 16:45:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 successfully reported itself as stopped in 1.7103 ms
2025-07-14 16:45:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:018229e1 has been stopped in total 8.5899 ms
2025-07-14 16:45:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 caught stopped signal...
2025-07-14 16:45:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 All dispatchers stopped
2025-07-14 16:45:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 successfully reported itself as stopped in 1.1743 ms
2025-07-14 16:45:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13544:5fd33296 has been stopped in total 999.1122 ms
2025-07-14 16:45:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:45:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd successfully announced in 128.2016 ms
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 successfully announced in 129.2052 ms
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 all the dispatchers started
2025-07-14 16:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd all the dispatchers started
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd caught stopping signal...
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 caught stopping signal...
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 caught stopped signal...
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd caught stopped signal...
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 All dispatchers stopped
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 successfully reported itself as stopped in 3.1997 ms
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:1e045ef6 has been stopped in total 888.0541 ms
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd All dispatchers stopped
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd successfully reported itself as stopped in 1.1666 ms
2025-07-14 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:a75e90fd has been stopped in total 896.7414 ms
2025-07-14 16:47:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:47:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:47:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 successfully announced in 120.8783 ms
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 successfully announced in 132.6299 ms
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 all the dispatchers started
2025-07-14 16:47:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 all the dispatchers started
2025-07-14 16:51:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 caught stopping signal...
2025-07-14 16:51:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 caught stopping signal...
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 caught stopped signal...
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 caught stopped signal...
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 All dispatchers stopped
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 All dispatchers stopped
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 successfully reported itself as stopped in 1.8616 ms
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:3e85abc3 has been stopped in total 983.0209 ms
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 successfully reported itself as stopped in 2.6292 ms
2025-07-14 16:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25648:294475c3 has been stopped in total 983.9472 ms
2025-07-14 16:51:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-14 16:51:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-14 16:51:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 successfully announced in 114.5389 ms
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b successfully announced in 105.3774 ms
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 all the dispatchers started
2025-07-14 16:51:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b all the dispatchers started
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 caught stopping signal...
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b caught stopping signal...
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b All dispatchers stopped
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 All dispatchers stopped
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 successfully reported itself as stopped in 1.5872 ms
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:61b9c184 has been stopped in total 15.6545 ms
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b successfully reported itself as stopped in 2.852 ms
2025-07-14 17:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26508:09fc167b has been stopped in total 15.6508 ms
