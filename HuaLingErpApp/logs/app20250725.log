2025-07-25 08:04:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 08:04:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 08:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 08:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 08:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b successfully announced in 66.8363 ms
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b successfully announced in 66.659 ms
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b all the dispatchers started
2025-07-25 08:04:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b all the dispatchers started
2025-07-25 08:07:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b caught stopping signal...
2025-07-25 08:07:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b caught stopping signal...
2025-07-25 08:07:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b caught stopped signal...
2025-07-25 08:07:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b caught stopped signal...
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b All dispatchers stopped
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b successfully reported itself as stopped in 2.2584 ms
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:ef65b32b has been stopped in total 825.0125 ms
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b All dispatchers stopped
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b successfully reported itself as stopped in 0.7287 ms
2025-07-25 08:07:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31980:290c245b has been stopped in total 832.446 ms
2025-07-25 08:08:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 08:08:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 08:08:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:08:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 successfully announced in 55.7747 ms
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a successfully announced in 62.7605 ms
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 all the dispatchers started
2025-07-25 08:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a all the dispatchers started
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a caught stopping signal...
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 caught stopping signal...
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 All dispatchers stopped
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 successfully reported itself as stopped in 15.7079 ms
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:148fa667 has been stopped in total 497.6709 ms
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a caught stopped signal...
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a All dispatchers stopped
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a successfully reported itself as stopped in 0.8929 ms
2025-07-25 08:08:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26004:33cdfb0a has been stopped in total 597.7758 ms
2025-07-25 08:09:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 08:09:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 08:09:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:09:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d successfully announced in 46.5 ms
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 successfully announced in 68.6834 ms
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 all the dispatchers started
2025-07-25 08:09:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d all the dispatchers started
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 caught stopping signal...
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d caught stopping signal...
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d All dispatchers stopped
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 All dispatchers stopped
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 successfully reported itself as stopped in 1.5359 ms
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:39f7e848 has been stopped in total 467.4586 ms
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d successfully reported itself as stopped in 2.974 ms
2025-07-25 08:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27720:4491597d has been stopped in total 467.5183 ms
2025-07-25 08:19:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 08:19:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 08:19:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:19:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 successfully announced in 67.8883 ms
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 successfully announced in 68.0535 ms
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 all the dispatchers started
2025-07-25 08:19:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 all the dispatchers started
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 caught stopping signal...
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 caught stopping signal...
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 caught stopped signal...
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 caught stopped signal...
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 All dispatchers stopped
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 successfully reported itself as stopped in 2.1471 ms
2025-07-25 08:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:6a427468 has been stopped in total 704.2264 ms
2025-07-25 08:26:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 All dispatchers stopped
2025-07-25 08:26:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 successfully reported itself as stopped in 0.6532 ms
2025-07-25 08:26:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20680:90b74ea0 has been stopped in total 806.5415 ms
2025-07-25 08:39:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 08:39:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 08:39:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 08:39:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 successfully announced in 66.3706 ms
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a successfully announced in 66.545 ms
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 all the dispatchers started
2025-07-25 08:39:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a all the dispatchers started
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 caught stopping signal...
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a caught stopping signal...
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a All dispatchers stopped
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 All dispatchers stopped
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a successfully reported itself as stopped in 2.3896 ms
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:655d450a has been stopped in total 499.9946 ms
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 successfully reported itself as stopped in 0.5921 ms
2025-07-25 09:03:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18208:dd115ca7 has been stopped in total 501.0349 ms
2025-07-25 09:03:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:03:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:03:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:03:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af successfully announced in 62.9294 ms
2025-07-25 09:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 successfully announced in 63.1291 ms
2025-07-25 09:03:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:03:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:03:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af all the dispatchers started
2025-07-25 09:03:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 all the dispatchers started
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 caught stopping signal...
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af caught stopping signal...
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 All dispatchers stopped
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 successfully reported itself as stopped in 2.6322 ms
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:1f93bb61 has been stopped in total 455.6861 ms
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af All dispatchers stopped
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af successfully reported itself as stopped in 0.8918 ms
2025-07-25 09:10:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28084:6c6339af has been stopped in total 839.0438 ms
2025-07-25 09:12:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:12:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:12:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:12:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b successfully announced in 51.0976 ms
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 successfully announced in 63.2123 ms
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 all the dispatchers started
2025-07-25 09:12:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b all the dispatchers started
2025-07-25 09:13:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 caught stopping signal...
2025-07-25 09:13:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b caught stopping signal...
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b caught stopped signal...
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 caught stopped signal...
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 All dispatchers stopped
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 successfully reported itself as stopped in 2.002 ms
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:49496f97 has been stopped in total 619.6786 ms
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b All dispatchers stopped
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b successfully reported itself as stopped in 0.9735 ms
2025-07-25 09:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17836:1cf3cc1b has been stopped in total 716.2114 ms
2025-07-25 09:13:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:13:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:13:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:13:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 successfully announced in 61.3634 ms
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 successfully announced in 61.5613 ms
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 all the dispatchers started
2025-07-25 09:13:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 all the dispatchers started
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 caught stopping signal...
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 caught stopping signal...
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 All dispatchers stopped
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 caught stopped signal...
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 caught stopped signal...
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 successfully reported itself as stopped in 3.7673 ms
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:ebf588f7 has been stopped in total 525.9351 ms
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 All dispatchers stopped
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 successfully reported itself as stopped in 2.3731 ms
2025-07-25 09:13:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18864:8a18e252 has been stopped in total 563.6767 ms
2025-07-25 09:13:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:13:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:13:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:13:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:13:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:13:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:13:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:13:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 successfully announced in 45.7096 ms
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 successfully announced in 59.2815 ms
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 all the dispatchers started
2025-07-25 09:13:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 all the dispatchers started
2025-07-25 09:17:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 caught stopping signal...
2025-07-25 09:17:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 caught stopping signal...
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 caught stopped signal...
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 caught stopped signal...
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 All dispatchers stopped
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 successfully reported itself as stopped in 1.866 ms
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:7983e365 has been stopped in total 711.1719 ms
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 All dispatchers stopped
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 successfully reported itself as stopped in 1.6268 ms
2025-07-25 09:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18908:b431d938 has been stopped in total 716.5082 ms
2025-07-25 09:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:18:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:18:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:18:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b successfully announced in 62.1391 ms
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb successfully announced in 59.3903 ms
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b all the dispatchers started
2025-07-25 09:18:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb all the dispatchers started
2025-07-25 09:19:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b caught stopping signal...
2025-07-25 09:19:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb caught stopping signal...
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb caught stopped signal...
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b caught stopped signal...
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb All dispatchers stopped
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb successfully reported itself as stopped in 1.766 ms
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:22dc1bbb has been stopped in total 924.9309 ms
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b All dispatchers stopped
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b successfully reported itself as stopped in 0.8211 ms
2025-07-25 09:19:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32668:25ae596b has been stopped in total 953.9678 ms
2025-07-25 09:19:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:19:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:19:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:19:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 successfully announced in 62.5033 ms
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 successfully announced in 62.6744 ms
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 all the dispatchers started
2025-07-25 09:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 all the dispatchers started
2025-07-25 09:20:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 caught stopping signal...
2025-07-25 09:20:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 caught stopping signal...
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 All dispatchers stopped
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 All dispatchers stopped
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 successfully reported itself as stopped in 1.7836 ms
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 successfully reported itself as stopped in 0.9559 ms
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:ac1c40c9 has been stopped in total 447.7989 ms
2025-07-25 09:20:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28080:19b8a344 has been stopped in total 447.6864 ms
2025-07-25 09:20:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:20:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:20:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:20:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 successfully announced in 61.1556 ms
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 successfully announced in 62.5516 ms
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 all the dispatchers started
2025-07-25 09:20:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 all the dispatchers started
2025-07-25 09:22:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 caught stopping signal...
2025-07-25 09:22:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 caught stopping signal...
2025-07-25 09:22:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 caught stopped signal...
2025-07-25 09:22:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 caught stopped signal...
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 All dispatchers stopped
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 successfully reported itself as stopped in 1.5491 ms
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:d24e28f3 has been stopped in total 872.2116 ms
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 All dispatchers stopped
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 successfully reported itself as stopped in 0.6217 ms
2025-07-25 09:22:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34688:7a018629 has been stopped in total 874.4547 ms
2025-07-25 09:22:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:22:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:22:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:22:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:22:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:22:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 successfully announced in 70.6425 ms
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a successfully announced in 70.5025 ms
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 all the dispatchers started
2025-07-25 09:22:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a all the dispatchers started
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a caught stopping signal...
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 caught stopping signal...
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 All dispatchers stopped
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a All dispatchers stopped
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 successfully reported itself as stopped in 1.9946 ms
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:1bd09f08 has been stopped in total 80.7416 ms
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a successfully reported itself as stopped in 12.8112 ms
2025-07-25 09:25:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14756:4202f40a has been stopped in total 92.7083 ms
2025-07-25 09:26:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:26:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:26:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:26:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:26:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:26:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e3adb472 successfully announced in 172.4607 ms
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e1c2e000 successfully announced in 172.3196 ms
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e3adb472 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e1c2e000 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e3adb472 all the dispatchers started
2025-07-25 09:26:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:e1c2e000 all the dispatchers started
2025-07-25 09:30:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:30:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:30:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:30:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d successfully announced in 61.1864 ms
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d successfully announced in 63.8747 ms
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d all the dispatchers started
2025-07-25 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d all the dispatchers started
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d caught stopping signal...
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d caught stopping signal...
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d All dispatchers stopped
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d All dispatchers stopped
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d successfully reported itself as stopped in 1.4989 ms
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d successfully reported itself as stopped in 1.7902 ms
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:5f70a01d has been stopped in total 249.9469 ms
2025-07-25 09:32:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c282677d has been stopped in total 250.5119 ms
2025-07-25 09:32:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:32:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 successfully announced in 62.9638 ms
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 successfully announced in 63.0437 ms
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 all the dispatchers started
2025-07-25 09:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 all the dispatchers started
2025-07-25 09:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 caught stopping signal...
2025-07-25 09:34:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 caught stopping signal...
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 caught stopped signal...
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 caught stopped signal...
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 All dispatchers stopped
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 successfully reported itself as stopped in 2.0397 ms
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:7bfa4070 has been stopped in total 773.4008 ms
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 All dispatchers stopped
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 successfully reported itself as stopped in 0.8537 ms
2025-07-25 09:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32068:31e1f7f6 has been stopped in total 826.0978 ms
2025-07-25 09:34:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:34:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:34:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:34:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff successfully announced in 58.5923 ms
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 successfully announced in 58.7458 ms
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:34:24 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 all the dispatchers started
2025-07-25 09:34:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff all the dispatchers started
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 caught stopping signal...
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff caught stopping signal...
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff All dispatchers stopped
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff successfully reported itself as stopped in 1.917 ms
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:9bb7afff has been stopped in total 347.4943 ms
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 All dispatchers stopped
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 successfully reported itself as stopped in 1.2516 ms
2025-07-25 09:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13708:20181f84 has been stopped in total 399.5589 ms
2025-07-25 09:37:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:37:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:37:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:37:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:37:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:37:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:37:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:37:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d successfully announced in 64.7281 ms
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c successfully announced in 64.9442 ms
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c all the dispatchers started
2025-07-25 09:37:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d all the dispatchers started
2025-07-25 09:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d caught stopping signal...
2025-07-25 09:39:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c caught stopping signal...
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c caught stopped signal...
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d caught stopped signal...
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c All dispatchers stopped
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c successfully reported itself as stopped in 2.3215 ms
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:a045da4c has been stopped in total 792.0979 ms
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d All dispatchers stopped
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d successfully reported itself as stopped in 1.6352 ms
2025-07-25 09:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17468:d861bc5d has been stopped in total 874.377 ms
2025-07-25 09:39:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:39:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:39:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 successfully announced in 61.2366 ms
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 successfully announced in 62.3186 ms
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 all the dispatchers started
2025-07-25 09:39:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 all the dispatchers started
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 caught stopping signal...
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 caught stopping signal...
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 All dispatchers stopped
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 All dispatchers stopped
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 successfully reported itself as stopped in 1.5865 ms
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:8ad6a577 has been stopped in total 302.9308 ms
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 successfully reported itself as stopped in 1.9049 ms
2025-07-25 09:41:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34780:6d9fb374 has been stopped in total 303.609 ms
2025-07-25 09:41:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:41:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:41:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:41:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:41:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:41:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:41:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c successfully announced in 60.7552 ms
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 successfully announced in 67.1357 ms
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c all the dispatchers started
2025-07-25 09:41:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 all the dispatchers started
2025-07-25 09:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c caught stopping signal...
2025-07-25 09:49:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 caught stopping signal...
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c All dispatchers stopped
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c successfully reported itself as stopped in 1.8206 ms
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:6453064c has been stopped in total 305.0192 ms
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 caught stopped signal...
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 All dispatchers stopped
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 successfully reported itself as stopped in 0.9255 ms
2025-07-25 09:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31096:e7a0b547 has been stopped in total 930.9787 ms
2025-07-25 09:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:49:24 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 successfully announced in 60.9618 ms
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 successfully announced in 70.8173 ms
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 all the dispatchers started
2025-07-25 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 all the dispatchers started
2025-07-25 09:50:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 caught stopping signal...
2025-07-25 09:50:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 caught stopping signal...
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 All dispatchers stopped
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 successfully reported itself as stopped in 1.4711 ms
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:305f9e52 has been stopped in total 242.3338 ms
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 All dispatchers stopped
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 successfully reported itself as stopped in 0.8921 ms
2025-07-25 09:50:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25064:8f0330e1 has been stopped in total 320.2656 ms
2025-07-25 09:50:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:50:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:50:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:50:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 successfully announced in 64.3019 ms
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 successfully announced in 64.1766 ms
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 all the dispatchers started
2025-07-25 09:50:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 all the dispatchers started
2025-07-25 09:52:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 caught stopping signal...
2025-07-25 09:52:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 caught stopping signal...
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 caught stopped signal...
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 caught stopped signal...
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 All dispatchers stopped
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 successfully reported itself as stopped in 1.4615 ms
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:87e32e18 has been stopped in total 835.8151 ms
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 All dispatchers stopped
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 successfully reported itself as stopped in 0.6166 ms
2025-07-25 09:52:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15316:0e98a9c7 has been stopped in total 920.851 ms
2025-07-25 09:52:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:52:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:52:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:52:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b successfully announced in 66.881 ms
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 successfully announced in 67.678 ms
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 all the dispatchers started
2025-07-25 09:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b all the dispatchers started
2025-07-25 09:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b caught stopping signal...
2025-07-25 09:55:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 caught stopping signal...
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 All dispatchers stopped
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 successfully reported itself as stopped in 1.3897 ms
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:66d0b410 has been stopped in total 361.8921 ms
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b caught stopped signal...
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b All dispatchers stopped
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b successfully reported itself as stopped in 0.6419 ms
2025-07-25 09:55:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:82ab7f6b has been stopped in total 508.4131 ms
2025-07-25 09:55:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:55:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:55:57 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:55:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:55:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a successfully announced in 60.3089 ms
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 successfully announced in 63.254 ms
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a all the dispatchers started
2025-07-25 09:55:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 all the dispatchers started
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 caught stopping signal...
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a caught stopping signal...
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a All dispatchers stopped
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 All dispatchers stopped
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a successfully reported itself as stopped in 1.3729 ms
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:85de3e8a has been stopped in total 41.6495 ms
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 successfully reported itself as stopped in 0.6225 ms
2025-07-25 09:57:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18836:a23ba870 has been stopped in total 42.8576 ms
2025-07-25 09:57:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:57:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:57:42 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:57:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:57:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb successfully announced in 71.0972 ms
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 successfully announced in 73.0325 ms
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb all the dispatchers started
2025-07-25 09:57:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 all the dispatchers started
2025-07-25 09:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 caught stopping signal...
2025-07-25 09:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb caught stopping signal...
2025-07-25 09:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb caught stopped signal...
2025-07-25 09:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 caught stopped signal...
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 All dispatchers stopped
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb All dispatchers stopped
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 successfully reported itself as stopped in 2.8374 ms
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:496f32c1 has been stopped in total 901.0807 ms
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb successfully reported itself as stopped in 3.5533 ms
2025-07-25 09:57:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13684:3d0b16cb has been stopped in total 901.0437 ms
2025-07-25 09:57:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 09:57:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 09:57:59 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 09:57:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 09:57:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d successfully announced in 64.2737 ms
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 successfully announced in 64.5668 ms
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 all the dispatchers started
2025-07-25 09:57:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d all the dispatchers started
2025-07-25 09:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d caught stopping signal...
2025-07-25 09:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 caught stopping signal...
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 caught stopped signal...
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d caught stopped signal...
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d All dispatchers stopped
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 All dispatchers stopped
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 successfully reported itself as stopped in 1.7001 ms
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:c4bbf9d9 has been stopped in total 915.0824 ms
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d successfully reported itself as stopped in 3.0305 ms
2025-07-25 09:59:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31724:4a19dd5d has been stopped in total 916.6235 ms
2025-07-25 10:03:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 10:03:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 10:03:12 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 10:03:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:03:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 successfully announced in 68.3752 ms
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 successfully announced in 68.5482 ms
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 all the dispatchers started
2025-07-25 10:03:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 all the dispatchers started
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 caught stopping signal...
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 caught stopping signal...
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 All dispatchers stopped
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 successfully reported itself as stopped in 2.5842 ms
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:1198e927 has been stopped in total 254.749 ms
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 caught stopped signal...
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 All dispatchers stopped
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 successfully reported itself as stopped in 0.8614 ms
2025-07-25 10:28:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:b2b6e6e6 has been stopped in total 891.6034 ms
2025-07-25 10:31:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 10:31:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 10:31:14 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 10:31:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:31:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 successfully announced in 62.5095 ms
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d successfully announced in 62.7543 ms
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d all the dispatchers started
2025-07-25 10:31:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 all the dispatchers started
2025-07-25 10:31:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d caught stopping signal...
2025-07-25 10:31:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 caught stopping signal...
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 caught stopped signal...
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d caught stopped signal...
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d All dispatchers stopped
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d successfully reported itself as stopped in 2.8736 ms
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:9132ea0d has been stopped in total 515.4678 ms
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 All dispatchers stopped
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 successfully reported itself as stopped in 1.0156 ms
2025-07-25 10:31:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27112:61f882f9 has been stopped in total 518.4334 ms
2025-07-25 10:31:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 10:31:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 10:31:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 10:31:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:31:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 successfully announced in 63.7194 ms
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de successfully announced in 63.5618 ms
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de all the dispatchers started
2025-07-25 10:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 all the dispatchers started
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de caught stopping signal...
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 caught stopping signal...
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 caught stopped signal...
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de caught stopped signal...
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 All dispatchers stopped
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de All dispatchers stopped
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 successfully reported itself as stopped in 2.0031 ms
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de successfully reported itself as stopped in 0.687 ms
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:e411be61 has been stopped in total 818.5389 ms
2025-07-25 10:53:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:9acc22de has been stopped in total 818.8859 ms
2025-07-25 10:54:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 10:54:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 10:54:31 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 10:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 10:54:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d successfully announced in 63.4533 ms
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 successfully announced in 63.6681 ms
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 all the dispatchers started
2025-07-25 10:54:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d all the dispatchers started
2025-07-25 11:00:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d caught stopping signal...
2025-07-25 11:00:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 caught stopping signal...
2025-07-25 11:00:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 caught stopped signal...
2025-07-25 11:00:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d caught stopped signal...
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 All dispatchers stopped
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 successfully reported itself as stopped in 2.0174 ms
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:9bdb8c90 has been stopped in total 692.4997 ms
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d All dispatchers stopped
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d successfully reported itself as stopped in 0.7595 ms
2025-07-25 11:00:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20532:7af1ec7d has been stopped in total 936.651 ms
2025-07-25 11:00:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:00:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:00:37 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:00:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:00:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa successfully announced in 60.687 ms
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 successfully announced in 60.8372 ms
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 all the dispatchers started
2025-07-25 11:00:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa all the dispatchers started
2025-07-25 11:05:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa caught stopping signal...
2025-07-25 11:05:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 caught stopping signal...
2025-07-25 11:05:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 caught stopped signal...
2025-07-25 11:05:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa caught stopped signal...
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa All dispatchers stopped
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 All dispatchers stopped
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa successfully reported itself as stopped in 3.2093 ms
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:cea18faa has been stopped in total 939.916 ms
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 successfully reported itself as stopped in 1.7968 ms
2025-07-25 11:05:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15140:d47dd729 has been stopped in total 941.0258 ms
2025-07-25 11:05:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:05:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:05:50 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:05:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:05:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 successfully announced in 43.586 ms
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba successfully announced in 60.7544 ms
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba all the dispatchers started
2025-07-25 11:05:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 all the dispatchers started
2025-07-25 11:06:15 [Error] HuaLingErpApp.Controller.ItemController: Failed to generate Excel template
System.InvalidCastException: Unable to cast object of type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable`1[System.Decimal],System.Nullable`1[System.Decimal],System.String]' to type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Double,System.Double,System.String]'.
   at lambda_method209(Closure, Object)
   at MiniExcelLibs.MemberGetter.Invoke(Object instance)
   at MiniExcelLibs.Property.GetValue(Object instance)
   at MiniExcelLibs.WriteAdapter.EnumerableWriteAdapter.GetRowValues(Object currentValue, List`1 props)+MoveNext()
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.WriteValues(MiniExcelStreamWriter writer, Object values)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.CreateSheetXml(Object values, String sheetPath)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.SaveAs()
   at MiniExcelLibs.MiniExcel.SaveAs(Stream stream, Object value, Boolean printHeader, String sheetName, ExcelType excelType, IConfiguration configuration)
   at HuaLingErpApp.Controller.ItemController.DownloadTemplate() in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 317
2025-07-25 11:06:19 [Error] HuaLingErpApp.Controller.ItemController: Failed to generate Excel template
System.InvalidCastException: Unable to cast object of type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable`1[System.Decimal],System.Nullable`1[System.Decimal],System.String]' to type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Double,System.Double,System.String]'.
   at lambda_method218(Closure, Object)
   at MiniExcelLibs.MemberGetter.Invoke(Object instance)
   at MiniExcelLibs.Property.GetValue(Object instance)
   at MiniExcelLibs.WriteAdapter.EnumerableWriteAdapter.GetRowValues(Object currentValue, List`1 props)+MoveNext()
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.WriteValues(MiniExcelStreamWriter writer, Object values)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.CreateSheetXml(Object values, String sheetPath)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.SaveAs()
   at MiniExcelLibs.MiniExcel.SaveAs(Stream stream, Object value, Boolean printHeader, String sheetName, ExcelType excelType, IConfiguration configuration)
   at HuaLingErpApp.Controller.ItemController.DownloadTemplate() in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 317
2025-07-25 11:08:05 [Error] HuaLingErpApp.Controller.ItemController: Failed to generate Excel template
System.InvalidCastException: Unable to cast object of type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable`1[System.Decimal],System.Nullable`1[System.Decimal],System.String]' to type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Double,System.Double,System.String]'.
   at lambda_method227(Closure, Object)
   at MiniExcelLibs.MemberGetter.Invoke(Object instance)
   at MiniExcelLibs.Property.GetValue(Object instance)
   at MiniExcelLibs.WriteAdapter.EnumerableWriteAdapter.GetRowValues(Object currentValue, List`1 props)+MoveNext()
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.WriteValues(MiniExcelStreamWriter writer, Object values)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.CreateSheetXml(Object values, String sheetPath)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.SaveAs()
   at MiniExcelLibs.MiniExcel.SaveAs(Stream stream, Object value, Boolean printHeader, String sheetName, ExcelType excelType, IConfiguration configuration)
   at HuaLingErpApp.Controller.ItemController.DownloadTemplate() in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 317
2025-07-25 11:08:11 [Error] HuaLingErpApp.Controller.ItemController: Failed to generate Excel template
System.InvalidCastException: Unable to cast object of type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable`1[System.Decimal],System.Nullable`1[System.Decimal],System.String]' to type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Double,System.Double,System.String]'.
   at lambda_method236(Closure, Object)
   at MiniExcelLibs.MemberGetter.Invoke(Object instance)
   at MiniExcelLibs.Property.GetValue(Object instance)
   at MiniExcelLibs.WriteAdapter.EnumerableWriteAdapter.GetRowValues(Object currentValue, List`1 props)+MoveNext()
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.WriteValues(MiniExcelStreamWriter writer, Object values)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.CreateSheetXml(Object values, String sheetPath)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.SaveAs()
   at MiniExcelLibs.MiniExcel.SaveAs(Stream stream, Object value, Boolean printHeader, String sheetName, ExcelType excelType, IConfiguration configuration)
   at HuaLingErpApp.Controller.ItemController.DownloadTemplate() in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 317
2025-07-25 11:08:59 [Error] HuaLingErpApp.Controller.ItemController: Failed to generate Excel template
System.InvalidCastException: Unable to cast object of type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Nullable`1[System.Decimal],System.Nullable`1[System.Decimal],System.String]' to type '<>f__AnonymousType5`9[System.String,System.String,System.String,System.String,System.String,System.String,System.Double,System.Double,System.String]'.
   at lambda_method245(Closure, Object)
   at MiniExcelLibs.MemberGetter.Invoke(Object instance)
   at MiniExcelLibs.Property.GetValue(Object instance)
   at MiniExcelLibs.WriteAdapter.EnumerableWriteAdapter.GetRowValues(Object currentValue, List`1 props)+MoveNext()
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.WriteValues(MiniExcelStreamWriter writer, Object values)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.CreateSheetXml(Object values, String sheetPath)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetWriter.SaveAs()
   at MiniExcelLibs.MiniExcel.SaveAs(Stream stream, Object value, Boolean printHeader, String sheetName, ExcelType excelType, IConfiguration configuration)
   at HuaLingErpApp.Controller.ItemController.DownloadTemplate() in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 317
2025-07-25 11:09:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba caught stopping signal...
2025-07-25 11:09:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 caught stopping signal...
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 caught stopped signal...
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba caught stopped signal...
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 All dispatchers stopped
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 successfully reported itself as stopped in 1.6253 ms
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:9ec57c38 has been stopped in total 631.5152 ms
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba All dispatchers stopped
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba successfully reported itself as stopped in 0.75 ms
2025-07-25 11:09:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10968:ffa591ba has been stopped in total 742.7202 ms
2025-07-25 11:09:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:09:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:09:21 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 successfully announced in 64.6258 ms
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c successfully announced in 64.9523 ms
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c all the dispatchers started
2025-07-25 11:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 all the dispatchers started
2025-07-25 11:12:29 [Error] HuaLingErpApp.Controller.ItemController: Error validating Excel file
System.InvalidOperationException: Object un-ignore properties count can't be 0
   at MiniExcelLibs.Utils.CustomPropertyHelper.GetExcelCustomPropertyInfos(Type type, String[] keys, Configuration configuration)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetReader.QueryImpl[T](IEnumerable`1 values, String startCell, Boolean hasHeader, Configuration configuration)+MoveNext()
   at MiniExcelLibs.MiniExcel.Query[T](Stream stream, String sheetName, ExcelType excelType, String startCell, IConfiguration configuration, Boolean hasHeader)+MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at HuaLingErpApp.Controller.ItemController.ValidateExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 392
2025-07-25 11:18:15 [Error] HuaLingErpApp.Controller.ItemController: Error validating Excel file
System.InvalidOperationException: Object un-ignore properties count can't be 0
   at MiniExcelLibs.Utils.CustomPropertyHelper.GetExcelCustomPropertyInfos(Type type, String[] keys, Configuration configuration)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetReader.QueryImpl[T](IEnumerable`1 values, String startCell, Boolean hasHeader, Configuration configuration)+MoveNext()
   at MiniExcelLibs.MiniExcel.Query[T](Stream stream, String sheetName, ExcelType excelType, String startCell, IConfiguration configuration, Boolean hasHeader)+MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at HuaLingErpApp.Controller.ItemController.ValidateExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 392
2025-07-25 11:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c caught stopping signal...
2025-07-25 11:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 caught stopping signal...
2025-07-25 11:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c All dispatchers stopped
2025-07-25 11:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c successfully reported itself as stopped in 1.6946 ms
2025-07-25 11:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:5b1ebf7c has been stopped in total 117.2748 ms
2025-07-25 11:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 All dispatchers stopped
2025-07-25 11:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 successfully reported itself as stopped in 1.0076 ms
2025-07-25 11:22:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25000:1a970389 has been stopped in total 294.6285 ms
2025-07-25 11:24:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:24:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:24:08 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:24:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:24:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:24:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:24:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 successfully announced in 59.4706 ms
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 successfully announced in 59.693 ms
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 all the dispatchers started
2025-07-25 11:24:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 all the dispatchers started
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 caught stopping signal...
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 caught stopping signal...
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 All dispatchers stopped
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 successfully reported itself as stopped in 1.7312 ms
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:6f8df440 has been stopped in total 194.7562 ms
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 All dispatchers stopped
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 successfully reported itself as stopped in 0.8841 ms
2025-07-25 11:25:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29180:97116262 has been stopped in total 301.1772 ms
2025-07-25 11:25:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:25:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:25:55 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:25:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:25:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 successfully announced in 64.82 ms
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb successfully announced in 66.8719 ms
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 all the dispatchers started
2025-07-25 11:25:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb all the dispatchers started
2025-07-25 11:26:16 [Error] HuaLingErpApp.Controller.ItemController: Error validating Excel file
System.InvalidOperationException: Object un-ignore properties count can't be 0
   at MiniExcelLibs.Utils.CustomPropertyHelper.GetExcelCustomPropertyInfos(Type type, String[] keys, Configuration configuration)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetReader.QueryImpl[T](IEnumerable`1 values, String startCell, Boolean hasHeader, Configuration configuration)+MoveNext()
   at MiniExcelLibs.MiniExcel.Query[T](Stream stream, String sheetName, ExcelType excelType, String startCell, IConfiguration configuration, Boolean hasHeader)+MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at HuaLingErpApp.Controller.ItemController.ValidateExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 392
2025-07-25 11:29:05 [Error] HuaLingErpApp.Controller.ItemController: Error validating Excel file
System.InvalidOperationException: Object un-ignore properties count can't be 0
   at MiniExcelLibs.Utils.CustomPropertyHelper.GetExcelCustomPropertyInfos(Type type, String[] keys, Configuration configuration)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetReader.QueryImpl[T](IEnumerable`1 values, String startCell, Boolean hasHeader, Configuration configuration)+MoveNext()
   at MiniExcelLibs.MiniExcel.Query[T](Stream stream, String sheetName, ExcelType excelType, String startCell, IConfiguration configuration, Boolean hasHeader)+MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at HuaLingErpApp.Controller.ItemController.ValidateExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 392
2025-07-25 11:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb caught stopping signal...
2025-07-25 11:29:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 caught stopping signal...
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 caught stopped signal...
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb caught stopped signal...
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 All dispatchers stopped
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb All dispatchers stopped
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb successfully reported itself as stopped in 1.6279 ms
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:e70649cb has been stopped in total 752.5416 ms
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 successfully reported itself as stopped in 1.8583 ms
2025-07-25 11:29:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1380:5ce50644 has been stopped in total 752.3445 ms
