2025-07-18 15:00:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:00:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:00:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 successfully announced in 75.4446 ms
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 successfully announced in 75.4427 ms
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 all the dispatchers started
2025-07-18 15:00:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 all the dispatchers started
2025-07-18 15:02:39 [Error] HuaLingErpApp.Controller.PurchaseOrderLinesController: Failed to retrieve purchase order lines
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'CreatedBy' 无效。
列名 'ModifiedBy' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.PurchaseOrderLinesController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\PurchaseOrderLinesController.cs:line 26
ClientConnectionId:92321b2e-b72d-46b8-979f-819addd3d054
Error Number:207,State:1,Class:16
2025-07-18 15:02:50 [Error] HuaLingErpApp.Controller.PurchaseOrderLinesController: Failed to retrieve purchase order lines
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'CreatedBy' 无效。
列名 'ModifiedBy' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.PurchaseOrderLinesController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\PurchaseOrderLinesController.cs:line 26
ClientConnectionId:92321b2e-b72d-46b8-979f-819addd3d054
Error Number:207,State:1,Class:16
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 caught stopping signal...
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 caught stopping signal...
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 All dispatchers stopped
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 successfully reported itself as stopped in 2.8026 ms
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:71772363 has been stopped in total 21.5715 ms
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 caught stopped signal...
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 All dispatchers stopped
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 successfully reported itself as stopped in 0.8108 ms
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31408:b56b94e7 has been stopped in total 677.1424 ms
2025-07-18 15:35:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:35:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:35:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 successfully announced in 78.0632 ms
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 successfully announced in 78.0651 ms
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 all the dispatchers started
2025-07-18 15:35:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 all the dispatchers started
2025-07-18 15:35:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 caught stopping signal...
2025-07-18 15:35:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 caught stopping signal...
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 All dispatchers stopped
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 successfully reported itself as stopped in 2.2434 ms
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:24d77425 has been stopped in total 219.5799 ms
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 All dispatchers stopped
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 successfully reported itself as stopped in 1.6447 ms
2025-07-18 15:35:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:e2e69d61 has been stopped in total 222.7804 ms
2025-07-18 15:35:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:35:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:35:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 successfully announced in 77.1615 ms
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b successfully announced in 77.1615 ms
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b all the dispatchers started
2025-07-18 15:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 all the dispatchers started
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 caught stopping signal...
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b caught stopping signal...
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b All dispatchers stopped
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 All dispatchers stopped
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b successfully reported itself as stopped in 2.2823 ms
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:4703735b has been stopped in total 384.1074 ms
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 successfully reported itself as stopped in 1.057 ms
2025-07-18 15:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33108:facd3d55 has been stopped in total 385.3033 ms
