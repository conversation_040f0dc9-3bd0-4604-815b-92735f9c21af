2025-07-21 11:07:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:07:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:08:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:08:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:08:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc successfully announced in 81.7947 ms
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa successfully announced in 82.3304 ms
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc all the dispatchers started
2025-07-21 11:08:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa all the dispatchers started
2025-07-21 11:10:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa caught stopping signal...
2025-07-21 11:10:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc caught stopping signal...
2025-07-21 11:10:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc caught stopped signal...
2025-07-21 11:10:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa caught stopped signal...
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc All dispatchers stopped
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc successfully reported itself as stopped in 1.522 ms
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:19ca50fc has been stopped in total 817.0063 ms
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa All dispatchers stopped
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa successfully reported itself as stopped in 0.7342 ms
2025-07-21 11:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19972:611569aa has been stopped in total 853.4786 ms
2025-07-21 11:11:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:11:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:11:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 successfully announced in 74.9877 ms
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea successfully announced in 82.9902 ms
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 all the dispatchers started
2025-07-21 11:11:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea all the dispatchers started
2025-07-21 11:12:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea caught stopping signal...
2025-07-21 11:12:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 caught stopping signal...
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 All dispatchers stopped
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea All dispatchers stopped
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 successfully reported itself as stopped in 1.4377 ms
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:e9710a40 has been stopped in total 335.1975 ms
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea successfully reported itself as stopped in 1.2728 ms
2025-07-21 11:12:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16804:1215b0ea has been stopped in total 336.1744 ms
2025-07-21 11:13:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:13:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:13:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 successfully announced in 70.7282 ms
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 successfully announced in 70.655 ms
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 all the dispatchers started
2025-07-21 11:13:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 all the dispatchers started
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 caught stopping signal...
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 caught stopping signal...
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 caught stopped signal...
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 caught stopped signal...
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 All dispatchers stopped
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 All dispatchers stopped
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 successfully reported itself as stopped in 2.5684 ms
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:e70ae1b7 has been stopped in total 627.8955 ms
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 successfully reported itself as stopped in 63.7769 ms
2025-07-21 11:14:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3912:f2d6b658 has been stopped in total 690.2273 ms
2025-07-21 11:14:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:14:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:14:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 successfully announced in 83.8429 ms
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 successfully announced in 84.4709 ms
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 all the dispatchers started
2025-07-21 11:14:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 all the dispatchers started
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 caught stopping signal...
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 caught stopping signal...
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 All dispatchers stopped
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 successfully reported itself as stopped in 1.7279 ms
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:13b96191 has been stopped in total 7.6969 ms
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 All dispatchers stopped
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 successfully reported itself as stopped in 0.6811 ms
2025-07-21 11:21:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15888:655ee230 has been stopped in total 23.9666 ms
2025-07-21 11:21:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:21:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:21:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 successfully announced in 83.8931 ms
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff successfully announced in 84.2338 ms
2025-07-21 11:21:18 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 all the dispatchers started
2025-07-21 11:21:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff all the dispatchers started
2025-07-21 11:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff caught stopping signal...
2025-07-21 11:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 caught stopping signal...
2025-07-21 11:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 caught stopped signal...
2025-07-21 11:23:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff caught stopped signal...
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 All dispatchers stopped
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff All dispatchers stopped
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 successfully reported itself as stopped in 1.4069 ms
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:9da51497 has been stopped in total 772.4881 ms
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff successfully reported itself as stopped in 6.6989 ms
2025-07-21 11:23:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31896:0e6a62ff has been stopped in total 779.0533 ms
2025-07-21 11:23:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:23:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:23:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 successfully announced in 79.1342 ms
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c successfully announced in 80.0711 ms
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c all the dispatchers started
2025-07-21 11:23:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 all the dispatchers started
2025-07-21 11:23:26 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c caught stopping signal...
2025-07-21 11:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 caught stopping signal...
2025-07-21 11:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 caught stopped signal...
2025-07-21 11:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c caught stopped signal...
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 All dispatchers stopped
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 successfully reported itself as stopped in 1.6319 ms
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:2d209fc6 has been stopped in total 918.6794 ms
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c All dispatchers stopped
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c successfully reported itself as stopped in 0.71 ms
2025-07-21 11:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:564:11a4df8c has been stopped in total 921.5589 ms
2025-07-21 11:27:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:27:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:27:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 successfully announced in 108.2342 ms
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 successfully announced in 109.2195 ms
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:27:10 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 all the dispatchers started
2025-07-21 11:27:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 all the dispatchers started
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 caught stopping signal...
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 caught stopping signal...
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 All dispatchers stopped
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 successfully reported itself as stopped in 4.2594 ms
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:6f020f42 has been stopped in total 357.7243 ms
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 All dispatchers stopped
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 successfully reported itself as stopped in 1.4128 ms
2025-07-21 11:32:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2128:98639378 has been stopped in total 495.0325 ms
2025-07-21 11:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:32:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:32:57 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed successfully announced in 104.7076 ms
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 successfully announced in 104.7453 ms
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 all the dispatchers started
2025-07-21 11:32:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed all the dispatchers started
2025-07-21 11:33:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 caught stopping signal...
2025-07-21 11:33:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed caught stopping signal...
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed All dispatchers stopped
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed successfully reported itself as stopped in 1.4842 ms
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:67d6cbed has been stopped in total 158.159 ms
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 All dispatchers stopped
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 successfully reported itself as stopped in 0.8932 ms
2025-07-21 11:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21340:a11f8239 has been stopped in total 197.1737 ms
2025-07-21 11:34:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:34:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:34:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 successfully announced in 81.2359 ms
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec successfully announced in 81.5045 ms
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec all the dispatchers started
2025-07-21 11:34:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 all the dispatchers started
2025-07-21 11:34:13 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec caught stopping signal...
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 caught stopping signal...
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 All dispatchers stopped
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 successfully reported itself as stopped in 2.3731 ms
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:6a6d7169 has been stopped in total 236.9034 ms
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec All dispatchers stopped
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec successfully reported itself as stopped in 0.7419 ms
2025-07-21 11:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29736:541adeec has been stopped in total 245.2013 ms
2025-07-21 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:37:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:37:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:37:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:37:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:37:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d successfully announced in 71.5882 ms
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 successfully announced in 84.554 ms
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d all the dispatchers started
2025-07-21 11:37:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 all the dispatchers started
2025-07-21 11:37:50 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 caught stopping signal...
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d caught stopping signal...
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d All dispatchers stopped
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 All dispatchers stopped
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d successfully reported itself as stopped in 1.6766 ms
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:53289d0d has been stopped in total 252.2377 ms
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 successfully reported itself as stopped in 0.6463 ms
2025-07-21 11:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28448:feaa5fa5 has been stopped in total 253.1331 ms
2025-07-21 11:41:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:41:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:42:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 11:42:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 11:42:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f successfully announced in 88.3434 ms
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 successfully announced in 88.5152 ms
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f all the dispatchers started
2025-07-21 11:42:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 all the dispatchers started
2025-07-21 11:42:34 [Warning] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
2025-07-21 11:51:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 caught stopping signal...
2025-07-21 11:51:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f caught stopping signal...
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f caught stopped signal...
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 caught stopped signal...
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 All dispatchers stopped
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 successfully reported itself as stopped in 8.1344 ms
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:9054d4e0 has been stopped in total 937.8407 ms
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f All dispatchers stopped
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f successfully reported itself as stopped in 2.3537 ms
2025-07-21 11:51:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16588:54619a3f has been stopped in total 959.1817 ms
2025-07-21 12:17:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 12:17:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 12:17:56 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:17:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 successfully announced in 83.1507 ms
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab successfully announced in 83.1573 ms
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 all the dispatchers started
2025-07-21 12:17:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab all the dispatchers started
2025-07-21 12:18:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab caught stopping signal...
2025-07-21 12:18:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 caught stopping signal...
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 caught stopped signal...
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab caught stopped signal...
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab All dispatchers stopped
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab successfully reported itself as stopped in 2.0522 ms
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:aa9fd8ab has been stopped in total 889.6893 ms
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 All dispatchers stopped
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 successfully reported itself as stopped in 1.6671 ms
2025-07-21 12:18:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23816:02c4cea1 has been stopped in total 929.0221 ms
2025-07-21 12:18:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 12:18:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 12:18:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:18:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d successfully announced in 82.7642 ms
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a successfully announced in 82.7623 ms
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a all the dispatchers started
2025-07-21 12:18:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d all the dispatchers started
2025-07-21 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a caught stopping signal...
2025-07-21 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d caught stopping signal...
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d caught stopped signal...
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a caught stopped signal...
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d All dispatchers stopped
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a All dispatchers stopped
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a successfully reported itself as stopped in 2.3321 ms
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:cac03c2a has been stopped in total 529.8461 ms
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d successfully reported itself as stopped in 2.5278 ms
2025-07-21 12:19:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21760:f14f0f2d has been stopped in total 529.823 ms
2025-07-21 12:19:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 12:19:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 12:19:47 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:19:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 successfully announced in 78.5341 ms
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 successfully announced in 78.5326 ms
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 all the dispatchers started
2025-07-21 12:19:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 all the dispatchers started
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 caught stopping signal...
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 caught stopping signal...
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 caught stopped signal...
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 caught stopped signal...
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 All dispatchers stopped
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 All dispatchers stopped
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 successfully reported itself as stopped in 2.5758 ms
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:f85e3c67 has been stopped in total 556.1108 ms
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 successfully reported itself as stopped in 1.9104 ms
2025-07-21 12:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24368:a3e80ba0 has been stopped in total 556.729 ms
2025-07-21 12:20:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 12:20:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 12:20:59 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 12:20:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 successfully announced in 75.8571 ms
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 successfully announced in 75.8575 ms
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 all the dispatchers started
2025-07-21 12:20:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 all the dispatchers started
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 caught stopping signal...
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 caught stopping signal...
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 All dispatchers stopped
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 All dispatchers stopped
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 successfully reported itself as stopped in 1.792 ms
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:fed4cb98 has been stopped in total 188.8099 ms
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 successfully reported itself as stopped in 7.1517 ms
2025-07-21 12:21:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11272:7f8c1852 has been stopped in total 193.9758 ms
2025-07-21 13:51:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:51:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role created successfully: "Administrator" - "System Administrator - Has all permissions"
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role created successfully: "Manager" - "Manager - Has management permissions"
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role created successfully: "Operator" - "Operator - Has operation permissions"
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role created successfully: "Viewer" - "Viewer - Has view-only permissions"
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user created successfully: "<EMAIL>"
2025-07-21 13:51:26 [Warning] HuaLingErpApp.Services.DatabaseSeeder: Default admin password is 'Admin123!' - Please change it after first login for security
2025-07-21 13:51:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 successfully announced in 62.7103 ms
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 successfully announced in 63.102 ms
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 all the dispatchers started
2025-07-21 13:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 all the dispatchers started
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 caught stopping signal...
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 caught stopping signal...
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 All dispatchers stopped
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 All dispatchers stopped
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 successfully reported itself as stopped in 2.4772 ms
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:d82b4e37 has been stopped in total 328.9654 ms
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 successfully reported itself as stopped in 2.4358 ms
2025-07-21 13:51:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:55990650 has been stopped in total 329.3377 ms
2025-07-21 13:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 13:51:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:51:46 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

/ (/)
/ (/)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 successfully announced in 61.4755 ms
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 successfully announced in 61.8119 ms
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 all the dispatchers started
2025-07-21 13:51:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 all the dispatchers started
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 caught stopping signal...
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 caught stopping signal...
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 caught stopped signal...
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 caught stopped signal...
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 All dispatchers stopped
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 successfully reported itself as stopped in 2.1852 ms
2025-07-21 13:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:5f244938 has been stopped in total 800.4489 ms
2025-07-21 13:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 All dispatchers stopped
2025-07-21 13:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 successfully reported itself as stopped in 1.0281 ms
2025-07-21 13:52:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33284:e6070e77 has been stopped in total 848.1779 ms
