2025-07-28 09:56:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:56:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:56:42 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:56:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 successfully announced in 61.8749 ms
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c successfully announced in 62.105 ms
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 all the dispatchers started
2025-07-28 09:56:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c all the dispatchers started
2025-07-28 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c caught stopping signal...
2025-07-28 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 caught stopping signal...
2025-07-28 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 caught stopped signal...
2025-07-28 10:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c caught stopped signal...
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 All dispatchers stopped
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c All dispatchers stopped
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 successfully reported itself as stopped in 1.9939 ms
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:de7857a7 has been stopped in total 946.3504 ms
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c successfully reported itself as stopped in 1.0402 ms
2025-07-28 10:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9156:04d4692c has been stopped in total 946.9868 ms
2025-07-28 10:03:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 10:03:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 10:03:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 10:03:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 10:03:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b successfully announced in 60.2946 ms
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 successfully announced in 60.469 ms
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 all the dispatchers started
2025-07-28 10:03:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b all the dispatchers started
2025-07-28 10:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 caught stopping signal...
2025-07-28 10:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b caught stopping signal...
2025-07-28 10:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b caught stopped signal...
2025-07-28 10:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 caught stopped signal...
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b All dispatchers stopped
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 All dispatchers stopped
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 successfully reported itself as stopped in 1.5575 ms
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:e301ed03 has been stopped in total 748.4804 ms
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b successfully reported itself as stopped in 13.4388 ms
2025-07-28 10:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14500:aaf53c8b has been stopped in total 760.1792 ms
2025-07-28 10:04:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 10:04:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 10:04:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 10:04:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 10:04:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 successfully announced in 101.9929 ms
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 successfully announced in 102.0684 ms
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 all the dispatchers started
2025-07-28 10:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 all the dispatchers started
2025-07-28 10:05:43 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 10:05:43 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 10:05:43 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 0 processed, 16 skipped, 0 errors
2025-07-28 10:05:53 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 10:05:53 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 10:05:53 [Information] HuaLingErpApp.Controller.ItemController: Successfully imported 16 items
2025-07-28 10:05:53 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 10:07:18 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 10:07:18 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 10:07:18 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 16 items
2025-07-28 10:07:18 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 caught stopping signal...
2025-07-28 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 caught stopping signal...
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 caught stopped signal...
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 caught stopped signal...
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 All dispatchers stopped
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 successfully reported itself as stopped in 2.1357 ms
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:793028e2 has been stopped in total 995.0715 ms
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 All dispatchers stopped
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 successfully reported itself as stopped in 0.665 ms
2025-07-28 10:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18116:325247b1 has been stopped in total 997.3666 ms
2025-07-28 11:18:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 11:18:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 11:18:39 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 11:18:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:18:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab successfully announced in 105.9552 ms
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 successfully announced in 106.0629 ms
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 all the dispatchers started
2025-07-28 11:18:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab all the dispatchers started
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab caught stopping signal...
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 caught stopping signal...
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab All dispatchers stopped
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab successfully reported itself as stopped in 1.9107 ms
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:1732a0ab has been stopped in total 183.5487 ms
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 All dispatchers stopped
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 successfully reported itself as stopped in 0.883 ms
2025-07-28 11:19:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14728:808e6018 has been stopped in total 273.6417 ms
2025-07-28 11:19:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 11:19:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 11:19:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 11:19:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:19:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab successfully announced in 105.2634 ms
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b successfully announced in 105.148 ms
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab all the dispatchers started
2025-07-28 11:19:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b all the dispatchers started
2025-07-28 11:23:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b caught stopping signal...
2025-07-28 11:23:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab caught stopping signal...
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab caught stopped signal...
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b caught stopped signal...
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab All dispatchers stopped
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b All dispatchers stopped
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab successfully reported itself as stopped in 1.9276 ms
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:d0a3a4ab has been stopped in total 932.6627 ms
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b successfully reported itself as stopped in 1.3505 ms
2025-07-28 11:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14924:f238387b has been stopped in total 933.9385 ms
2025-07-28 11:23:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 11:23:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 11:23:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 11:23:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:23:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 successfully announced in 103.1123 ms
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f successfully announced in 104.1624 ms
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 all the dispatchers started
2025-07-28 11:23:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f all the dispatchers started
2025-07-28 11:23:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f caught stopping signal...
2025-07-28 11:23:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 caught stopping signal...
2025-07-28 11:23:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 caught stopped signal...
2025-07-28 11:23:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f caught stopped signal...
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 All dispatchers stopped
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f All dispatchers stopped
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f successfully reported itself as stopped in 2.9722 ms
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 successfully reported itself as stopped in 1.2168 ms
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:7855e73f has been stopped in total 986.472 ms
2025-07-28 11:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16056:118ebb56 has been stopped in total 986.2775 ms
2025-07-28 11:24:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 11:24:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 11:24:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 11:24:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 11:24:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e successfully announced in 103.1203 ms
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a successfully announced in 103.1236 ms
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e all the dispatchers started
2025-07-28 11:24:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a all the dispatchers started
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e caught stopping signal...
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a caught stopping signal...
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a All dispatchers stopped
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a successfully reported itself as stopped in 2.2423 ms
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:2eebb23a has been stopped in total 254.0055 ms
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e All dispatchers stopped
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e successfully reported itself as stopped in 0.998 ms
2025-07-28 11:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14484:429feb3e has been stopped in total 533.1041 ms
2025-07-28 13:01:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:01:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:01:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:01:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:01:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:01:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:01:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 successfully announced in 111.1749 ms
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 successfully announced in 113.2971 ms
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 all the dispatchers started
2025-07-28 13:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 all the dispatchers started
2025-07-28 13:02:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 caught stopping signal...
2025-07-28 13:02:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 caught stopping signal...
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 All dispatchers stopped
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 caught stopped signal...
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 caught stopped signal...
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 successfully reported itself as stopped in 2.0071 ms
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:a9a1a7a2 has been stopped in total 502.4228 ms
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 All dispatchers stopped
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 successfully reported itself as stopped in 0.6349 ms
2025-07-28 13:02:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16124:3dbc8491 has been stopped in total 526.0722 ms
2025-07-28 13:04:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:04:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:04:31 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:04:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:04:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 successfully announced in 102.3846 ms
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 successfully announced in 102.3754 ms
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 all the dispatchers started
2025-07-28 13:04:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 all the dispatchers started
2025-07-28 13:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 caught stopping signal...
2025-07-28 13:15:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 caught stopping signal...
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 All dispatchers stopped
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 All dispatchers stopped
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 successfully reported itself as stopped in 2.4252 ms
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:cf02a746 has been stopped in total 430.0464 ms
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 successfully reported itself as stopped in 1.9371 ms
2025-07-28 13:15:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4868:11c5e8b7 has been stopped in total 431.0372 ms
2025-07-28 13:16:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:16:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:16:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:16:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:16:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 successfully announced in 104.6515 ms
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e successfully announced in 104.7288 ms
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e all the dispatchers started
2025-07-28 13:16:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 all the dispatchers started
2025-07-28 13:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e caught stopping signal...
2025-07-28 13:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 caught stopping signal...
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 caught stopped signal...
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e caught stopped signal...
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e All dispatchers stopped
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e successfully reported itself as stopped in 1.6004 ms
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:d69c5c6e has been stopped in total 693.7202 ms
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 All dispatchers stopped
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 successfully reported itself as stopped in 0.9962 ms
2025-07-28 13:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20764:96bbf4d2 has been stopped in total 791.7491 ms
2025-07-28 13:17:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:17:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:17:13 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:17:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:17:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 successfully announced in 101.274 ms
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 successfully announced in 101.2773 ms
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 all the dispatchers started
2025-07-28 13:17:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 all the dispatchers started
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 caught stopping signal...
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 caught stopping signal...
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 All dispatchers stopped
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 successfully reported itself as stopped in 1.7242 ms
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:27249dc2 has been stopped in total 237.6783 ms
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 All dispatchers stopped
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 successfully reported itself as stopped in 1.0021 ms
2025-07-28 13:19:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12880:a6b14f45 has been stopped in total 352.3239 ms
2025-07-28 13:19:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:19:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:19:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:19:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:19:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:19:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:19:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 successfully announced in 105.0157 ms
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 successfully announced in 105.0974 ms
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 all the dispatchers started
2025-07-28 13:19:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 all the dispatchers started
2025-07-28 13:24:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 caught stopping signal...
2025-07-28 13:24:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 caught stopping signal...
2025-07-28 13:24:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 All dispatchers stopped
2025-07-28 13:24:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 successfully reported itself as stopped in 1.6869 ms
2025-07-28 13:24:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:22f6af93 has been stopped in total 137.406 ms
2025-07-28 13:24:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 caught stopped signal...
2025-07-28 13:24:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 All dispatchers stopped
2025-07-28 13:24:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 successfully reported itself as stopped in 1.0889 ms
2025-07-28 13:24:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20616:d5cd9768 has been stopped in total 998.1617 ms
2025-07-28 13:24:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:24:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:24:21 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:24:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:24:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 successfully announced in 108.6863 ms
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c successfully announced in 108.7604 ms
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 all the dispatchers started
2025-07-28 13:24:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c all the dispatchers started
2025-07-28 13:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c caught stopping signal...
2025-07-28 13:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 caught stopping signal...
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 caught stopped signal...
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c caught stopped signal...
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c All dispatchers stopped
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 All dispatchers stopped
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c successfully reported itself as stopped in 1.8448 ms
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:de1ecd7c has been stopped in total 903.6126 ms
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 successfully reported itself as stopped in 0.9621 ms
2025-07-28 13:25:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11444:868012b7 has been stopped in total 903.6008 ms
2025-07-28 13:25:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:25:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:25:29 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:25:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:25:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 successfully announced in 103.3578 ms
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a successfully announced in 103.4442 ms
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 all the dispatchers started
2025-07-28 13:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a all the dispatchers started
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a caught stopping signal...
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 caught stopping signal...
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 All dispatchers stopped
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 successfully reported itself as stopped in 2.8718 ms
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:2dd57577 has been stopped in total 431.4875 ms
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a All dispatchers stopped
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a successfully reported itself as stopped in 0.8866 ms
2025-07-28 13:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18236:9b10790a has been stopped in total 451.5637 ms
2025-07-28 13:31:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:31:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:31:24 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:31:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec successfully announced in 106.0398 ms
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 successfully announced in 105.8552 ms
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 all the dispatchers started
2025-07-28 13:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec all the dispatchers started
2025-07-28 13:37:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 caught stopping signal...
2025-07-28 13:37:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec caught stopping signal...
2025-07-28 13:37:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec caught stopped signal...
2025-07-28 13:37:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 caught stopped signal...
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec All dispatchers stopped
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec successfully reported itself as stopped in 2.0694 ms
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:329e36ec has been stopped in total 957.8781 ms
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 All dispatchers stopped
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 successfully reported itself as stopped in 0.721 ms
2025-07-28 13:37:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18944:b0566408 has been stopped in total 992.1249 ms
2025-07-28 13:37:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:37:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:37:45 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:37:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:37:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:37:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:37:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 successfully announced in 98.8294 ms
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e successfully announced in 98.8279 ms
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 all the dispatchers started
2025-07-28 13:37:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e all the dispatchers started
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e caught stopping signal...
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 caught stopping signal...
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 All dispatchers stopped
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 successfully reported itself as stopped in 2.1782 ms
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:ab3763c7 has been stopped in total 23.3558 ms
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e All dispatchers stopped
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e successfully reported itself as stopped in 0.8804 ms
2025-07-28 13:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13892:d0ce130e has been stopped in total 70.6729 ms
2025-07-28 13:39:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:39:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:39:17 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:39:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:39:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 successfully announced in 102.2234 ms
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 successfully announced in 102.2479 ms
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 all the dispatchers started
2025-07-28 13:39:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 all the dispatchers started
2025-07-28 13:40:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 caught stopping signal...
2025-07-28 13:40:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 caught stopping signal...
2025-07-28 13:40:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 caught stopped signal...
2025-07-28 13:40:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 caught stopped signal...
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 All dispatchers stopped
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 successfully reported itself as stopped in 1.9639 ms
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:966a9f45 has been stopped in total 821.7046 ms
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 All dispatchers stopped
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 successfully reported itself as stopped in 1.1182 ms
2025-07-28 13:40:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22520:72210552 has been stopped in total 906.4071 ms
2025-07-28 13:40:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:40:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:40:51 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:40:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:40:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a successfully announced in 103.3145 ms
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b successfully announced in 103.534 ms
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a all the dispatchers started
2025-07-28 13:40:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b all the dispatchers started
2025-07-28 13:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b caught stopping signal...
2025-07-28 13:42:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a caught stopping signal...
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a caught stopped signal...
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b caught stopped signal...
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a All dispatchers stopped
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a successfully reported itself as stopped in 2.0906 ms
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:229b5c7a has been stopped in total 779.7773 ms
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b All dispatchers stopped
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b successfully reported itself as stopped in 0.835 ms
2025-07-28 13:42:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20428:3f29120b has been stopped in total 810.9214 ms
2025-07-28 13:42:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:42:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:42:13 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:42:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:42:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:42:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:42:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:42:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:42:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 successfully announced in 101.0718 ms
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 successfully announced in 101.1234 ms
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 all the dispatchers started
2025-07-28 13:42:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 all the dispatchers started
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 caught stopping signal...
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 caught stopping signal...
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 All dispatchers stopped
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 All dispatchers stopped
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 successfully reported itself as stopped in 2.0038 ms
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:931c48b0 has been stopped in total 307.0487 ms
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 successfully reported itself as stopped in 0.9852 ms
2025-07-28 13:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17840:189b2ba2 has been stopped in total 307.6423 ms
2025-07-28 13:47:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:47:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:47:18 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:47:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:47:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:47:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:47:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:47:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:47:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 successfully announced in 106.8276 ms
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 successfully announced in 106.5326 ms
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 all the dispatchers started
2025-07-28 13:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 all the dispatchers started
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 caught stopping signal...
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 caught stopping signal...
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 All dispatchers stopped
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 successfully reported itself as stopped in 1.9492 ms
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:b678cf97 has been stopped in total 393.2135 ms
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 caught stopped signal...
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 All dispatchers stopped
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 successfully reported itself as stopped in 0.9892 ms
2025-07-28 13:51:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12388:90b72b58 has been stopped in total 577.4292 ms
2025-07-28 13:51:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 13:51:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 13:51:29 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 13:51:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 13:51:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 successfully announced in 103.6084 ms
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 successfully announced in 103.6901 ms
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 all the dispatchers started
2025-07-28 13:51:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 all the dispatchers started
2025-07-28 13:59:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 caught stopping signal...
2025-07-28 13:59:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 caught stopping signal...
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 All dispatchers stopped
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 All dispatchers stopped
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 successfully reported itself as stopped in 14.6025 ms
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 has been stopped in total 623.6339 ms
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:9295c590 caught stopped signal...
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 successfully reported itself as stopped in 1.1131 ms
2025-07-28 13:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20204:66b11b46 has been stopped in total 624.7327 ms
2025-07-28 14:16:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 14:16:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 14:16:08 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 14:16:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 14:16:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 14:16:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 14:16:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 successfully announced in 65.1685 ms
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 successfully announced in 64.9486 ms
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 all the dispatchers started
2025-07-28 14:16:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 all the dispatchers started
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 caught stopping signal...
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 caught stopping signal...
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 All dispatchers stopped
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 All dispatchers stopped
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 successfully reported itself as stopped in 1.8781 ms
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:d56b7225 has been stopped in total 271.8138 ms
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 successfully reported itself as stopped in 2.1606 ms
2025-07-28 14:18:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2868:9c9cdfd1 has been stopped in total 271.8046 ms
