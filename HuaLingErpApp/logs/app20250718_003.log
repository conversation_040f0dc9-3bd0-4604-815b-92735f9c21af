2025-07-18 16:13:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 16:13:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 16:13:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:13:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 16:13:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 successfully announced in 64.7709 ms
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 successfully announced in 65.6228 ms
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 all the dispatchers started
2025-07-18 16:13:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 all the dispatchers started
2025-07-18 16:14:23 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LoginModel: User logged in.
2025-07-18 16:15:10 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LogoutModel: User logged out.
2025-07-18 16:15:31 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LoginModel: User logged in.
2025-07-18 16:16:16 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LogoutModel: User logged out.
2025-07-18 16:16:21 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LoginModel: User logged in.
2025-07-18 16:36:02 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LogoutModel: User logged out.
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 caught stopping signal...
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 caught stopping signal...
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 All dispatchers stopped
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 successfully reported itself as stopped in 3.0469 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:10e30c71 has been stopped in total 360.8306 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 All dispatchers stopped
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 successfully reported itself as stopped in 1.5817 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34344:eb5cb1e4 has been stopped in total 395.5175 ms
