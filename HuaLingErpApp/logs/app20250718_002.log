2025-07-18 15:36:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:36:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:36:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 successfully announced in 70.8067 ms
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f successfully announced in 70.4293 ms
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 all the dispatchers started
2025-07-18 15:36:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f all the dispatchers started
2025-07-18 15:37:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 caught stopping signal...
2025-07-18 15:37:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f caught stopping signal...
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f All dispatchers stopped
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f successfully reported itself as stopped in 3.6046 ms
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:87125f8f has been stopped in total 450.0636 ms
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 caught stopped signal...
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 All dispatchers stopped
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 successfully reported itself as stopped in 1.193 ms
2025-07-18 15:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19916:510b0e83 has been stopped in total 518.2795 ms
2025-07-18 15:54:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:54:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:54:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 创建角色成功: "Administrator" - "系统管理员 - 拥有所有权限"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 创建角色成功: "Manager" - "管理员 - 拥有管理权限"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 创建角色成功: "Operator" - "操作员 - 拥有操作权限"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 创建角色成功: "Viewer" - "查看者 - 只有查看权限"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户创建成功: "<EMAIL>"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员默认密码: "Admin123!"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 普通用户创建成功: "经理用户" ("<EMAIL>") - 角色: "Manager"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户 "<EMAIL>" 默认密码: "Manager123!"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 普通用户创建成功: "操作员用户" ("<EMAIL>") - 角色: "Operator"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户 "<EMAIL>" 默认密码: "Operator123!"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 普通用户创建成功: "查看者用户" ("<EMAIL>") - 角色: "Viewer"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户 "<EMAIL>" 默认密码: "Viewer123!"
2025-07-18 15:54:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:54:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 successfully announced in 67.6813 ms
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e successfully announced in 68.8603 ms
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 all the dispatchers started
2025-07-18 15:54:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e all the dispatchers started
2025-07-18 15:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 caught stopping signal...
2025-07-18 15:58:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e caught stopping signal...
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e caught stopped signal...
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 caught stopped signal...
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e All dispatchers stopped
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 All dispatchers stopped
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e successfully reported itself as stopped in 1.8964 ms
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:33f7e37e has been stopped in total 805.7841 ms
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 successfully reported itself as stopped in 1.1068 ms
2025-07-18 15:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:a36c7846 has been stopped in total 806.2989 ms
2025-07-18 15:58:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 15:58:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 15:58:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 15:58:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 15:58:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 successfully announced in 69.5975 ms
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 successfully announced in 69.5979 ms
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 all the dispatchers started
2025-07-18 15:58:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 all the dispatchers started
2025-07-18 15:58:45 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LoginModel: User logged in.
2025-07-18 15:59:57 [Information] HuaLingErpApp.Areas.Identity.Pages.Account.LogoutModel: User logged out.
2025-07-18 16:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 caught stopping signal...
2025-07-18 16:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 caught stopping signal...
2025-07-18 16:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 All dispatchers stopped
2025-07-18 16:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 successfully reported itself as stopped in 2.1548 ms
2025-07-18 16:06:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:7690da66 has been stopped in total 267.8691 ms
2025-07-18 16:06:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 All dispatchers stopped
2025-07-18 16:06:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 successfully reported itself as stopped in 0.92 ms
2025-07-18 16:06:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15892:16801b75 has been stopped in total 334.0258 ms
2025-07-18 16:11:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 16:11:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 16:11:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-18 16:11:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 16:11:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 successfully announced in 67.3548 ms
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 successfully announced in 67.6212 ms
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 all the dispatchers started
2025-07-18 16:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 all the dispatchers started
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 caught stopping signal...
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 caught stopping signal...
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 caught stopped signal...
2025-07-18 17:00:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 caught stopped signal...
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 All dispatchers stopped
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 successfully reported itself as stopped in 1.8807 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:05469234 has been stopped in total 592.1999 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 All dispatchers stopped
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 successfully reported itself as stopped in 1.0471 ms
2025-07-18 17:00:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23076:c01dc043 has been stopped in total 629.0507 ms
