2025-07-22 10:01:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 10:01:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 10:01:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:01:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 successfully announced in 64.7522 ms
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c successfully announced in 64.5705 ms
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c all the dispatchers started
2025-07-22 10:01:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 all the dispatchers started
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c caught stopping signal...
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 caught stopping signal...
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c All dispatchers stopped
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c successfully reported itself as stopped in 2.7406 ms
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1e5edc2c has been stopped in total 84.5214 ms
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 All dispatchers stopped
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 successfully reported itself as stopped in 0.7364 ms
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14568:1dbeac64 has been stopped in total 303.7775 ms
2025-07-22 10:17:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 10:17:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 10:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b successfully announced in 62.7352 ms
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f successfully announced in 62.9235 ms
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b all the dispatchers started
2025-07-22 10:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f all the dispatchers started
2025-07-22 10:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b caught stopping signal...
2025-07-22 10:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f caught stopping signal...
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f caught stopped signal...
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b caught stopped signal...
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f All dispatchers stopped
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b All dispatchers stopped
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f successfully reported itself as stopped in 1.7572 ms
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:8654527f has been stopped in total 798.3795 ms
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b successfully reported itself as stopped in 1.9005 ms
2025-07-22 10:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31512:f1e7061b has been stopped in total 799.8667 ms
2025-07-22 10:22:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 10:22:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 10:22:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 10:22:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:22:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:22:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:22:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:22:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 successfully announced in 48.7229 ms
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 successfully announced in 65.1798 ms
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 all the dispatchers started
2025-07-22 10:22:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 all the dispatchers started
2025-07-22 10:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 caught stopping signal...
2025-07-22 10:32:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 caught stopping signal...
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 caught stopped signal...
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 caught stopped signal...
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 All dispatchers stopped
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 All dispatchers stopped
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 successfully reported itself as stopped in 2.3632 ms
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 successfully reported itself as stopped in 2.3676 ms
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:c521d972 has been stopped in total 990.5395 ms
2025-07-22 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16380:1162e635 has been stopped in total 991.0191 ms
2025-07-22 11:38:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 11:38:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 11:38:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:38:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 successfully announced in 61.8218 ms
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 successfully announced in 61.8225 ms
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 all the dispatchers started
2025-07-22 11:38:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 all the dispatchers started
2025-07-22 11:40:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 caught stopping signal...
2025-07-22 11:40:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 caught stopping signal...
2025-07-22 11:40:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 All dispatchers stopped
2025-07-22 11:40:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 successfully reported itself as stopped in 13.0955 ms
2025-07-22 11:40:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4b417483 has been stopped in total 292.902 ms
2025-07-22 11:40:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 All dispatchers stopped
2025-07-22 11:40:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 successfully reported itself as stopped in 1.0299 ms
2025-07-22 11:40:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28868:4e9718a7 has been stopped in total 406.0933 ms
2025-07-22 11:40:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 11:40:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 11:40:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:40:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d successfully announced in 64.4796 ms
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 successfully announced in 65.3315 ms
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d all the dispatchers started
2025-07-22 11:40:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 all the dispatchers started
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 caught stopping signal...
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d caught stopping signal...
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d All dispatchers stopped
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d successfully reported itself as stopped in 2.1229 ms
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:cba04a3d has been stopped in total 485.9937 ms
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 caught stopped signal...
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 All dispatchers stopped
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 successfully reported itself as stopped in 0.8885 ms
2025-07-22 11:58:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16104:8f28c322 has been stopped in total 570.3038 ms
2025-07-22 11:59:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 11:59:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 11:59:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 11:59:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 successfully announced in 63.0796 ms
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e successfully announced in 63.0847 ms
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e all the dispatchers started
2025-07-22 11:59:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 all the dispatchers started
2025-07-22 12:18:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e caught stopping signal...
2025-07-22 12:18:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 caught stopping signal...
2025-07-22 12:18:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 caught stopped signal...
2025-07-22 12:18:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e caught stopped signal...
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e All dispatchers stopped
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e successfully reported itself as stopped in 2.1562 ms
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:2663941e has been stopped in total 833.9421 ms
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 All dispatchers stopped
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 successfully reported itself as stopped in 0.8163 ms
2025-07-22 12:18:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27904:086fa745 has been stopped in total 872.224 ms
2025-07-22 13:02:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:02:34 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:02:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:02:35 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:f71cf363 successfully announced in 103.4354 ms
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:efdeb4a3 successfully announced in 103.5666 ms
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:efdeb4a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:f71cf363 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:efdeb4a3 all the dispatchers started
2025-07-22 13:02:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27328:f71cf363 all the dispatchers started
2025-07-22 13:04:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:04:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 successfully announced in 62.6663 ms
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 successfully announced in 62.9858 ms
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 all the dispatchers started
2025-07-22 13:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 all the dispatchers started
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 caught stopping signal...
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 caught stopping signal...
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 caught stopped signal...
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 caught stopped signal...
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 All dispatchers stopped
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 successfully reported itself as stopped in 2.3464 ms
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:1e178505 has been stopped in total 650.3236 ms
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 All dispatchers stopped
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 successfully reported itself as stopped in 38.1548 ms
2025-07-22 13:06:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32756:60468f61 has been stopped in total 691.3412 ms
2025-07-22 13:06:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:06:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:06:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:06:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user created successfully: "<EMAIL>"
2025-07-22 13:06:41 [Warning] HuaLingErpApp.Services.DatabaseSeeder: Default admin password is 'Admin123!' - Please change it after first login for security
2025-07-22 13:06:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:06:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 successfully announced in 61.3517 ms
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 successfully announced in 61.7826 ms
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 all the dispatchers started
2025-07-22 13:06:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 all the dispatchers started
2025-07-22 13:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 caught stopping signal...
2025-07-22 13:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 caught stopping signal...
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 caught stopped signal...
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 caught stopped signal...
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 All dispatchers stopped
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 successfully reported itself as stopped in 1.7854 ms
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:1ed4d9e7 has been stopped in total 839.2945 ms
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 All dispatchers stopped
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 successfully reported itself as stopped in 0.7302 ms
2025-07-22 13:08:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14660:b8972b82 has been stopped in total 852.8454 ms
2025-07-22 13:08:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:08:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:08:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:08:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 successfully announced in 63.261 ms
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 successfully announced in 63.6263 ms
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 all the dispatchers started
2025-07-22 13:08:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 all the dispatchers started
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 caught stopping signal...
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 caught stopping signal...
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 All dispatchers stopped
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 successfully reported itself as stopped in 7.1418 ms
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:bd344879 has been stopped in total 426.8631 ms
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 All dispatchers stopped
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 successfully reported itself as stopped in 0.7082 ms
2025-07-22 13:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20956:7bf83815 has been stopped in total 428.901 ms
2025-07-22 13:10:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:10:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:10:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:10:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:10:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:10:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:10:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:10:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 successfully announced in 62.4952 ms
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 successfully announced in 64.0019 ms
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:10:03 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-22 13:10:03 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 all the dispatchers started
2025-07-22 13:10:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 all the dispatchers started
2025-07-22 13:13:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 caught stopping signal...
2025-07-22 13:13:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 caught stopping signal...
2025-07-22 13:13:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 All dispatchers stopped
2025-07-22 13:13:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 successfully reported itself as stopped in 2.0045 ms
2025-07-22 13:13:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:beb5aac0 has been stopped in total 44.2693 ms
2025-07-22 13:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 caught stopped signal...
2025-07-22 13:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 All dispatchers stopped
2025-07-22 13:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 successfully reported itself as stopped in 1.0578 ms
2025-07-22 13:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29920:f0abd505 has been stopped in total 947.4071 ms
2025-07-22 13:13:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:13:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:13:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:13:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:13:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:13:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:13:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:13:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 successfully announced in 88.5478 ms
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca successfully announced in 68.8021 ms
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca all the dispatchers started
2025-07-22 13:13:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 all the dispatchers started
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 caught stopping signal...
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca caught stopping signal...
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca All dispatchers stopped
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca successfully reported itself as stopped in 2.3174 ms
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:abedc8ca has been stopped in total 121.6562 ms
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 All dispatchers stopped
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 successfully reported itself as stopped in 1.5498 ms
2025-07-22 13:15:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28660:16b3c7a0 has been stopped in total 257.8964 ms
2025-07-22 13:15:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:15:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:15:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:15:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:bbec9907 successfully announced in 103.604 ms
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:1e90d2a6 successfully announced in 103.6011 ms
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:1e90d2a6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:bbec9907 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:bbec9907 all the dispatchers started
2025-07-22 13:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23544:1e90d2a6 all the dispatchers started
2025-07-22 13:57:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 13:57:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 13:57:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 13:57:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 successfully announced in 61.9291 ms
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 successfully announced in 61.7195 ms
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 13:57:22 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 all the dispatchers started
2025-07-22 13:57:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 all the dispatchers started
2025-07-22 14:07:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 caught stopping signal...
2025-07-22 14:07:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 caught stopping signal...
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 caught stopped signal...
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 caught stopped signal...
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 All dispatchers stopped
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 All dispatchers stopped
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 successfully reported itself as stopped in 2.4915 ms
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:580ca7b8 has been stopped in total 776.7823 ms
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 successfully reported itself as stopped in 1.0834 ms
2025-07-22 14:07:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27840:55a20d10 has been stopped in total 777.4019 ms
2025-07-22 14:09:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:09:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:09:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:09:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:09:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:09:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 successfully announced in 67.8938 ms
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 successfully announced in 68.7064 ms
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 all the dispatchers started
2025-07-22 14:09:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 all the dispatchers started
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 caught stopping signal...
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 caught stopping signal...
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 caught stopped signal...
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 All dispatchers stopped
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 caught stopped signal...
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 successfully reported itself as stopped in 1.9628 ms
2025-07-22 14:12:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:315fd5f4 has been stopped in total 505.3327 ms
2025-07-22 14:12:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 All dispatchers stopped
2025-07-22 14:12:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 successfully reported itself as stopped in 0.8002 ms
2025-07-22 14:12:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32412:add55073 has been stopped in total 616.4832 ms
2025-07-22 14:13:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:13:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:13:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:13:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 successfully announced in 60.4983 ms
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 successfully announced in 61.8137 ms
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 all the dispatchers started
2025-07-22 14:13:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 all the dispatchers started
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 caught stopping signal...
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 caught stopping signal...
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 caught stopped signal...
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 caught stopped signal...
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 All dispatchers stopped
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 All dispatchers stopped
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 successfully reported itself as stopped in 2.3303 ms
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:0b93d701 has been stopped in total 835.0405 ms
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 successfully reported itself as stopped in 1.6436 ms
2025-07-22 14:14:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32080:cc4f05a3 has been stopped in total 834.3151 ms
2025-07-22 14:15:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:15:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:15:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:15:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 successfully announced in 61.4231 ms
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 successfully announced in 57.433 ms
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 all the dispatchers started
2025-07-22 14:15:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 all the dispatchers started
2025-07-22 14:17:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 caught stopping signal...
2025-07-22 14:17:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 caught stopping signal...
2025-07-22 14:17:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 caught stopped signal...
2025-07-22 14:17:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 caught stopped signal...
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 All dispatchers stopped
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 successfully reported itself as stopped in 2.2713 ms
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:9f525fa3 has been stopped in total 788.1247 ms
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 All dispatchers stopped
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 successfully reported itself as stopped in 1.0262 ms
2025-07-22 14:17:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11236:5fa26e82 has been stopped in total 827.3549 ms
2025-07-22 14:17:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:17:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:17:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:17:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 successfully announced in 64.7515 ms
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 successfully announced in 65.3139 ms
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 all the dispatchers started
2025-07-22 14:17:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 all the dispatchers started
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 caught stopping signal...
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 caught stopping signal...
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 All dispatchers stopped
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 successfully reported itself as stopped in 1.629 ms
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:e8c04ea3 has been stopped in total 483.5154 ms
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 All dispatchers stopped
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 successfully reported itself as stopped in 1.3373 ms
2025-07-22 14:22:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25316:b1a915c0 has been stopped in total 569.7274 ms
2025-07-22 14:22:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:22:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:22:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:22:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 successfully announced in 60.8193 ms
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 successfully announced in 82.201 ms
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 all the dispatchers started
2025-07-22 14:22:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 all the dispatchers started
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 caught stopping signal...
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 caught stopping signal...
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 All dispatchers stopped
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 successfully reported itself as stopped in 1.5689 ms
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:8b3bd077 has been stopped in total 210.3756 ms
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 All dispatchers stopped
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 successfully reported itself as stopped in 1.1505 ms
2025-07-22 14:23:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22184:fe712446 has been stopped in total 261.6223 ms
2025-07-22 14:23:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:23:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:23:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:23:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 successfully announced in 61.8445 ms
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 successfully announced in 61.9522 ms
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 all the dispatchers started
2025-07-22 14:23:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 all the dispatchers started
2025-07-22 14:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 caught stopping signal...
2025-07-22 14:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 caught stopping signal...
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 caught stopped signal...
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 caught stopped signal...
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 All dispatchers stopped
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 successfully reported itself as stopped in 1.4923 ms
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:7ac5ad00 has been stopped in total 575.4162 ms
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 All dispatchers stopped
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 successfully reported itself as stopped in 3.557 ms
2025-07-22 14:25:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5144:d37b7274 has been stopped in total 605.5413 ms
2025-07-22 14:25:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:25:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:25:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:25:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 successfully announced in 63.6534 ms
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 successfully announced in 63.7479 ms
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 all the dispatchers started
2025-07-22 14:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 all the dispatchers started
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 caught stopping signal...
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 caught stopping signal...
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 caught stopped signal...
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 caught stopped signal...
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 All dispatchers stopped
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 successfully reported itself as stopped in 1.5967 ms
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:a8336004 has been stopped in total 554.7321 ms
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 All dispatchers stopped
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 successfully reported itself as stopped in 1.4908 ms
2025-07-22 14:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19004:ac63b8e6 has been stopped in total 556.9045 ms
2025-07-22 14:28:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:28:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:28:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:28:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 successfully announced in 61.5147 ms
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 successfully announced in 62.404 ms
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 all the dispatchers started
2025-07-22 14:28:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 all the dispatchers started
2025-07-22 14:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 caught stopping signal...
2025-07-22 14:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 caught stopping signal...
2025-07-22 14:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 caught stopped signal...
2025-07-22 14:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 caught stopped signal...
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 All dispatchers stopped
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 All dispatchers stopped
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 successfully reported itself as stopped in 1.9298 ms
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 successfully reported itself as stopped in 2.5322 ms
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:3b575db0 has been stopped in total 782.548 ms
2025-07-22 14:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28788:545fed77 has been stopped in total 782.4715 ms
2025-07-22 14:28:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:28:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:28:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:28:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f successfully announced in 62.3655 ms
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 successfully announced in 62.6308 ms
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 all the dispatchers started
2025-07-22 14:28:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f all the dispatchers started
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f caught stopping signal...
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 caught stopping signal...
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 caught stopped signal...
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f caught stopped signal...
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 All dispatchers stopped
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 successfully reported itself as stopped in 2.2478 ms
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:0289e693 has been stopped in total 627.4705 ms
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f All dispatchers stopped
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f successfully reported itself as stopped in 1.4055 ms
2025-07-22 14:29:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16896:fe3f7c9f has been stopped in total 702.5978 ms
2025-07-22 14:29:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:29:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:29:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:29:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:29:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:29:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e successfully announced in 92.4592 ms
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 successfully announced in 61.4118 ms
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 all the dispatchers started
2025-07-22 14:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e all the dispatchers started
2025-07-22 14:30:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e caught stopping signal...
2025-07-22 14:30:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 caught stopping signal...
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 caught stopped signal...
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e caught stopped signal...
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e All dispatchers stopped
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 All dispatchers stopped
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 successfully reported itself as stopped in 1.3238 ms
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:2e099ee8 has been stopped in total 672.9376 ms
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e successfully reported itself as stopped in 2.5633 ms
2025-07-22 14:30:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7544:6bd4f98e has been stopped in total 673.8147 ms
2025-07-22 14:31:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:31:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:31:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:31:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 successfully announced in 59.8513 ms
2025-07-22 14:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c successfully announced in 60.007 ms
2025-07-22 14:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:31:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 all the dispatchers started
2025-07-22 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c all the dispatchers started
2025-07-22 14:32:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c caught stopping signal...
2025-07-22 14:32:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 caught stopping signal...
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 All dispatchers stopped
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 successfully reported itself as stopped in 1.6052 ms
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:692e8225 has been stopped in total 132.3617 ms
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c All dispatchers stopped
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c successfully reported itself as stopped in 0.8896 ms
2025-07-22 14:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8108:74b2577c has been stopped in total 158.8555 ms
2025-07-22 14:32:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:32:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:32:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:32:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 successfully announced in 60.8918 ms
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 successfully announced in 61.4704 ms
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 all the dispatchers started
2025-07-22 14:32:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 all the dispatchers started
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 caught stopping signal...
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 caught stopping signal...
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 All dispatchers stopped
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 All dispatchers stopped
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 successfully reported itself as stopped in 2.1881 ms
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:09be2a07 has been stopped in total 103.678 ms
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 successfully reported itself as stopped in 46.1761 ms
2025-07-22 14:33:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25816:85f29333 has been stopped in total 147.855 ms
2025-07-22 14:33:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:33:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:33:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:33:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 successfully announced in 60.9109 ms
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 successfully announced in 61.1271 ms
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 all the dispatchers started
2025-07-22 14:33:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 all the dispatchers started
2025-07-22 14:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 caught stopping signal...
2025-07-22 14:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 caught stopping signal...
2025-07-22 14:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 All dispatchers stopped
2025-07-22 14:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 successfully reported itself as stopped in 2.0577 ms
2025-07-22 14:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:c85fe221 has been stopped in total 38.1907 ms
2025-07-22 14:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 caught stopped signal...
2025-07-22 14:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 All dispatchers stopped
2025-07-22 14:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 successfully reported itself as stopped in 1.035 ms
2025-07-22 14:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23104:4cf02ec7 has been stopped in total 918.9516 ms
2025-07-22 14:34:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:34:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:34:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e successfully announced in 63.6904 ms
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 successfully announced in 63.6889 ms
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e all the dispatchers started
2025-07-22 14:34:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 all the dispatchers started
2025-07-22 14:35:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e caught stopping signal...
2025-07-22 14:35:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 caught stopping signal...
2025-07-22 14:35:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 caught stopped signal...
2025-07-22 14:35:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e caught stopped signal...
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 All dispatchers stopped
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 successfully reported itself as stopped in 1.8228 ms
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:1358a013 has been stopped in total 884.1867 ms
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e All dispatchers stopped
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e successfully reported itself as stopped in 0.8489 ms
2025-07-22 14:35:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:6ff9340e has been stopped in total 929.8975 ms
2025-07-22 14:35:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:35:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:35:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:35:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 successfully announced in 60.9724 ms
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c successfully announced in 61.1894 ms
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 all the dispatchers started
2025-07-22 14:35:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c all the dispatchers started
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 caught stopping signal...
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c caught stopping signal...
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c caught stopped signal...
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 caught stopped signal...
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 All dispatchers stopped
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 successfully reported itself as stopped in 1.5344 ms
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:551f3e17 has been stopped in total 536.7326 ms
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c All dispatchers stopped
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c successfully reported itself as stopped in 0.898 ms
2025-07-22 14:38:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33724:7326b30c has been stopped in total 811.4003 ms
2025-07-22 14:38:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:38:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:38:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:38:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 successfully announced in 70.1076 ms
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 successfully announced in 72.8783 ms
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 all the dispatchers started
2025-07-22 14:38:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 all the dispatchers started
2025-07-22 14:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 caught stopping signal...
2025-07-22 14:42:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 caught stopping signal...
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 All dispatchers stopped
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 successfully reported itself as stopped in 1.5557 ms
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:f0d63918 has been stopped in total 221.6692 ms
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 All dispatchers stopped
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 successfully reported itself as stopped in 0.7291 ms
2025-07-22 14:42:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19832:688078f3 has been stopped in total 344.6049 ms
2025-07-22 14:42:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:42:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:42:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:42:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 successfully announced in 64.2972 ms
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 successfully announced in 64.1638 ms
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 all the dispatchers started
2025-07-22 14:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 all the dispatchers started
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 caught stopping signal...
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 caught stopping signal...
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 All dispatchers stopped
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 successfully reported itself as stopped in 1.6898 ms
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:5f83fcf6 has been stopped in total 69.6602 ms
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 All dispatchers stopped
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 successfully reported itself as stopped in 0.7954 ms
2025-07-22 14:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17192:495fcc61 has been stopped in total 201.0052 ms
2025-07-22 14:45:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:45:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:45:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:45:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc successfully announced in 60.0341 ms
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 successfully announced in 60.5093 ms
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 all the dispatchers started
2025-07-22 14:45:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc all the dispatchers started
2025-07-22 14:45:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc caught stopping signal...
2025-07-22 14:45:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 caught stopping signal...
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 caught stopped signal...
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc caught stopped signal...
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc All dispatchers stopped
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 All dispatchers stopped
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc successfully reported itself as stopped in 1.8561 ms
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9c3bd1bc has been stopped in total 581.6128 ms
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 successfully reported itself as stopped in 2.0818 ms
2025-07-22 14:45:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10924:9a15ace8 has been stopped in total 581.6102 ms
2025-07-22 14:45:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:45:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:45:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:45:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:45:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:45:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:45:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb successfully announced in 72.8164 ms
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 successfully announced in 72.6383 ms
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 all the dispatchers started
2025-07-22 14:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb all the dispatchers started
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 caught stopping signal...
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb caught stopping signal...
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 All dispatchers stopped
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 successfully reported itself as stopped in 1.7301 ms
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:dc0f5091 has been stopped in total 146.2509 ms
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb All dispatchers stopped
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb successfully reported itself as stopped in 0.6129 ms
2025-07-22 14:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30404:fa05a4bb has been stopped in total 150.7404 ms
2025-07-22 14:55:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 14:55:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 14:55:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 14:55:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:3ea9968d successfully announced in 99.4428 ms
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:572dcac7 successfully announced in 99.6571 ms
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:3ea9968d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:572dcac7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:3ea9968d all the dispatchers started
2025-07-22 14:55:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13828:572dcac7 all the dispatchers started
2025-07-22 15:00:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:00:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:00:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:00:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:4a5487b4 successfully announced in 98.7591 ms
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:83772e13 successfully announced in 98.7147 ms
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:83772e13 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:4a5487b4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:83772e13 all the dispatchers started
2025-07-22 15:00:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17736:4a5487b4 all the dispatchers started
2025-07-22 15:01:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:01:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:01:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:01:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:01:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:01:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:e52a2811 successfully announced in 107.5813 ms
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:cf3924cc successfully announced in 107.7527 ms
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:e52a2811 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:cf3924cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:cf3924cc all the dispatchers started
2025-07-22 15:01:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28812:e52a2811 all the dispatchers started
2025-07-22 15:07:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:07:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:07:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:07:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:8fd3e9e2 successfully announced in 98.9016 ms
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:120e7bb8 successfully announced in 98.8265 ms
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:120e7bb8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:8fd3e9e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:07:59 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 15:07:59 [Information] Hangfire.Server.ServerWatchdog: 4 servers were removed due to timeout
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:120e7bb8 all the dispatchers started
2025-07-22 15:07:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11200:8fd3e9e2 all the dispatchers started
2025-07-22 15:13:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:13:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:13:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:13:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:13:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:13:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:13:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:13:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:96b1ac4b successfully announced in 100.6339 ms
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:181fa27a successfully announced in 100.7746 ms
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:181fa27a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:96b1ac4b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:181fa27a all the dispatchers started
2025-07-22 15:13:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12704:96b1ac4b all the dispatchers started
2025-07-22 15:16:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:16:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:16:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:16:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab successfully announced in 64.5995 ms
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd successfully announced in 64.5108 ms
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:16:54 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd all the dispatchers started
2025-07-22 15:16:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab all the dispatchers started
2025-07-22 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd caught stopping signal...
2025-07-22 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab caught stopping signal...
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab caught stopped signal...
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd caught stopped signal...
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab All dispatchers stopped
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab successfully reported itself as stopped in 1.9041 ms
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:a4afb8ab has been stopped in total 860.209 ms
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd All dispatchers stopped
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd successfully reported itself as stopped in 0.7599 ms
2025-07-22 15:19:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25536:4438c7bd has been stopped in total 864.5454 ms
2025-07-22 15:19:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:19:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:19:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:19:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb successfully announced in 60.3704 ms
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 successfully announced in 60.0169 ms
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:19:24 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 all the dispatchers started
2025-07-22 15:19:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb all the dispatchers started
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 caught stopping signal...
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb caught stopping signal...
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb caught stopped signal...
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 caught stopped signal...
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 All dispatchers stopped
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 successfully reported itself as stopped in 1.851 ms
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:14af4784 has been stopped in total 537.8542 ms
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb All dispatchers stopped
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb successfully reported itself as stopped in 0.9482 ms
2025-07-22 15:20:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11752:6a230cbb has been stopped in total 565.184 ms
2025-07-22 15:20:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:20:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:20:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:20:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 successfully announced in 63.062 ms
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 successfully announced in 63.3152 ms
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 all the dispatchers started
2025-07-22 15:20:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 all the dispatchers started
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 caught stopping signal...
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 caught stopping signal...
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 caught stopped signal...
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 caught stopped signal...
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 All dispatchers stopped
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 successfully reported itself as stopped in 1.6275 ms
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:b8fd5850 has been stopped in total 657.0673 ms
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 All dispatchers stopped
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 successfully reported itself as stopped in 0.7188 ms
2025-07-22 15:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32152:5e808e80 has been stopped in total 665.6836 ms
2025-07-22 15:21:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:21:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:21:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 successfully announced in 71.8612 ms
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 successfully announced in 72.0858 ms
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 all the dispatchers started
2025-07-22 15:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 all the dispatchers started
2025-07-22 15:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 caught stopping signal...
2025-07-22 15:25:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 caught stopping signal...
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 All dispatchers stopped
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 All dispatchers stopped
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 successfully reported itself as stopped in 3.3844 ms
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:55db3158 has been stopped in total 262.3723 ms
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 successfully reported itself as stopped in 3.4632 ms
2025-07-22 15:25:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12796:9e8c6b55 has been stopped in total 262.2745 ms
2025-07-22 15:25:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:25:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:25:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:25:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:25:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:25:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b successfully announced in 71.4834 ms
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af successfully announced in 72.3917 ms
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b all the dispatchers started
2025-07-22 15:25:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af all the dispatchers started
2025-07-22 15:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b caught stopping signal...
2025-07-22 15:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af caught stopping signal...
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af caught stopped signal...
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b caught stopped signal...
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af All dispatchers stopped
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af successfully reported itself as stopped in 1.8118 ms
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:fdda97af has been stopped in total 651.9318 ms
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b All dispatchers stopped
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b successfully reported itself as stopped in 2.1603 ms
2025-07-22 15:26:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34416:3050395b has been stopped in total 703.5064 ms
2025-07-22 15:26:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:26:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:26:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:26:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:26:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:26:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:26:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:26:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 successfully announced in 62.0892 ms
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 successfully announced in 62.3578 ms
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 all the dispatchers started
2025-07-22 15:26:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 all the dispatchers started
2025-07-22 15:34:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 caught stopping signal...
2025-07-22 15:34:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 caught stopping signal...
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 caught stopped signal...
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 caught stopped signal...
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 All dispatchers stopped
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 All dispatchers stopped
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 successfully reported itself as stopped in 2.0701 ms
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:ec940651 has been stopped in total 847.9053 ms
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 successfully reported itself as stopped in 1.8001 ms
2025-07-22 15:34:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34232:571f3807 has been stopped in total 849.2353 ms
2025-07-22 15:34:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:34:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:34:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:34:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 successfully announced in 61.3462 ms
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 successfully announced in 61.148 ms
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 all the dispatchers started
2025-07-22 15:34:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 all the dispatchers started
2025-07-22 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 caught stopping signal...
2025-07-22 15:38:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 caught stopping signal...
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 All dispatchers stopped
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 successfully reported itself as stopped in 2.342 ms
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:450ddd97 has been stopped in total 481.2173 ms
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 caught stopped signal...
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 All dispatchers stopped
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 successfully reported itself as stopped in 1.8807 ms
2025-07-22 15:38:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15340:93849472 has been stopped in total 642.6512 ms
2025-07-22 15:38:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:38:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:38:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:38:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 successfully announced in 62.57 ms
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 successfully announced in 62.8312 ms
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 all the dispatchers started
2025-07-22 15:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 all the dispatchers started
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 caught stopping signal...
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 caught stopping signal...
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 All dispatchers stopped
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 successfully reported itself as stopped in 2.6736 ms
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:8ce77ca0 has been stopped in total 114.2293 ms
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 All dispatchers stopped
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 successfully reported itself as stopped in 1.1017 ms
2025-07-22 15:39:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32700:1e4ab0f5 has been stopped in total 173.3496 ms
2025-07-22 15:39:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:39:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:39:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:39:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:39:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:39:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 successfully announced in 63.8326 ms
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e successfully announced in 64.0682 ms
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e all the dispatchers started
2025-07-22 15:39:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 all the dispatchers started
2025-07-22 15:41:52 [Warning] Microsoft.AspNetCore.Watch.BrowserRefresh.BrowserRefreshMiddleware: Unable to configure browser refresh script injection on the response. Consider manually adding '<script src="/_framework/aspnetcore-browser-refresh.js"></script>' to the body of the page.
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e caught stopping signal...
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 caught stopping signal...
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e All dispatchers stopped
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e successfully reported itself as stopped in 1.8668 ms
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:70178d3e has been stopped in total 270.2001 ms
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 All dispatchers stopped
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 successfully reported itself as stopped in 1.0988 ms
2025-07-22 15:43:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22984:368924d8 has been stopped in total 408.28 ms
2025-07-22 15:43:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:43:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:43:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:43:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea successfully announced in 60.587 ms
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb successfully announced in 60.3998 ms
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb all the dispatchers started
2025-07-22 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea all the dispatchers started
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb caught stopping signal...
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea caught stopping signal...
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb All dispatchers stopped
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea All dispatchers stopped
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb successfully reported itself as stopped in 2.2991 ms
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:4c6125eb has been stopped in total 99.7879 ms
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea successfully reported itself as stopped in 64.2517 ms
2025-07-22 15:44:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:0c4fd1ea has been stopped in total 161.4961 ms
2025-07-22 15:44:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:44:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:44:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:44:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc successfully announced in 61.1938 ms
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 successfully announced in 61.2531 ms
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 all the dispatchers started
2025-07-22 15:44:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc all the dispatchers started
2025-07-22 15:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 caught stopping signal...
2025-07-22 15:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc caught stopping signal...
2025-07-22 15:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc caught stopped signal...
2025-07-22 15:47:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 caught stopped signal...
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 All dispatchers stopped
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 successfully reported itself as stopped in 1.8078 ms
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:83310621 has been stopped in total 948.252 ms
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc All dispatchers stopped
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc successfully reported itself as stopped in 0.8863 ms
2025-07-22 15:47:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18996:fd2663cc has been stopped in total 952.526 ms
2025-07-22 15:47:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:47:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:47:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:47:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 successfully announced in 63.4966 ms
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca successfully announced in 63.6959 ms
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca all the dispatchers started
2025-07-22 15:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 all the dispatchers started
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 caught stopping signal...
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca caught stopping signal...
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca All dispatchers stopped
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca successfully reported itself as stopped in 1.9785 ms
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:4dcfabca has been stopped in total 154.4121 ms
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 All dispatchers stopped
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 successfully reported itself as stopped in 0.7749 ms
2025-07-22 15:51:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:7cccd3e6 has been stopped in total 195.1802 ms
2025-07-22 15:51:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:51:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:51:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:51:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d successfully announced in 64.0304 ms
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e successfully announced in 64.0594 ms
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e all the dispatchers started
2025-07-22 15:51:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d all the dispatchers started
2025-07-22 15:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d caught stopping signal...
2025-07-22 15:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e caught stopping signal...
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e caught stopped signal...
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d caught stopped signal...
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e All dispatchers stopped
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e successfully reported itself as stopped in 1.8909 ms
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:cba16e2e has been stopped in total 526.953 ms
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d All dispatchers stopped
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d successfully reported itself as stopped in 0.9914 ms
2025-07-22 15:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23368:d013f61d has been stopped in total 601.1775 ms
2025-07-22 15:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 15:59:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 15:59:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 15:59:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 successfully announced in 64.9724 ms
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 successfully announced in 65.0512 ms
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 all the dispatchers started
2025-07-22 15:59:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 all the dispatchers started
2025-07-22 16:00:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 caught stopping signal...
2025-07-22 16:00:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 caught stopping signal...
2025-07-22 16:00:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 caught stopped signal...
2025-07-22 16:00:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 caught stopped signal...
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 All dispatchers stopped
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 successfully reported itself as stopped in 1.5407 ms
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:03557ff1 has been stopped in total 885.0518 ms
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 All dispatchers stopped
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 successfully reported itself as stopped in 0.6635 ms
2025-07-22 16:00:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23176:0bf7b828 has been stopped in total 898.8017 ms
2025-07-22 16:00:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:00:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:00:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:00:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:00:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:00:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:00:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:00:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 successfully announced in 65.008 ms
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c successfully announced in 65.6221 ms
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c all the dispatchers started
2025-07-22 16:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 all the dispatchers started
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 caught stopping signal...
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c caught stopping signal...
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 All dispatchers stopped
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 successfully reported itself as stopped in 1.8206 ms
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:fea44fc4 has been stopped in total 177.7222 ms
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c All dispatchers stopped
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c successfully reported itself as stopped in 0.9749 ms
2025-07-22 16:03:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17996:6af70a9c has been stopped in total 577.9462 ms
2025-07-22 16:03:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:03:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:03:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:03:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:03:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:03:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:03:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 successfully announced in 68.3752 ms
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 successfully announced in 68.0755 ms
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 all the dispatchers started
2025-07-22 16:03:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 all the dispatchers started
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 caught stopping signal...
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 caught stopping signal...
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 All dispatchers stopped
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 All dispatchers stopped
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 successfully reported itself as stopped in 0.7437 ms
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:81916436 has been stopped in total 305.1614 ms
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 successfully reported itself as stopped in 2.0012 ms
2025-07-22 16:10:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19640:24018774 has been stopped in total 305.7337 ms
2025-07-22 16:19:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:19:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:19:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:19:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac successfully announced in 63.9418 ms
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 successfully announced in 63.8498 ms
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac all the dispatchers started
2025-07-22 16:19:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 all the dispatchers started
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac caught stopping signal...
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 caught stopping signal...
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 All dispatchers stopped
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac All dispatchers stopped
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac successfully reported itself as stopped in 1.8122 ms
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:0699ccac has been stopped in total 444.1921 ms
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 successfully reported itself as stopped in 21.2923 ms
2025-07-22 16:21:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14308:72aeda22 has been stopped in total 463.2823 ms
2025-07-22 16:21:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:21:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:21:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:21:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 successfully announced in 64.3067 ms
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca successfully announced in 64.1744 ms
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca all the dispatchers started
2025-07-22 16:21:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 all the dispatchers started
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 caught stopping signal...
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca caught stopping signal...
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 All dispatchers stopped
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 successfully reported itself as stopped in 2.0507 ms
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:c0dc0501 has been stopped in total 458.2472 ms
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca All dispatchers stopped
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca successfully reported itself as stopped in 1.5337 ms
2025-07-22 16:27:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23260:91542cca has been stopped in total 711.2884 ms
2025-07-22 16:27:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:27:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:27:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:27:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f successfully announced in 62.4725 ms
2025-07-22 16:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 successfully announced in 62.4732 ms
2025-07-22 16:27:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:28:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:28:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f all the dispatchers started
2025-07-22 16:28:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 all the dispatchers started
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 caught stopping signal...
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f caught stopping signal...
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 All dispatchers stopped
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 successfully reported itself as stopped in 2.8861 ms
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:fbf00ee2 has been stopped in total 254.871 ms
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f All dispatchers stopped
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f successfully reported itself as stopped in 7.1067 ms
2025-07-22 16:31:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22692:558f631f has been stopped in total 307.7819 ms
2025-07-22 16:31:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:31:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:31:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:31:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:31:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e successfully announced in 68.705 ms
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 successfully announced in 69.7159 ms
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e all the dispatchers started
2025-07-22 16:31:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 all the dispatchers started
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e caught stopping signal...
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 caught stopping signal...
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 All dispatchers stopped
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e All dispatchers stopped
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e successfully reported itself as stopped in 1.548 ms
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:5836221e has been stopped in total 51.8523 ms
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 successfully reported itself as stopped in 1.5678 ms
2025-07-22 16:32:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16852:928f5b58 has been stopped in total 51.734 ms
2025-07-22 16:32:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:32:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:32:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:32:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:32:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:32:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:32:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:32:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:32:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a successfully announced in 59.0906 ms
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab successfully announced in 59.0913 ms
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a all the dispatchers started
2025-07-22 16:32:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab all the dispatchers started
2025-07-22 16:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab caught stopping signal...
2025-07-22 16:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a caught stopping signal...
2025-07-22 16:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab All dispatchers stopped
2025-07-22 16:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab successfully reported itself as stopped in 1.7492 ms
2025-07-22 16:33:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:2c3994ab has been stopped in total 52.8229 ms
2025-07-22 16:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a caught stopped signal...
2025-07-22 16:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a All dispatchers stopped
2025-07-22 16:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a successfully reported itself as stopped in 1.0922 ms
2025-07-22 16:33:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34096:8c4c965a has been stopped in total 957.5531 ms
2025-07-22 16:33:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 16:33:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 16:33:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 16:33:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 successfully announced in 70.0024 ms
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae successfully announced in 44.2503 ms
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae all the dispatchers started
2025-07-22 16:33:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 all the dispatchers started
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 caught stopping signal...
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae caught stopping signal...
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae All dispatchers stopped
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae successfully reported itself as stopped in 2.061 ms
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:3f93b6ae has been stopped in total 294.9623 ms
2025-07-22 16:59:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 caught stopped signal...
2025-07-22 16:59:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 All dispatchers stopped
2025-07-22 16:59:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 successfully reported itself as stopped in 1.1952 ms
2025-07-22 16:59:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19560:16d69f76 has been stopped in total 919.6463 ms
