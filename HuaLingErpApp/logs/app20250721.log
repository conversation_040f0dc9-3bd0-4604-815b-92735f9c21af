2025-07-21 08:07:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:07:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:07:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:07:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:07:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f successfully announced in 72.014 ms
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 successfully announced in 72.2503 ms
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 all the dispatchers started
2025-07-21 08:07:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f all the dispatchers started
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 caught stopping signal...
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f caught stopping signal...
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f All dispatchers stopped
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f successfully reported itself as stopped in 14.2779 ms
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:dec9153f has been stopped in total 330.7725 ms
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 caught stopped signal...
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 All dispatchers stopped
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 successfully reported itself as stopped in 1.2681 ms
2025-07-21 08:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19380:a0022c29 has been stopped in total 527.9207 ms
2025-07-21 08:48:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:48:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:49:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:49:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:49:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:49:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf successfully announced in 69.1674 ms
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc successfully announced in 69.0172 ms
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf all the dispatchers started
2025-07-21 08:49:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc all the dispatchers started
2025-07-21 08:49:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf caught stopping signal...
2025-07-21 08:49:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc caught stopping signal...
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf All dispatchers stopped
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf successfully reported itself as stopped in 1.5986 ms
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:d220e0cf has been stopped in total 219.7503 ms
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc All dispatchers stopped
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc successfully reported itself as stopped in 0.7562 ms
2025-07-21 08:49:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1388:98f71fbc has been stopped in total 241.7249 ms
2025-07-21 08:50:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 08:50:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 08:50:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建系统角色...
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Administrator"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Manager"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Operator"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 角色已存在: "Viewer"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建管理员用户...
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 管理员用户已存在: "<EMAIL>"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 开始创建普通用户...
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 用户已存在: "<EMAIL>"
2025-07-21 08:50:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: 数据库种子数据初始化完成
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 08:50:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 successfully announced in 65.8481 ms
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 successfully announced in 66.6726 ms
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 all the dispatchers started
2025-07-21 08:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 all the dispatchers started
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 caught stopping signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 caught stopping signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 caught stopped signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 caught stopped signal...
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 All dispatchers stopped
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 successfully reported itself as stopped in 1.7982 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:61ee66c7 has been stopped in total 698.3222 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 All dispatchers stopped
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 successfully reported itself as stopped in 0.9006 ms
2025-07-21 08:51:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26692:dfe3fd83 has been stopped in total 742.8151 ms
