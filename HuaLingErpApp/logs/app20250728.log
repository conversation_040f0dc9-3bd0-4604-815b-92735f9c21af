2025-07-28 09:26:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:26:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:26:08 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:26:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:26:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:26:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:26:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 successfully announced in 137.8127 ms
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 successfully announced in 147.5399 ms
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 all the dispatchers started
2025-07-28 09:26:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 all the dispatchers started
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 caught stopping signal...
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 caught stopping signal...
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 All dispatchers stopped
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 All dispatchers stopped
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 successfully reported itself as stopped in 2.8942 ms
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:dc8d0cf5 has been stopped in total 132.4434 ms
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 successfully reported itself as stopped in 1.36 ms
2025-07-28 09:26:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16500:190b5260 has been stopped in total 132.948 ms
2025-07-28 09:26:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:26:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:26:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:26:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:26:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 successfully announced in 101.2997 ms
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 successfully announced in 101.5008 ms
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 all the dispatchers started
2025-07-28 09:26:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 all the dispatchers started
2025-07-28 09:27:07 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:27:07 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:27:07 [Information] HuaLingErpApp.Controller.ItemController: Successfully imported 16 items
2025-07-28 09:27:07 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 09:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 caught stopping signal...
2025-07-28 09:34:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 caught stopping signal...
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 caught stopped signal...
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 caught stopped signal...
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 All dispatchers stopped
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 successfully reported itself as stopped in 2.5541 ms
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:73de3da3 has been stopped in total 838.1689 ms
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 All dispatchers stopped
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 successfully reported itself as stopped in 2.1496 ms
2025-07-28 09:34:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17388:27884322 has been stopped in total 1077.0179 ms
2025-07-28 09:34:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:34:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:34:56 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:34:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:34:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f successfully announced in 101.1396 ms
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c successfully announced in 101.1363 ms
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c all the dispatchers started
2025-07-28 09:34:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f all the dispatchers started
2025-07-28 09:35:09 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:35:09 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:35:09 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 0 processed, 16 skipped, 0 errors
2025-07-28 09:35:19 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:35:19 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:35:19 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 16 items
2025-07-28 09:35:19 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 09:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f caught stopping signal...
2025-07-28 09:36:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c caught stopping signal...
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c All dispatchers stopped
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f All dispatchers stopped
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c successfully reported itself as stopped in 1.8539 ms
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:5d0df39c has been stopped in total 340.7562 ms
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f successfully reported itself as stopped in 0.9526 ms
2025-07-28 09:36:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14808:e4aaa21f has been stopped in total 342.2669 ms
2025-07-28 09:42:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:42:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:42:29 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:42:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:42:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:42:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:42:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b successfully announced in 59.549 ms
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 successfully announced in 59.8164 ms
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b all the dispatchers started
2025-07-28 09:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 all the dispatchers started
2025-07-28 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 caught stopping signal...
2025-07-28 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b caught stopping signal...
2025-07-28 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b caught stopped signal...
2025-07-28 09:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 caught stopped signal...
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 All dispatchers stopped
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b All dispatchers stopped
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b successfully reported itself as stopped in 2.0371 ms
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:bb75f11b has been stopped in total 871.6975 ms
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 successfully reported itself as stopped in 0.6859 ms
2025-07-28 09:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9356:54e2ae65 has been stopped in total 872.5274 ms
2025-07-28 09:52:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:52:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:52:02 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:52:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:52:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:52:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:52:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb successfully announced in 104.8003 ms
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a successfully announced in 104.8377 ms
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb all the dispatchers started
2025-07-28 09:52:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a all the dispatchers started
2025-07-28 09:53:47 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:53:47 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:53:48 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 16 items
2025-07-28 09:53:48 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 09:53:54 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:53:54 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:53:54 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 0 processed, 16 skipped, 0 errors
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb caught stopping signal...
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a caught stopping signal...
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb All dispatchers stopped
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb successfully reported itself as stopped in 1.515 ms
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:daed65fb has been stopped in total 232.0552 ms
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a All dispatchers stopped
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a successfully reported itself as stopped in 0.7947 ms
2025-07-28 09:56:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16288:3e81945a has been stopped in total 246.447 ms
2025-07-28 09:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-28 09:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-28 09:56:41 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-28 09:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-28 09:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-28 09:56:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-28 09:56:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 successfully announced in 103.5285 ms
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d successfully announced in 103.6102 ms
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d all the dispatchers started
2025-07-28 09:56:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 all the dispatchers started
2025-07-28 09:57:08 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:57:08 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:57:08 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 0 processed, 16 skipped, 0 errors
2025-07-28 09:57:17 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-28 09:57:17 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-28 09:57:17 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 16 items
2025-07-28 09:57:17 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 16 processed, 0 skipped, 0 errors
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d caught stopping signal...
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 caught stopping signal...
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 caught stopped signal...
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d caught stopped signal...
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 All dispatchers stopped
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 successfully reported itself as stopped in 1.6623 ms
2025-07-28 10:00:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:8396eeb8 has been stopped in total 528.216 ms
2025-07-28 10:00:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d All dispatchers stopped
2025-07-28 10:00:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d successfully reported itself as stopped in 1.0072 ms
2025-07-28 10:00:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14244:3b538d1d has been stopped in total 991.1045 ms
