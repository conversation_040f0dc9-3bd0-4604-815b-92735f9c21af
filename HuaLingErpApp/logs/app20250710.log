2025-07-10 10:26:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:26:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:26:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 successfully announced in 110.7283 ms
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 successfully announced in 110.729 ms
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 all the dispatchers started
2025-07-10 10:26:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 all the dispatchers started
2025-07-10 10:27:10 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: MessageService not registered. refer doc https://www.blazor.zone/install-webapp step 7 for BootstrapBlazorRoot
   at BootstrapBlazor.Components.BootstrapServiceBase`1.Invoke(TOption option, ComponentBase component)
   at BootstrapBlazor.Components.MessageService.Show(MessageOption option, Message message)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.LoadData() in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 51
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnInitializedAsync() in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 29
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 caught stopping signal...
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 caught stopping signal...
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 caught stopped signal...
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 caught stopped signal...
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 All dispatchers stopped
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 successfully reported itself as stopped in 3.5233 ms
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:d8ede8a9 has been stopped in total 652.791 ms
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 All dispatchers stopped
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 successfully reported itself as stopped in 1.0076 ms
2025-07-10 10:29:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23912:8f0a8287 has been stopped in total 685.8562 ms
2025-07-10 10:30:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:30:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:30:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b successfully announced in 107.9949 ms
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 successfully announced in 108.9454 ms
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 all the dispatchers started
2025-07-10 10:30:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b all the dispatchers started
2025-07-10 10:30:12 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-10 10:30:13 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-10 10:32:09 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-10 10:32:09 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-10 10:32:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 caught stopping signal...
2025-07-10 10:32:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b caught stopping signal...
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b caught stopped signal...
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 caught stopped signal...
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b All dispatchers stopped
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b successfully reported itself as stopped in 3.2748 ms
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:d4fc675b has been stopped in total 531.4901 ms
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 All dispatchers stopped
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 successfully reported itself as stopped in 1.5191 ms
2025-07-10 10:32:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31316:f12816f0 has been stopped in total 544.7301 ms
2025-07-10 10:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:32:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:32:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:6dd6df2e successfully announced in 452.923 ms
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:88f94a2b successfully announced in 452.9465 ms
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:6dd6df2e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:88f94a2b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:88f94a2b all the dispatchers started
2025-07-10 10:32:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36764:6dd6df2e all the dispatchers started
2025-07-10 10:33:25 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-10 10:33:44 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.<>c__DisplayClass0_0.<BuildRenderTree>b__2(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-10 10:34:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:34:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:34:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 successfully announced in 110.5902 ms
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 successfully announced in 111.471 ms
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 all the dispatchers started
2025-07-10 10:34:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 all the dispatchers started
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 caught stopping signal...
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 caught stopping signal...
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 All dispatchers stopped
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 successfully reported itself as stopped in 3.2507 ms
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:77921522 has been stopped in total 475.0486 ms
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 caught stopped signal...
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 All dispatchers stopped
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 successfully reported itself as stopped in 1.312 ms
2025-07-10 10:36:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11728:5967b668 has been stopped in total 663.8332 ms
2025-07-10 10:36:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:36:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:36:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 successfully announced in 77.4334 ms
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd successfully announced in 95.4094 ms
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 all the dispatchers started
2025-07-10 10:36:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd all the dispatchers started
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 caught stopping signal...
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd caught stopping signal...
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd caught stopped signal...
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 caught stopped signal...
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 All dispatchers stopped
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 successfully reported itself as stopped in 3.5442 ms
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:63b8b765 has been stopped in total 658.7447 ms
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd All dispatchers stopped
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd successfully reported itself as stopped in 1.4099 ms
2025-07-10 10:37:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23676:75cc33dd has been stopped in total 686.53 ms
2025-07-10 10:39:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:39:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:39:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5f83a464 successfully announced in 161.3221 ms
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 successfully announced in 161.3228 ms
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5f83a464 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:39:44 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5f83a464 all the dispatchers started
2025-07-10 10:39:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 all the dispatchers started
2025-07-10 10:47:29 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 10:47:29 [Warning] Hangfire.Server.ServerHeartbeatProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 was considered dead by other servers, restarting...
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 caught restart signal...
2025-07-10 10:47:29 [Warning] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 stopped non-gracefully due to ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler. Outstanding work on those dispatchers could be aborted, and there can be delays in background processing. This server instance will be incorrectly shown as active for a while. To avoid non-graceful shutdowns, investigate what prevents from stopping gracefully and add CancellationToken support for those methods.
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 successfully reported itself as stopped in 19.6259 ms
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:5c37cc68 has been stopped in total 105.17 ms
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:7bb12b1a successfully announced in 7.107 ms
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:7bb12b1a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:47:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22476:7bb12b1a all the dispatchers started
2025-07-10 10:50:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:50:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:50:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:a130080d successfully announced in 133.0311 ms
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:9df2bbfc successfully announced in 133.0385 ms
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:a130080d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:9df2bbfc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:a130080d all the dispatchers started
2025-07-10 10:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11328:9df2bbfc all the dispatchers started
2025-07-10 10:51:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:51:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:51:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:3909647b successfully announced in 172.2332 ms
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:33217f0a successfully announced in 174.148 ms
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:3909647b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:33217f0a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:33217f0a all the dispatchers started
2025-07-10 10:51:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34876:3909647b all the dispatchers started
2025-07-10 10:54:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:54:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:54:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:bdf061d1 successfully announced in 154.4491 ms
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:86fca1d7 successfully announced in 159.3465 ms
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:86fca1d7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:bdf061d1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:54:20 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:86fca1d7 all the dispatchers started
2025-07-10 10:54:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24692:bdf061d1 all the dispatchers started
2025-07-10 10:57:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 10:57:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 10:57:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 successfully announced in 84.9908 ms
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 successfully announced in 83.7102 ms
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 10:57:17 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 all the dispatchers started
2025-07-10 10:57:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 all the dispatchers started
2025-07-10 11:00:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 caught stopping signal...
2025-07-10 11:00:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 caught stopping signal...
2025-07-10 11:00:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 caught stopped signal...
2025-07-10 11:00:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 caught stopped signal...
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 All dispatchers stopped
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 All dispatchers stopped
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 successfully reported itself as stopped in 3.1561 ms
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:83b07149 has been stopped in total 984.6243 ms
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 successfully reported itself as stopped in 6.8319 ms
2025-07-10 11:00:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13832:e99d33f0 has been stopped in total 989.3336 ms
2025-07-10 11:00:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:00:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:00:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 successfully announced in 72.7717 ms
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca successfully announced in 73.2795 ms
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:00:13 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 all the dispatchers started
2025-07-10 11:00:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca all the dispatchers started
2025-07-10 11:00:13 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 11:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 caught stopping signal...
2025-07-10 11:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca caught stopping signal...
2025-07-10 11:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca All dispatchers stopped
2025-07-10 11:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca successfully reported itself as stopped in 2.9363 ms
2025-07-10 11:01:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:e01fd6ca has been stopped in total 143.6725 ms
2025-07-10 11:01:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 All dispatchers stopped
2025-07-10 11:01:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 successfully reported itself as stopped in 1.0948 ms
2025-07-10 11:01:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32000:a0269bd1 has been stopped in total 350.5256 ms
2025-07-10 11:01:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:01:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:01:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:10c6095b successfully announced in 194.7046 ms
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:6b3e6fac successfully announced in 222.4723 ms
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:6b3e6fac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:10c6095b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:01:51 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:6b3e6fac all the dispatchers started
2025-07-10 11:01:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25572:10c6095b all the dispatchers started
2025-07-10 11:18:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:18:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 successfully announced in 78.5711 ms
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a successfully announced in 79.4061 ms
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:18:41 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a all the dispatchers started
2025-07-10 11:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 all the dispatchers started
2025-07-10 11:34:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 caught stopping signal...
2025-07-10 11:34:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a caught stopping signal...
2025-07-10 11:34:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 All dispatchers stopped
2025-07-10 11:34:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 successfully reported itself as stopped in 4.4983 ms
2025-07-10 11:34:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:f3c1b350 has been stopped in total 355.1547 ms
2025-07-10 11:34:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a caught stopped signal...
2025-07-10 11:34:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a All dispatchers stopped
2025-07-10 11:34:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a successfully reported itself as stopped in 0.7848 ms
2025-07-10 11:34:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24852:7aa5521a has been stopped in total 955.3873 ms
2025-07-10 11:34:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:34:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:34:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:34:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b successfully announced in 77.7947 ms
2025-07-10 11:34:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 successfully announced in 217.0202 ms
2025-07-10 11:34:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:34:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b all the dispatchers started
2025-07-10 11:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 all the dispatchers started
2025-07-10 11:35:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b caught stopping signal...
2025-07-10 11:35:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 caught stopping signal...
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b All dispatchers stopped
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b successfully reported itself as stopped in 2.4717 ms
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:7bfc8d6b has been stopped in total 470.0109 ms
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 All dispatchers stopped
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 successfully reported itself as stopped in 0.7972 ms
2025-07-10 11:35:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2068:304b7001 has been stopped in total 513.7842 ms
2025-07-10 11:36:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:36:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:36:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4803b69b successfully announced in 137.0978 ms
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4ba84224 successfully announced in 137.2096 ms
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4803b69b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4ba84224 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4803b69b all the dispatchers started
2025-07-10 11:36:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33440:4ba84224 all the dispatchers started
2025-07-10 11:37:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:37:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:37:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:93e08d0d successfully announced in 138.939 ms
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:23a7089e successfully announced in 139.681 ms
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:93e08d0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:23a7089e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:93e08d0d all the dispatchers started
2025-07-10 11:37:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26176:23a7089e all the dispatchers started
2025-07-10 11:39:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:39:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:39:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 successfully announced in 81.8265 ms
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d successfully announced in 90.1472 ms
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 all the dispatchers started
2025-07-10 11:39:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d all the dispatchers started
2025-07-10 11:42:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 caught stopping signal...
2025-07-10 11:42:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d caught stopping signal...
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 All dispatchers stopped
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d All dispatchers stopped
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 successfully reported itself as stopped in 2.5508 ms
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:946c6c03 has been stopped in total 361.5121 ms
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d successfully reported itself as stopped in 1.4436 ms
2025-07-10 11:42:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17660:b45a155d has been stopped in total 362.408 ms
2025-07-10 11:42:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:42:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:42:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 successfully announced in 74.5201 ms
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 successfully announced in 76.6735 ms
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:42:32 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 all the dispatchers started
2025-07-10 11:42:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 all the dispatchers started
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 caught stopping signal...
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 caught stopping signal...
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 caught stopped signal...
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 caught stopped signal...
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 All dispatchers stopped
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 successfully reported itself as stopped in 2.5442 ms
2025-07-10 11:44:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:49617399 has been stopped in total 647.0334 ms
2025-07-10 11:44:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 All dispatchers stopped
2025-07-10 11:44:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 successfully reported itself as stopped in 1.1402 ms
2025-07-10 11:44:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18784:7be807b3 has been stopped in total 730.8426 ms
2025-07-10 11:44:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:44:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:44:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 successfully announced in 74.888 ms
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 successfully announced in 74.1039 ms
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:44:17 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 11:44:17 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 all the dispatchers started
2025-07-10 11:44:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 all the dispatchers started
2025-07-10 11:47:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 caught stopping signal...
2025-07-10 11:47:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 caught stopping signal...
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 caught stopped signal...
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 caught stopped signal...
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 All dispatchers stopped
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 All dispatchers stopped
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 successfully reported itself as stopped in 1.6249 ms
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:9f799ca0 has been stopped in total 863.8774 ms
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 successfully reported itself as stopped in 2.1324 ms
2025-07-10 11:47:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:35028:4ef1f653 has been stopped in total 864.1643 ms
2025-07-10 11:54:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 11:54:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 11:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc successfully announced in 74.227 ms
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 successfully announced in 74.3615 ms
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 all the dispatchers started
2025-07-10 11:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc all the dispatchers started
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 caught stopping signal...
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc caught stopping signal...
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc All dispatchers stopped
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 All dispatchers stopped
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc successfully reported itself as stopped in 2.8029 ms
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:83065bfc has been stopped in total 27.5405 ms
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 successfully reported itself as stopped in 7.949 ms
2025-07-10 12:19:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22580:041b2257 has been stopped in total 33.666 ms
2025-07-10 12:19:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 12:19:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 12:19:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 successfully announced in 74.0248 ms
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 successfully announced in 75.7293 ms
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 all the dispatchers started
2025-07-10 12:19:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 all the dispatchers started
2025-07-10 12:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 caught stopping signal...
2025-07-10 12:21:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 caught stopping signal...
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 caught stopped signal...
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 caught stopped signal...
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 All dispatchers stopped
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 successfully reported itself as stopped in 1.8155 ms
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:35ad8bf5 has been stopped in total 809.6181 ms
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 All dispatchers stopped
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 successfully reported itself as stopped in 0.846 ms
2025-07-10 12:21:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25020:e311f453 has been stopped in total 822.7866 ms
2025-07-10 13:03:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 13:03:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:03:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:cea7a031 successfully announced in 214.3386 ms
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:78c472b5 successfully announced in 218.776 ms
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:78c472b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:cea7a031 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:cea7a031 all the dispatchers started
2025-07-10 13:03:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18756:78c472b5 all the dispatchers started
2025-07-10 13:05:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 13:05:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:05:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 successfully announced in 74.0413 ms
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 successfully announced in 75.0192 ms
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 all the dispatchers started
2025-07-10 13:05:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 all the dispatchers started
2025-07-10 13:06:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 caught stopping signal...
2025-07-10 13:06:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 caught stopping signal...
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 All dispatchers stopped
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 All dispatchers stopped
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 successfully reported itself as stopped in 2.1489 ms
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:b00f6b89 has been stopped in total 86.8041 ms
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 successfully reported itself as stopped in 0.7797 ms
2025-07-10 13:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19744:7891fa60 has been stopped in total 87.6582 ms
2025-07-10 13:06:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 13:06:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:06:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:06:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:929d3e03 successfully announced in 132.1093 ms
2025-07-10 13:06:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:9c6fd184 successfully announced in 132.0807 ms
2025-07-10 13:06:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:929d3e03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:06:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:9c6fd184 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:06:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:9c6fd184 all the dispatchers started
2025-07-10 13:06:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24656:929d3e03 all the dispatchers started
2025-07-10 13:29:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 13:29:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 13:29:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff successfully announced in 77.1278 ms
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa successfully announced in 79.7681 ms
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 13:29:42 [Information] Hangfire.Server.ServerWatchdog: 4 servers were removed due to timeout
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa all the dispatchers started
2025-07-10 13:29:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff all the dispatchers started
2025-07-10 13:34:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff caught stopping signal...
2025-07-10 13:34:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa caught stopping signal...
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa caught stopped signal...
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff caught stopped signal...
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff All dispatchers stopped
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa All dispatchers stopped
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa successfully reported itself as stopped in 3.3152 ms
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:103bd7fa has been stopped in total 723.1313 ms
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff successfully reported itself as stopped in 6.6054 ms
2025-07-10 13:34:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13136:f8b9bdff has been stopped in total 728.0381 ms
2025-07-10 14:16:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:16:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:16:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce successfully announced in 140.2053 ms
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e successfully announced in 148.2522 ms
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce all the dispatchers started
2025-07-10 14:16:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e all the dispatchers started
2025-07-10 14:18:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e caught stopping signal...
2025-07-10 14:18:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce caught stopping signal...
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce caught stopped signal...
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e caught stopped signal...
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce All dispatchers stopped
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce successfully reported itself as stopped in 2.283 ms
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:e568f8ce has been stopped in total 752.8556 ms
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e All dispatchers stopped
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e successfully reported itself as stopped in 1.6194 ms
2025-07-10 14:18:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29092:a296351e has been stopped in total 826.2355 ms
2025-07-10 14:18:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:18:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:18:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda successfully announced in 75.5149 ms
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 successfully announced in 76.3294 ms
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda all the dispatchers started
2025-07-10 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 all the dispatchers started
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda caught stopping signal...
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 caught stopping signal...
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 caught stopped signal...
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda caught stopped signal...
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda All dispatchers stopped
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda successfully reported itself as stopped in 2.3614 ms
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:23498eda has been stopped in total 508.5527 ms
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 All dispatchers stopped
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 successfully reported itself as stopped in 0.8405 ms
2025-07-10 14:20:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33544:7f726e75 has been stopped in total 625.5556 ms
2025-07-10 14:20:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:20:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:20:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 successfully announced in 75.8985 ms
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 successfully announced in 76.753 ms
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 all the dispatchers started
2025-07-10 14:20:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 all the dispatchers started
2025-07-10 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 caught stopping signal...
2025-07-10 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 caught stopping signal...
2025-07-10 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 caught stopped signal...
2025-07-10 14:21:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 caught stopped signal...
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 All dispatchers stopped
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 successfully reported itself as stopped in 2.6512 ms
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:5dfed492 has been stopped in total 858.048 ms
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 All dispatchers stopped
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 successfully reported itself as stopped in 1.0548 ms
2025-07-10 14:21:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24452:d580b5b6 has been stopped in total 979.2737 ms
2025-07-10 14:21:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:21:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:21:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 successfully announced in 71.1661 ms
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 successfully announced in 1.5857 ms
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 all the dispatchers started
2025-07-10 14:21:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 all the dispatchers started
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 caught stopping signal...
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 caught stopping signal...
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 All dispatchers stopped
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 successfully reported itself as stopped in 2.6736 ms
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:89f30f08 has been stopped in total 163.7491 ms
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 All dispatchers stopped
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 successfully reported itself as stopped in 1.6843 ms
2025-07-10 14:22:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27368:901378d4 has been stopped in total 170.9746 ms
2025-07-10 14:23:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:23:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:23:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 successfully announced in 85.6829 ms
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 successfully announced in 85.0868 ms
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 all the dispatchers started
2025-07-10 14:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 all the dispatchers started
2025-07-10 14:37:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 caught stopping signal...
2025-07-10 14:37:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 caught stopping signal...
2025-07-10 14:37:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 All dispatchers stopped
2025-07-10 14:37:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 successfully reported itself as stopped in 14.4054 ms
2025-07-10 14:37:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:8adeab76 has been stopped in total 223.3986 ms
2025-07-10 14:37:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 All dispatchers stopped
2025-07-10 14:37:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 successfully reported itself as stopped in 1.3509 ms
2025-07-10 14:37:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25672:fdf34778 has been stopped in total 587.2421 ms
2025-07-10 14:38:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:38:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:38:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f successfully announced in 75.2178 ms
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 successfully announced in 75.9817 ms
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f all the dispatchers started
2025-07-10 14:38:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 all the dispatchers started
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 caught stopping signal...
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f caught stopping signal...
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f All dispatchers stopped
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f successfully reported itself as stopped in 2.8582 ms
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:d530474f has been stopped in total 471.4285 ms
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 All dispatchers stopped
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 successfully reported itself as stopped in 1.5425 ms
2025-07-10 14:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28400:82925682 has been stopped in total 492.213 ms
2025-07-10 14:41:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:41:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:41:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 successfully announced in 53.5748 ms
2025-07-10 14:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e successfully announced in 73.7181 ms
2025-07-10 14:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:41:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:41:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 all the dispatchers started
2025-07-10 14:41:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e all the dispatchers started
2025-07-10 14:44:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e caught stopping signal...
2025-07-10 14:44:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 caught stopping signal...
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 caught stopped signal...
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e caught stopped signal...
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 All dispatchers stopped
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 successfully reported itself as stopped in 2.4962 ms
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:b3b6f6e7 has been stopped in total 967.5148 ms
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e All dispatchers stopped
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e successfully reported itself as stopped in 0.7236 ms
2025-07-10 14:44:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30836:24e5d92e has been stopped in total 984.2593 ms
2025-07-10 14:45:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:45:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:45:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 successfully announced in 75.0379 ms
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c successfully announced in 75.0386 ms
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 all the dispatchers started
2025-07-10 14:45:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c all the dispatchers started
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 caught stopping signal...
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c caught stopping signal...
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 All dispatchers stopped
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 successfully reported itself as stopped in 2.5563 ms
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:76e54e58 has been stopped in total 359.4731 ms
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c All dispatchers stopped
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c successfully reported itself as stopped in 0.9412 ms
2025-07-10 14:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15228:16ea3c6c has been stopped in total 471.2981 ms
2025-07-10 14:46:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:46:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:46:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e successfully announced in 77.9548 ms
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae successfully announced in 89.9753 ms
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e all the dispatchers started
2025-07-10 14:46:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae all the dispatchers started
2025-07-10 14:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae caught stopping signal...
2025-07-10 14:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e caught stopping signal...
2025-07-10 14:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e caught stopped signal...
2025-07-10 14:47:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae caught stopped signal...
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e All dispatchers stopped
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae All dispatchers stopped
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae successfully reported itself as stopped in 1.6843 ms
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:a767c6ae has been stopped in total 952.4348 ms
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e successfully reported itself as stopped in 2.7681 ms
2025-07-10 14:47:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31388:e4c54f2e has been stopped in total 953.2325 ms
2025-07-10 14:47:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:47:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:47:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b successfully announced in 79.9722 ms
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 successfully announced in 80.1363 ms
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b all the dispatchers started
2025-07-10 14:47:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 all the dispatchers started
2025-07-10 14:58:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b caught stopping signal...
2025-07-10 14:58:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 caught stopping signal...
2025-07-10 14:58:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 caught stopped signal...
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b caught stopped signal...
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 All dispatchers stopped
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b All dispatchers stopped
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b successfully reported itself as stopped in 3.4635 ms
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:1735a99b has been stopped in total 883.7946 ms
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 successfully reported itself as stopped in 9.8378 ms
2025-07-10 14:58:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3844:2c67ce20 has been stopped in total 889.5347 ms
2025-07-10 14:58:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 14:58:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 14:58:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf successfully announced in 78.0468 ms
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 successfully announced in 78.0457 ms
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf all the dispatchers started
2025-07-10 14:58:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 all the dispatchers started
2025-07-10 15:02:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf caught stopping signal...
2025-07-10 15:02:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 caught stopping signal...
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 caught stopped signal...
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf caught stopped signal...
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf All dispatchers stopped
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 All dispatchers stopped
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf successfully reported itself as stopped in 3.4778 ms
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:1885b6cf has been stopped in total 753.7602 ms
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 successfully reported itself as stopped in 1.6535 ms
2025-07-10 15:02:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:36468:39c29946 has been stopped in total 756.5398 ms
2025-07-10 15:03:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:03:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:03:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c successfully announced in 74.338 ms
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 successfully announced in 75.6336 ms
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c all the dispatchers started
2025-07-10 15:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 all the dispatchers started
2025-07-10 15:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c caught stopping signal...
2025-07-10 15:09:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 caught stopping signal...
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c All dispatchers stopped
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c successfully reported itself as stopped in 4.292 ms
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:1dc4829c has been stopped in total 147.5868 ms
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 All dispatchers stopped
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 successfully reported itself as stopped in 1.0643 ms
2025-07-10 15:09:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13532:e94007a3 has been stopped in total 217.6706 ms
2025-07-10 15:10:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:10:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:10:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e successfully announced in 78.2197 ms
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 successfully announced in 78.2193 ms
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 all the dispatchers started
2025-07-10 15:10:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e all the dispatchers started
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e caught stopping signal...
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 caught stopping signal...
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 All dispatchers stopped
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 successfully reported itself as stopped in 1.5033 ms
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:3f25a544 has been stopped in total 137.0436 ms
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e All dispatchers stopped
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e successfully reported itself as stopped in 2.135 ms
2025-07-10 15:10:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22880:cf0ccc3e has been stopped in total 171.1296 ms
2025-07-10 15:11:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:11:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:11:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:11:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 successfully announced in 76.3562 ms
2025-07-10 15:11:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 successfully announced in 75.8227 ms
2025-07-10 15:11:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:11:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:11:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 all the dispatchers started
2025-07-10 15:11:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 all the dispatchers started
2025-07-10 15:11:04 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering."
System.NotSupportedException: Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering.
   at Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer.ResolveComponentForRenderMode(Type componentType, Nullable`1 parentComponentId, IComponentActivator componentActivator, IComponentRenderMode renderMode)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-10 15:11:04 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"kwC6KkqAHL5EBGL7_-87vAO4nGPHwEcSEeMoIo9acdE"'.
System.NotSupportedException: Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering.
   at Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer.ResolveComponentForRenderMode(Type componentType, Nullable`1 parentComponentId, IComponentActivator componentActivator, IComponentRenderMode renderMode)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-10 15:11:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 caught stopping signal...
2025-07-10 15:11:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 caught stopping signal...
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 All dispatchers stopped
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 successfully reported itself as stopped in 2.4878 ms
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:f07f2b16 has been stopped in total 454.5825 ms
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 All dispatchers stopped
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 successfully reported itself as stopped in 1.6836 ms
2025-07-10 15:11:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:3d20c363 has been stopped in total 486.4063 ms
2025-07-10 15:13:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:13:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:13:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c successfully announced in 84.6204 ms
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba successfully announced in 76.8765 ms
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c all the dispatchers started
2025-07-10 15:13:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba all the dispatchers started
2025-07-10 15:13:07 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering."
System.NotSupportedException: Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering.
   at Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer.ResolveComponentForRenderMode(Type componentType, Nullable`1 parentComponentId, IComponentActivator componentActivator, IComponentRenderMode renderMode)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-10 15:13:07 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"b9nHXvPne8iSPjDG1SlDdUUjK6FwMFOAx8k15vRfaq4"'.
System.NotSupportedException: Cannot create a component of type 'Microsoft.AspNetCore.Components.Routing.Router' because its render mode 'Microsoft.AspNetCore.Components.Web.InteractiveWebAssemblyRenderMode' is not supported by interactive server-side rendering.
   at Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer.ResolveComponentForRenderMode(Type componentType, Nullable`1 parentComponentId, IComponentActivator componentActivator, IComponentRenderMode renderMode)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c caught stopping signal...
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba caught stopping signal...
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba All dispatchers stopped
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba successfully reported itself as stopped in 2.272 ms
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:be89baba has been stopped in total 342.1163 ms
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c All dispatchers stopped
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c successfully reported itself as stopped in 0.8002 ms
2025-07-10 15:13:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34896:33868f5c has been stopped in total 373.0003 ms
2025-07-10 15:17:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:17:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:17:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 successfully announced in 73.9405 ms
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec successfully announced in 74.578 ms
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec all the dispatchers started
2025-07-10 15:17:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 all the dispatchers started
2025-07-10 15:18:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 caught stopping signal...
2025-07-10 15:18:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec caught stopping signal...
2025-07-10 15:18:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec caught stopped signal...
2025-07-10 15:18:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 caught stopped signal...
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec All dispatchers stopped
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 All dispatchers stopped
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec successfully reported itself as stopped in 2.7289 ms
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:cf70b0ec has been stopped in total 825.9421 ms
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 successfully reported itself as stopped in 1.6532 ms
2025-07-10 15:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17404:067393b8 has been stopped in total 826.6591 ms
2025-07-10 15:18:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:18:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:18:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 successfully announced in 75.3354 ms
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e successfully announced in 75.7271 ms
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 all the dispatchers started
2025-07-10 15:18:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e all the dispatchers started
2025-07-10 15:22:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 caught stopping signal...
2025-07-10 15:22:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e caught stopping signal...
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e caught stopped signal...
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 caught stopped signal...
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e All dispatchers stopped
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 All dispatchers stopped
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 successfully reported itself as stopped in 2.1819 ms
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:1a657c16 has been stopped in total 674.0635 ms
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e successfully reported itself as stopped in 37.7118 ms
2025-07-10 15:22:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25480:d988490e has been stopped in total 677.4256 ms
2025-07-10 15:23:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:23:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:23:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 successfully announced in 78.6956 ms
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 successfully announced in 79.6948 ms
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 all the dispatchers started
2025-07-10 15:23:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 all the dispatchers started
2025-07-10 15:23:12 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnSearchModelQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-10 15:23:12 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnSearchModelQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:23:12 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"uO4HLa2niUj4xSXbTfxDajeds5yAYfHu8ufTRcQ8zEM"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnSearchModelQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 caught stopping signal...
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 caught stopping signal...
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 All dispatchers stopped
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 successfully reported itself as stopped in 1.5594 ms
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:07534d38 has been stopped in total 189.2496 ms
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 All dispatchers stopped
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 successfully reported itself as stopped in 1.245 ms
2025-07-10 15:23:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10048:9f8eba23 has been stopped in total 233.8776 ms
2025-07-10 15:24:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:24:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:24:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 successfully announced in 65.7987 ms
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe successfully announced in 79.6461 ms
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe all the dispatchers started
2025-07-10 15:24:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 all the dispatchers started
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe caught stopping signal...
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 caught stopping signal...
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe All dispatchers stopped
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 All dispatchers stopped
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe successfully reported itself as stopped in 2.3955 ms
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:49b356fe has been stopped in total 14.5648 ms
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 successfully reported itself as stopped in 1.1461 ms
2025-07-10 15:26:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2796:935494e6 has been stopped in total 15.3042 ms
2025-07-10 15:26:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:26:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:26:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a successfully announced in 87.3632 ms
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 successfully announced in 88.1942 ms
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a all the dispatchers started
2025-07-10 15:26:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 all the dispatchers started
2025-07-10 15:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 caught stopping signal...
2025-07-10 15:26:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a caught stopping signal...
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a caught stopped signal...
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 caught stopped signal...
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a All dispatchers stopped
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 All dispatchers stopped
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 successfully reported itself as stopped in 2.4369 ms
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a successfully reported itself as stopped in 2.4662 ms
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:146129c8 has been stopped in total 702.0438 ms
2025-07-10 15:26:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21100:2237b99a has been stopped in total 701.9166 ms
2025-07-10 15:31:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:31:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:31:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e successfully announced in 75.7025 ms
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 successfully announced in 77.1681 ms
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e all the dispatchers started
2025-07-10 15:31:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 all the dispatchers started
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 caught stopping signal...
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e caught stopping signal...
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 All dispatchers stopped
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 successfully reported itself as stopped in 3.6768 ms
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:a0024f46 has been stopped in total 9.1347 ms
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e All dispatchers stopped
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e successfully reported itself as stopped in 1.3293 ms
2025-07-10 15:31:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28476:d621243e has been stopped in total 25.4231 ms
2025-07-10 15:40:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:40:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:40:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e successfully announced in 73.8353 ms
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 successfully announced in 74.3259 ms
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e all the dispatchers started
2025-07-10 15:40:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 all the dispatchers started
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 caught stopping signal...
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e caught stopping signal...
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e caught stopped signal...
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 caught stopped signal...
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e All dispatchers stopped
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e successfully reported itself as stopped in 2.2588 ms
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:553d744e has been stopped in total 670.1632 ms
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 All dispatchers stopped
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 successfully reported itself as stopped in 1.334 ms
2025-07-10 15:42:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30984:c53490f0 has been stopped in total 712.8097 ms
2025-07-10 15:42:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:42:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c successfully announced in 71.8762 ms
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 successfully announced in 72.7218 ms
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 all the dispatchers started
2025-07-10 15:42:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c all the dispatchers started
2025-07-10 15:42:46 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 108
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-10 15:42:47 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 108
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:42:47 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"4XQLohCf3Fv3rhuOCX7JuESdnSA7UAISCqYITtc_NSs"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 108
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 caught stopping signal...
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c caught stopping signal...
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 All dispatchers stopped
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 successfully reported itself as stopped in 1.9558 ms
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:3fe8eaf8 has been stopped in total 227.3594 ms
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c All dispatchers stopped
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c successfully reported itself as stopped in 0.9856 ms
2025-07-10 15:48:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23892:5a7d683c has been stopped in total 288.1179 ms
2025-07-10 15:48:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:48:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:48:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 successfully announced in 74.9928 ms
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 successfully announced in 74.9932 ms
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 all the dispatchers started
2025-07-10 15:48:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 all the dispatchers started
2025-07-10 15:48:33 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 113
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-10 15:48:33 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 113
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:48:33 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"0G8hGruCQkT-MPeLh4nvQ_CIvWarsGJvWDR9SYJNJV8"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 113
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 caught stopping signal...
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 caught stopping signal...
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 All dispatchers stopped
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 All dispatchers stopped
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 successfully reported itself as stopped in 2.7234 ms
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 successfully reported itself as stopped in 1.2314 ms
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:313a1df5 has been stopped in total 388.7856 ms
2025-07-10 15:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3940:691e1ec3 has been stopped in total 389.0036 ms
2025-07-10 15:50:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:50:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea successfully announced in 73.0805 ms
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a successfully announced in 73.0802 ms
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a all the dispatchers started
2025-07-10 15:50:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea all the dispatchers started
2025-07-10 15:50:48 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
2025-07-10 15:50:48 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set."
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:50:48 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"tFYtXaDKEY2IaIO0P1VAJGnfGL9PdszD91GAuzSxKPg"'.
System.InvalidOperationException: An invalid request URI was provided. Either the request URI must be an absolute URI or BaseAddress must be set.
   at System.Net.Http.HttpClient.PrepareRequestMessage(HttpRequestMessage request)
   at System.Net.Http.HttpClient.SendAsync(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue,TJsonOptions](Func`4 getMethod, HttpClient client, Uri requestUri, Func`4 deserializeMethod, TJsonOptions jsonOptions, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.FromJsonAsyncCore[TValue](Func`4 getMethod, HttpClient client, Uri requestUri, JsonSerializerOptions options, CancellationToken cancellationToken)
   at System.Net.Http.Json.HttpClientJsonExtensions.GetFromJsonAsync[TValue](HttpClient client, String requestUri, CancellationToken cancellationToken)
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderPage.OnQueryAsync(QueryPageOptions options) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\PurchaseOrderPage.razor:line 110
   at BootstrapBlazor.Components.Table`1.InternalOnQueryAsync(QueryPageOptions options)
   at BootstrapBlazor.Components.Table`1.<QueryData>g__OnQuery|680_0(QueryPageOptions queryOption)
   at BootstrapBlazor.Components.Table`1.QueryData()
   at BootstrapBlazor.Components.Table`1.QueryAsync(Boolean shouldRender, Nullable`1 pageIndex)
   at BootstrapBlazor.Components.Table`1.ProcessFirstRender()
   at BootstrapBlazor.Components.Table`1.OnAfterRenderAsync(Boolean firstRender)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea caught stopping signal...
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a caught stopping signal...
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a All dispatchers stopped
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a successfully reported itself as stopped in 2.1035 ms
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:55103b6a has been stopped in total 116.1422 ms
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea All dispatchers stopped
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea successfully reported itself as stopped in 0.9988 ms
2025-07-10 15:52:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28828:c6213eea has been stopped in total 232.0075 ms
2025-07-10 15:53:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 15:53:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 15:53:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad successfully announced in 81.5543 ms
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 successfully announced in 82.4205 ms
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 all the dispatchers started
2025-07-10 15:53:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad all the dispatchers started
2025-07-10 16:01:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 caught stopping signal...
2025-07-10 16:01:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad caught stopping signal...
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad caught stopped signal...
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 caught stopped signal...
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 All dispatchers stopped
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad All dispatchers stopped
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 successfully reported itself as stopped in 3.169 ms
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:62f323c2 has been stopped in total 908.4469 ms
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad successfully reported itself as stopped in 1.7096 ms
2025-07-10 16:01:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6468:3d3755ad has been stopped in total 909.3394 ms
2025-07-10 16:01:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:01:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 successfully announced in 72.7757 ms
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b successfully announced in 73.6221 ms
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 all the dispatchers started
2025-07-10 16:01:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b all the dispatchers started
2025-07-10 16:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b caught stopping signal...
2025-07-10 16:02:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 caught stopping signal...
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b All dispatchers stopped
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b successfully reported itself as stopped in 2.2094 ms
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:a6b5dd9b has been stopped in total 301.1284 ms
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 All dispatchers stopped
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 successfully reported itself as stopped in 0.7679 ms
2025-07-10 16:02:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20468:55b5f687 has been stopped in total 350.1775 ms
2025-07-10 16:03:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:03:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:03:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 successfully announced in 87.8396 ms
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f successfully announced in 87.8465 ms
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 all the dispatchers started
2025-07-10 16:03:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f all the dispatchers started
2025-07-10 16:11:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f caught stopping signal...
2025-07-10 16:11:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 caught stopping signal...
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 All dispatchers stopped
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 successfully reported itself as stopped in 3.3346 ms
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:7ad40d18 has been stopped in total 443.0584 ms
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f All dispatchers stopped
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f successfully reported itself as stopped in 0.7716 ms
2025-07-10 16:11:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29608:4bab3f8f has been stopped in total 465.3554 ms
2025-07-10 16:11:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:11:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:11:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 successfully announced in 76.5855 ms
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 successfully announced in 77.4404 ms
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 all the dispatchers started
2025-07-10 16:11:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 all the dispatchers started
2025-07-10 16:16:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 caught stopping signal...
2025-07-10 16:16:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 caught stopping signal...
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 caught stopped signal...
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 caught stopped signal...
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 All dispatchers stopped
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 successfully reported itself as stopped in 1.8576 ms
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:d7400e64 has been stopped in total 623.82 ms
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 All dispatchers stopped
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 successfully reported itself as stopped in 0.7313 ms
2025-07-10 16:16:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20908:f75e84e0 has been stopped in total 651.8123 ms
2025-07-10 16:16:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:16:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:16:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:cd4662a8 successfully announced in 155.8924 ms
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:0a4c6b82 successfully announced in 155.6216 ms
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:0a4c6b82 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:cd4662a8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:0a4c6b82 all the dispatchers started
2025-07-10 16:16:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31716:cd4662a8 all the dispatchers started
2025-07-10 16:26:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:26:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:26:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 successfully announced in 77.9401 ms
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d successfully announced in 78.1937 ms
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d all the dispatchers started
2025-07-10 16:26:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 all the dispatchers started
2025-07-10 16:31:53 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 16:31:53 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d caught stopping signal...
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 caught stopping signal...
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 All dispatchers stopped
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 successfully reported itself as stopped in 2.9784 ms
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:6d36d8e2 has been stopped in total 276.8488 ms
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d caught stopped signal...
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d All dispatchers stopped
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d successfully reported itself as stopped in 1.552 ms
2025-07-10 16:32:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32580:968a6f7d has been stopped in total 672.4078 ms
2025-07-10 16:32:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:32:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:32:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 successfully announced in 71.1749 ms
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 successfully announced in 74.6385 ms
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 all the dispatchers started
2025-07-10 16:32:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 all the dispatchers started
2025-07-10 16:35:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 caught stopping signal...
2025-07-10 16:35:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 caught stopping signal...
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 All dispatchers stopped
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 successfully reported itself as stopped in 2.2797 ms
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:09fca7c2 has been stopped in total 275.5423 ms
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 All dispatchers stopped
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 successfully reported itself as stopped in 0.7837 ms
2025-07-10 16:35:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7732:f61fba27 has been stopped in total 317.7414 ms
2025-07-10 16:35:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:35:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:35:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 successfully announced in 74.0013 ms
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d successfully announced in 74.895 ms
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d all the dispatchers started
2025-07-10 16:35:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 all the dispatchers started
2025-07-10 16:37:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d caught stopping signal...
2025-07-10 16:37:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 caught stopping signal...
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 caught stopped signal...
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d caught stopped signal...
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 All dispatchers stopped
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 successfully reported itself as stopped in 4.3572 ms
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:02d7c808 has been stopped in total 763.3394 ms
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d All dispatchers stopped
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d successfully reported itself as stopped in 2.3053 ms
2025-07-10 16:37:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28380:69dd833d has been stopped in total 770.2435 ms
2025-07-10 16:37:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:37:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:37:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 successfully announced in 72.0297 ms
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 successfully announced in 72.4357 ms
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 all the dispatchers started
2025-07-10 16:37:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 all the dispatchers started
2025-07-10 16:40:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 caught stopping signal...
2025-07-10 16:40:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 caught stopping signal...
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 All dispatchers stopped
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 All dispatchers stopped
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 successfully reported itself as stopped in 1.7466 ms
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:f82c8e02 has been stopped in total 447.1185 ms
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 successfully reported itself as stopped in 1.9946 ms
2025-07-10 16:40:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28584:8dd3f620 has been stopped in total 448.1466 ms
2025-07-10 16:40:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:40:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:40:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 successfully announced in 74.1145 ms
2025-07-10 16:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a successfully announced in 75.1412 ms
2025-07-10 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 all the dispatchers started
2025-07-10 16:40:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a all the dispatchers started
2025-07-10 16:41:19 [Error] HuaLingErpApp.Controller.PurchaseOrderController: 创建采购订单失败
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'po_num'，表 '0412.dbo.po'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.Repository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 39
   at HuaLingErpApp.Controller.PurchaseOrderController.Create(PurchaseOrder model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\PurchaseOrderController.cs:line 70
ClientConnectionId:12929c9c-ca88-4260-9817-f92042ccaa22
Error Number:515,State:2,Class:16
2025-07-10 16:41:24 [Error] HuaLingErpApp.Controller.PurchaseOrderController: 创建采购订单失败
Microsoft.Data.SqlClient.SqlException (0x80131904): 不能将值 NULL 插入列 'po_num'，表 '0412.dbo.po'；列不允许有 Null 值。INSERT 失败。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.Repository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 39
   at HuaLingErpApp.Controller.PurchaseOrderController.Create(PurchaseOrder model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\PurchaseOrderController.cs:line 70
ClientConnectionId:dee65959-ef3c-4aa8-8ac5-0d64a8bbed1c
Error Number:515,State:2,Class:16
2025-07-10 16:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a caught stopping signal...
2025-07-10 16:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 caught stopping signal...
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 caught stopped signal...
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a caught stopped signal...
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 All dispatchers stopped
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 successfully reported itself as stopped in 2.9436 ms
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:81d8a397 has been stopped in total 649.0123 ms
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a All dispatchers stopped
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a successfully reported itself as stopped in 1.1596 ms
2025-07-10 16:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34564:87b3e25a has been stopped in total 718.4307 ms
2025-07-10 16:47:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:47:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d successfully announced in 75.3544 ms
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f successfully announced in 75.6237 ms
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d all the dispatchers started
2025-07-10 16:47:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f all the dispatchers started
2025-07-10 16:50:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d caught stopping signal...
2025-07-10 16:50:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f caught stopping signal...
2025-07-10 16:50:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f caught stopped signal...
2025-07-10 16:50:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d caught stopped signal...
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d All dispatchers stopped
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f All dispatchers stopped
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d successfully reported itself as stopped in 5.1387 ms
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:d313f43d has been stopped in total 848.2651 ms
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f successfully reported itself as stopped in 1.1625 ms
2025-07-10 16:50:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28592:42f9a27f has been stopped in total 852.3721 ms
2025-07-10 16:50:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:50:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:50:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c successfully announced in 72.221 ms
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b successfully announced in 72.0766 ms
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c all the dispatchers started
2025-07-10 16:50:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b all the dispatchers started
2025-07-10 16:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b caught stopping signal...
2025-07-10 16:54:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c caught stopping signal...
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c All dispatchers stopped
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c successfully reported itself as stopped in 2.6216 ms
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:c24df20c has been stopped in total 476.5248 ms
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b All dispatchers stopped
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b successfully reported itself as stopped in 1.2391 ms
2025-07-10 16:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30272:4a6b4f6b has been stopped in total 486.1395 ms
2025-07-10 16:54:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:54:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:54:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 successfully announced in 75.7564 ms
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a successfully announced in 76.8853 ms
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 all the dispatchers started
2025-07-10 16:54:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a all the dispatchers started
2025-07-10 16:56:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 caught stopping signal...
2025-07-10 16:56:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a caught stopping signal...
2025-07-10 16:56:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a All dispatchers stopped
2025-07-10 16:56:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a successfully reported itself as stopped in 1.4047 ms
2025-07-10 16:56:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:c882375a has been stopped in total 27.5845 ms
2025-07-10 16:56:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 caught stopped signal...
2025-07-10 16:56:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 All dispatchers stopped
2025-07-10 16:56:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 successfully reported itself as stopped in 1.2604 ms
2025-07-10 16:56:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26060:00ed6dc2 has been stopped in total 900.2186 ms
2025-07-10 16:57:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:57:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 successfully announced in 74.1351 ms
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 successfully announced in 74.134 ms
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 all the dispatchers started
2025-07-10 16:57:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 all the dispatchers started
2025-07-10 16:59:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 caught stopping signal...
2025-07-10 16:59:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 caught stopping signal...
2025-07-10 16:59:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 caught stopped signal...
2025-07-10 16:59:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 caught stopped signal...
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 All dispatchers stopped
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 All dispatchers stopped
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 successfully reported itself as stopped in 1.9331 ms
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:f3f1d2a2 has been stopped in total 794.5821 ms
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 successfully reported itself as stopped in 9.3534 ms
2025-07-10 16:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14976:a6e03f72 has been stopped in total 802.2278 ms
2025-07-10 16:59:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 16:59:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 16:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 successfully announced in 75.4464 ms
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad successfully announced in 76.284 ms
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad all the dispatchers started
2025-07-10 16:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 all the dispatchers started
2025-07-10 17:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad caught stopping signal...
2025-07-10 17:07:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 caught stopping signal...
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 caught stopped signal...
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad caught stopped signal...
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 All dispatchers stopped
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad All dispatchers stopped
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 successfully reported itself as stopped in 1.8748 ms
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:834c9993 has been stopped in total 601.1797 ms
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad successfully reported itself as stopped in 0.8789 ms
2025-07-10 17:07:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:38032:734af2ad has been stopped in total 602.509 ms
2025-07-10 17:07:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 17:07:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 17:07:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 successfully announced in 71.9773 ms
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 successfully announced in 72.2756 ms
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 all the dispatchers started
2025-07-10 17:07:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 all the dispatchers started
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 caught stopping signal...
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 caught stopping signal...
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 All dispatchers stopped
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 successfully reported itself as stopped in 2.785 ms
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:b0fef513 has been stopped in total 234.8065 ms
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 All dispatchers stopped
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 successfully reported itself as stopped in 1.0636 ms
2025-07-10 17:09:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25040:93a46401 has been stopped in total 256.0812 ms
2025-07-10 17:09:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-10 17:09:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-10 17:09:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-10 17:09:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 successfully announced in 70.2618 ms
2025-07-10 17:09:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 successfully announced in 68.6218 ms
2025-07-10 17:09:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 17:09:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-10 17:09:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 all the dispatchers started
2025-07-10 17:09:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 all the dispatchers started
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 caught stopping signal...
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 caught stopping signal...
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 All dispatchers stopped
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 All dispatchers stopped
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 successfully reported itself as stopped in 3.0851 ms
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:af94ada0 has been stopped in total 62.0808 ms
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 successfully reported itself as stopped in 6.3607 ms
2025-07-10 17:17:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33992:63583b61 has been stopped in total 66.3073 ms
