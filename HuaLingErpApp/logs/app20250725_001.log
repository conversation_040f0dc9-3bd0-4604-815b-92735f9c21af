2025-07-25 11:27:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:27:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:27:16 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:27:16 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:27:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d successfully announced in 59.5585 ms
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba successfully announced in 59.3823 ms
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d all the dispatchers started
2025-07-25 11:27:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba all the dispatchers started
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d caught stopping signal...
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba caught stopping signal...
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba All dispatchers stopped
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d All dispatchers stopped
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba successfully reported itself as stopped in 2.8934 ms
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:a4ffc7ba has been stopped in total 170.6342 ms
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d successfully reported itself as stopped in 1.6751 ms
2025-07-25 11:27:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30612:bfd5e14d has been stopped in total 171.4853 ms
2025-07-25 11:29:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:29:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:29:25 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:29:25 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:29:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 successfully announced in 64.6724 ms
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac successfully announced in 64.8182 ms
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 all the dispatchers started
2025-07-25 11:29:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac all the dispatchers started
2025-07-25 11:29:47 [Error] HuaLingErpApp.Controller.ItemController: Error validating Excel file
System.InvalidOperationException: Object un-ignore properties count can't be 0
   at MiniExcelLibs.Utils.CustomPropertyHelper.GetExcelCustomPropertyInfos(Type type, String[] keys, Configuration configuration)
   at MiniExcelLibs.OpenXml.ExcelOpenXmlSheetReader.QueryImpl[T](IEnumerable`1 values, String startCell, Boolean hasHeader, Configuration configuration)+MoveNext()
   at MiniExcelLibs.MiniExcel.Query[T](Stream stream, String sheetName, ExcelType excelType, String startCell, IConfiguration configuration, Boolean hasHeader)+MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at HuaLingErpApp.Controller.ItemController.ValidateExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 392
2025-07-25 11:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 caught stopping signal...
2025-07-25 11:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac caught stopping signal...
2025-07-25 11:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac caught stopped signal...
2025-07-25 11:35:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 caught stopped signal...
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac All dispatchers stopped
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 All dispatchers stopped
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 successfully reported itself as stopped in 1.3795 ms
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:b2218729 has been stopped in total 676.1978 ms
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac successfully reported itself as stopped in 2.4277 ms
2025-07-25 11:35:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20312:a2622cac has been stopped in total 676.3077 ms
2025-07-25 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:37:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:37:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:37:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:37:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c successfully announced in 58.0247 ms
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 successfully announced in 58.2109 ms
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c all the dispatchers started
2025-07-25 11:37:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 all the dispatchers started
2025-07-25 11:37:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 caught stopping signal...
2025-07-25 11:37:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c caught stopping signal...
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 All dispatchers stopped
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c All dispatchers stopped
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 successfully reported itself as stopped in 1.7393 ms
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c successfully reported itself as stopped in 1.7316 ms
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:a09f8149 has been stopped in total 388.838 ms
2025-07-25 11:37:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:5044:676dbe4c has been stopped in total 388.6724 ms
2025-07-25 11:38:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:38:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:38:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:38:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:38:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb successfully announced in 62.1841 ms
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 successfully announced in 65.3847 ms
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 all the dispatchers started
2025-07-25 11:38:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb all the dispatchers started
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 caught stopping signal...
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb caught stopping signal...
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 All dispatchers stopped
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 successfully reported itself as stopped in 1.8235 ms
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:d04a6e07 has been stopped in total 140.623 ms
2025-07-25 11:45:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb caught stopped signal...
2025-07-25 11:45:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb All dispatchers stopped
2025-07-25 11:45:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb successfully reported itself as stopped in 0.9563 ms
2025-07-25 11:45:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30260:ca95cdfb has been stopped in total 953.8224 ms
