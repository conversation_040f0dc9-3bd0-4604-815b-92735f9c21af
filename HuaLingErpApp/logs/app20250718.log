2025-07-18 09:14:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:14:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:14:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 successfully announced in 118.7166 ms
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 successfully announced in 119.3915 ms
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 all the dispatchers started
2025-07-18 09:14:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 all the dispatchers started
2025-07-18 09:15:23 [Error] HuaLingErpApp.Controller.ItemController: Failed to retrieve inventory for item HL001
System.InvalidCastException: Failed to convert parameter value from a Item to a String.
 ---> System.InvalidCastException: Object must implement IConvertible.
   at System.Convert.ChangeType(Object value, Type conversionType, IFormatProvider provider)
   at Microsoft.Data.SqlClient.SqlParameter.CoerceValue(Object value, MetaType destinationType, Boolean& coercedToDataFeed, Boolean& typeChanged, Boolean allowStreaming)
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Controller.ItemController.GetItemInventory(String id) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 64
2025-07-18 09:15:28 [Error] HuaLingErpApp.Controller.ItemController: Failed to retrieve inventory for item 091-13100-161
System.InvalidCastException: Failed to convert parameter value from a Item to a String.
 ---> System.InvalidCastException: Object must implement IConvertible.
   at System.Convert.ChangeType(Object value, Type conversionType, IFormatProvider provider)
   at Microsoft.Data.SqlClient.SqlParameter.CoerceValue(Object value, MetaType destinationType, Boolean& coercedToDataFeed, Boolean& typeChanged, Boolean allowStreaming)
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Controller.ItemController.GetItemInventory(String id) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 64
2025-07-18 09:15:32 [Error] HuaLingErpApp.Controller.ItemController: Failed to retrieve inventory for item 091-13100-161
System.InvalidCastException: Failed to convert parameter value from a Item to a String.
 ---> System.InvalidCastException: Object must implement IConvertible.
   at System.Convert.ChangeType(Object value, Type conversionType, IFormatProvider provider)
   at Microsoft.Data.SqlClient.SqlParameter.CoerceValue(Object value, MetaType destinationType, Boolean& coercedToDataFeed, Boolean& typeChanged, Boolean allowStreaming)
   --- End of inner exception stack trace ---
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Controller.ItemController.GetItemInventory(String id) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 64
2025-07-18 09:18:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 caught stopping signal...
2025-07-18 09:18:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 caught stopping signal...
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 caught stopped signal...
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 caught stopped signal...
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 All dispatchers stopped
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 All dispatchers stopped
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 successfully reported itself as stopped in 2.4922 ms
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:9ef7e7f6 has been stopped in total 708.6361 ms
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 successfully reported itself as stopped in 1.086 ms
2025-07-18 09:18:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20576:f2e9b9c3 has been stopped in total 708.4455 ms
2025-07-18 09:18:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:18:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:18:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c successfully announced in 111.9099 ms
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 successfully announced in 112.8461 ms
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c all the dispatchers started
2025-07-18 09:18:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 all the dispatchers started
2025-07-18 09:19:23 [Error] HuaLingErpApp.Controller.ItemController: Failed to retrieve inventory for item HL001
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'description' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Controller.ItemController.GetItemInventory(String id) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 64
ClientConnectionId:b7c5807c-456c-48af-900f-3dc72e72f76b
Error Number:207,State:1,Class:16
2025-07-18 09:21:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c caught stopping signal...
2025-07-18 09:21:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 caught stopping signal...
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c All dispatchers stopped
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 All dispatchers stopped
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c successfully reported itself as stopped in 2.2478 ms
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:2dd7b29c has been stopped in total 466.6221 ms
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 successfully reported itself as stopped in 0.7192 ms
2025-07-18 09:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25264:9fb40740 has been stopped in total 466.4374 ms
2025-07-18 09:21:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:21:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:21:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 successfully announced in 110.7891 ms
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f successfully announced in 111.442 ms
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f all the dispatchers started
2025-07-18 09:21:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 all the dispatchers started
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 caught stopping signal...
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f caught stopping signal...
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f caught stopped signal...
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 caught stopped signal...
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 All dispatchers stopped
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f All dispatchers stopped
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 successfully reported itself as stopped in 2.5311 ms
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f successfully reported itself as stopped in 0.876 ms
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:b78f71a3 has been stopped in total 751.4431 ms
2025-07-18 09:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23732:2775560f has been stopped in total 751.2786 ms
2025-07-18 09:25:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:25:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:25:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 successfully announced in 118.1336 ms
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 successfully announced in 118.8331 ms
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 all the dispatchers started
2025-07-18 09:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 all the dispatchers started
2025-07-18 09:29:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 caught stopping signal...
2025-07-18 09:29:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 caught stopping signal...
2025-07-18 09:29:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 All dispatchers stopped
2025-07-18 09:29:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 successfully reported itself as stopped in 1.9507 ms
2025-07-18 09:29:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:98888388 has been stopped in total 55.2533 ms
2025-07-18 09:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 All dispatchers stopped
2025-07-18 09:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 successfully reported itself as stopped in 0.7947 ms
2025-07-18 09:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27220:3520bb37 has been stopped in total 68.4958 ms
2025-07-18 09:30:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:30:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:30:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 successfully announced in 116.0279 ms
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba successfully announced in 116.0451 ms
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 all the dispatchers started
2025-07-18 09:30:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba all the dispatchers started
2025-07-18 09:30:11 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba caught stopping signal...
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 caught stopping signal...
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba All dispatchers stopped
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba successfully reported itself as stopped in 2.5164 ms
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:9d8557ba has been stopped in total 73.2282 ms
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 caught stopped signal...
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 All dispatchers stopped
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 successfully reported itself as stopped in 1.4439 ms
2025-07-18 09:32:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26804:6c4f4868 has been stopped in total 846.5115 ms
2025-07-18 09:32:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:32:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:32:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:a7ecf4b0 successfully announced in 287.5452 ms
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:7d80ad63 successfully announced in 291.6782 ms
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:7d80ad63 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:a7ecf4b0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:a7ecf4b0 all the dispatchers started
2025-07-18 09:32:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18644:7d80ad63 all the dispatchers started
2025-07-18 09:37:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:37:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:37:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:3a8390fb successfully announced in 240.8169 ms
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:f736d7d5 successfully announced in 240.7543 ms
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:3a8390fb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:f736d7d5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:3a8390fb all the dispatchers started
2025-07-18 09:37:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21728:f736d7d5 all the dispatchers started
2025-07-18 09:41:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:41:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:41:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 successfully announced in 109.0458 ms
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f successfully announced in 94.0255 ms
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f all the dispatchers started
2025-07-18 09:41:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 all the dispatchers started
2025-07-18 09:42:14 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 09:46:32 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 09:46:32 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 caught stopping signal...
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f caught stopping signal...
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f All dispatchers stopped
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 All dispatchers stopped
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f successfully reported itself as stopped in 2.2112 ms
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:15c9d41f has been stopped in total 122.791 ms
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 successfully reported itself as stopped in 1.4487 ms
2025-07-18 09:49:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2676:89f97462 has been stopped in total 123.4373 ms
2025-07-18 09:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:49:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad successfully announced in 116.3463 ms
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 successfully announced in 116.3401 ms
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad all the dispatchers started
2025-07-18 09:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 all the dispatchers started
2025-07-18 09:49:31 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 09:49:50 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 09:49:50 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"QK3Rfpa5yXZs4Z3_-SEIXREYiTVMCMA8xxl8-N1Ehek"'.
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad caught stopping signal...
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 caught stopping signal...
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad All dispatchers stopped
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad successfully reported itself as stopped in 1.7154 ms
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:d7e381ad has been stopped in total 353.2424 ms
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 All dispatchers stopped
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 successfully reported itself as stopped in 1.4743 ms
2025-07-18 09:51:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25344:e180ca58 has been stopped in total 358.8967 ms
2025-07-18 09:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:51:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:51:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 successfully announced in 109.8826 ms
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 successfully announced in 116.4365 ms
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 all the dispatchers started
2025-07-18 09:51:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 all the dispatchers started
2025-07-18 09:52:11 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 09:53:05 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 09:53:05 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"oy-WMQdESt6n1SmIx0nrDqOQ_hncZwle6esIGSsr2j4"'.
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 09:55:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 caught stopping signal...
2025-07-18 09:55:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 caught stopping signal...
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 All dispatchers stopped
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 All dispatchers stopped
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 successfully reported itself as stopped in 1.6341 ms
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:2a63ff67 has been stopped in total 449.5463 ms
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 successfully reported itself as stopped in 1.5194 ms
2025-07-18 09:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:89c763b4 has been stopped in total 450.6363 ms
2025-07-18 09:55:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:55:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:55:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd successfully announced in 113.7068 ms
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 successfully announced in 113.691 ms
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd all the dispatchers started
2025-07-18 09:55:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 all the dispatchers started
2025-07-18 09:55:38 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 caught stopping signal...
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd caught stopping signal...
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 All dispatchers stopped
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 successfully reported itself as stopped in 2.3757 ms
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:9bd8c591 has been stopped in total 447.2424 ms
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd All dispatchers stopped
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd successfully reported itself as stopped in 1.3483 ms
2025-07-18 09:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:2f8348dd has been stopped in total 476.106 ms
2025-07-18 09:57:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 09:57:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 09:57:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:c6d777d3 successfully announced in 292.7452 ms
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:580222c3 successfully announced in 292.7965 ms
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:c6d777d3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:580222c3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:580222c3 all the dispatchers started
2025-07-18 09:57:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7724:c6d777d3 all the dispatchers started
2025-07-18 10:04:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:04:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:04:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:04:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:04:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:04:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:04:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 successfully announced in 110.8052 ms
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 successfully announced in 110.7895 ms
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 all the dispatchers started
2025-07-18 10:04:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 all the dispatchers started
2025-07-18 10:04:14 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 caught stopping signal...
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 caught stopping signal...
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 All dispatchers stopped
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 All dispatchers stopped
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 successfully reported itself as stopped in 2.3706 ms
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 successfully reported itself as stopped in 2.3907 ms
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:390378c6 has been stopped in total 74.84 ms
2025-07-18 10:04:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29392:8c8d9da9 has been stopped in total 74.7191 ms
2025-07-18 10:05:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:05:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:05:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:8422f849 successfully announced in 321.8807 ms
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:6046bc13 successfully announced in 321.8818 ms
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:6046bc13 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:8422f849 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:6046bc13 all the dispatchers started
2025-07-18 10:05:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22352:8422f849 all the dispatchers started
2025-07-18 10:12:36 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 10:12:39 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 10:14:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:14:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:14:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 successfully announced in 122.8243 ms
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf successfully announced in 122.3465 ms
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf all the dispatchers started
2025-07-18 10:14:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 all the dispatchers started
2025-07-18 10:14:37 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_3.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 10:14:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf caught stopping signal...
2025-07-18 10:14:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 caught stopping signal...
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf All dispatchers stopped
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 All dispatchers stopped
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf successfully reported itself as stopped in 2.7084 ms
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 successfully reported itself as stopped in 1.7488 ms
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:b65551cf has been stopped in total 294.882 ms
2025-07-18 10:14:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21936:e3072cc7 has been stopped in total 294.7802 ms
2025-07-18 10:15:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:15:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:15:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:ecc22963 successfully announced in 303.4346 ms
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:e956ab3e successfully announced in 303.4272 ms
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:e956ab3e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:ecc22963 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:e956ab3e all the dispatchers started
2025-07-18 10:15:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:ecc22963 all the dispatchers started
2025-07-18 10:15:07 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "The signature is incorrect."
System.TypeLoadException: The signature is incorrect.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_0.<BuildRenderTree>b__7(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 10:15:07 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.TypeLoadException: The signature is incorrect.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.BuildRenderTree(RenderTreeBuilder __builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.GetErrorHandledTask(Task taskToHandle, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.<WaitForNonStreamingPendingTasks>g__Execute|43_0()
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.WaitForResultReady(Boolean waitForQuiescence, PrerenderedComponentHtmlContent result)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-18 10:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:e956ab3e caught stopping signal...
2025-07-18 10:15:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21716:ecc22963 caught stopping signal...
2025-07-18 10:17:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:17:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:17:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:c4a364e4 successfully announced in 290.0298 ms
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:35e80289 successfully announced in 290.0316 ms
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:c4a364e4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:35e80289 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:35e80289 all the dispatchers started
2025-07-18 10:17:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11772:c4a364e4 all the dispatchers started
2025-07-18 10:18:29 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at HuaLingErpApp.Client.Pages.Procurement.PurchaseOrderReceivingPage.<>c__DisplayClass0_4.<BuildRenderTree>b__31(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.Rendering.RenderTreeBuilder.AddContent(Int32 sequence, RenderFragment fragment)
   at BootstrapBlazor.Components.Table`1.<BuildRenderTree>b__1607_0(RenderTreeBuilder __builder2)
   at Microsoft.AspNetCore.Components.CascadingValue`1.Render(RenderTreeBuilder builder)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
2025-07-18 10:20:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:20:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:20:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 successfully announced in 116.3705 ms
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff successfully announced in 116.3947 ms
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:20:17 [Information] Hangfire.Server.ServerWatchdog: 3 servers were removed due to timeout
2025-07-18 10:20:17 [Information] Hangfire.Server.ServerWatchdog: 1 servers were removed due to timeout
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 all the dispatchers started
2025-07-18 10:20:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff all the dispatchers started
2025-07-18 10:21:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 caught stopping signal...
2025-07-18 10:21:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff caught stopping signal...
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff All dispatchers stopped
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff successfully reported itself as stopped in 2.5622 ms
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:32d645ff has been stopped in total 312.0967 ms
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 All dispatchers stopped
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 successfully reported itself as stopped in 0.8368 ms
2025-07-18 10:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10088:97185530 has been stopped in total 314.3343 ms
2025-07-18 10:22:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:22:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:22:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 successfully announced in 84.1103 ms
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 successfully announced in 84.6255 ms
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 all the dispatchers started
2025-07-18 10:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 all the dispatchers started
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 caught stopping signal...
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 caught stopping signal...
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 All dispatchers stopped
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 successfully reported itself as stopped in 2.4757 ms
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:d3184f34 has been stopped in total 51.6856 ms
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 All dispatchers stopped
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 successfully reported itself as stopped in 0.8331 ms
2025-07-18 10:27:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24220:de44e8e9 has been stopped in total 105.9537 ms
2025-07-18 10:27:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:27:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:27:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b successfully announced in 83.9088 ms
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd successfully announced in 84.757 ms
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:27:32 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd all the dispatchers started
2025-07-18 10:27:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b all the dispatchers started
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b caught stopping signal...
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd caught stopping signal...
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b All dispatchers stopped
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd All dispatchers stopped
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b successfully reported itself as stopped in 1.0413 ms
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd successfully reported itself as stopped in 1.8019 ms
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:fb71336b has been stopped in total 153.7284 ms
2025-07-18 10:29:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22332:97b2f4cd has been stopped in total 153.6412 ms
2025-07-18 10:29:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:29:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:29:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e successfully announced in 80.4954 ms
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 successfully announced in 83.848 ms
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e all the dispatchers started
2025-07-18 10:29:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 all the dispatchers started
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e caught stopping signal...
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 caught stopping signal...
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 All dispatchers stopped
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 successfully reported itself as stopped in 1.4733 ms
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:3c6208a1 has been stopped in total 194.0095 ms
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e All dispatchers stopped
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e successfully reported itself as stopped in 1.7206 ms
2025-07-18 10:30:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8144:49a0382e has been stopped in total 259.455 ms
2025-07-18 10:31:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:31:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:31:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:31:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe successfully announced in 84.029 ms
2025-07-18 10:31:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 successfully announced in 89.8877 ms
2025-07-18 10:31:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:31:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:31:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe all the dispatchers started
2025-07-18 10:31:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 all the dispatchers started
2025-07-18 10:32:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe caught stopping signal...
2025-07-18 10:32:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 caught stopping signal...
2025-07-18 10:32:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 caught stopped signal...
2025-07-18 10:32:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe caught stopped signal...
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 All dispatchers stopped
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 successfully reported itself as stopped in 2.3299 ms
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:de715eb5 has been stopped in total 969.3113 ms
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe All dispatchers stopped
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe successfully reported itself as stopped in 1.3652 ms
2025-07-18 10:32:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20044:e1118cfe has been stopped in total 976.4975 ms
2025-07-18 10:32:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:32:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:32:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 successfully announced in 81.5272 ms
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 successfully announced in 81.8086 ms
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 all the dispatchers started
2025-07-18 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 all the dispatchers started
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 caught stopping signal...
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 caught stopping signal...
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 caught stopped signal...
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 caught stopped signal...
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 All dispatchers stopped
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 successfully reported itself as stopped in 2.4373 ms
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:427d7444 has been stopped in total 538.3312 ms
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 All dispatchers stopped
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 successfully reported itself as stopped in 1.201 ms
2025-07-18 10:33:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25624:81b845c9 has been stopped in total 636.7932 ms
2025-07-18 10:33:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:33:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:33:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 successfully announced in 81.6924 ms
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 successfully announced in 82.9994 ms
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 all the dispatchers started
2025-07-18 10:33:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 all the dispatchers started
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 caught stopping signal...
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 caught stopping signal...
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 All dispatchers stopped
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 successfully reported itself as stopped in 2.5897 ms
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:9981cb95 has been stopped in total 220.9616 ms
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 All dispatchers stopped
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 successfully reported itself as stopped in 0.7441 ms
2025-07-18 10:39:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27204:8be799a4 has been stopped in total 396.4544 ms
2025-07-18 10:43:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:43:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:43:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 successfully announced in 87.1046 ms
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 successfully announced in 90.4432 ms
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 all the dispatchers started
2025-07-18 10:43:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 all the dispatchers started
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 caught stopping signal...
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 caught stopping signal...
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 All dispatchers stopped
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 successfully reported itself as stopped in 2.5959 ms
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:d5105047 has been stopped in total 401.9189 ms
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 caught stopped signal...
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 All dispatchers stopped
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 successfully reported itself as stopped in 0.7518 ms
2025-07-18 10:44:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32056:dcbf91d2 has been stopped in total 563.3953 ms
2025-07-18 10:44:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:44:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:44:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 successfully announced in 85.0447 ms
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 successfully announced in 85.0436 ms
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 all the dispatchers started
2025-07-18 10:44:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 all the dispatchers started
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 caught stopping signal...
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 caught stopping signal...
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 All dispatchers stopped
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 successfully reported itself as stopped in 2.0232 ms
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:afc64e68 has been stopped in total 170.6921 ms
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 All dispatchers stopped
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 successfully reported itself as stopped in 0.9094 ms
2025-07-18 10:45:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13800:5ae02b08 has been stopped in total 205.1554 ms
2025-07-18 10:45:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:45:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:45:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d successfully announced in 79.3178 ms
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 successfully announced in 79.3182 ms
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d all the dispatchers started
2025-07-18 10:45:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 all the dispatchers started
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d caught stopping signal...
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 caught stopping signal...
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d All dispatchers stopped
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d successfully reported itself as stopped in 1.7678 ms
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:fb38042d has been stopped in total 165.1821 ms
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 All dispatchers stopped
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 successfully reported itself as stopped in 1.1545 ms
2025-07-18 10:46:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20564:a8eeb4c6 has been stopped in total 168.4633 ms
2025-07-18 10:46:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:46:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:46:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 successfully announced in 70.2387 ms
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 successfully announced in 88.7918 ms
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 all the dispatchers started
2025-07-18 10:46:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 all the dispatchers started
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 caught stopping signal...
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 caught stopping signal...
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 All dispatchers stopped
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 successfully reported itself as stopped in 2.1925 ms
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:f2319560 has been stopped in total 143.6495 ms
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 caught stopped signal...
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 All dispatchers stopped
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 successfully reported itself as stopped in 0.8379 ms
2025-07-18 10:51:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12752:b79fd8b6 has been stopped in total 779.8938 ms
2025-07-18 10:51:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:51:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:51:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c successfully announced in 81.1692 ms
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 successfully announced in 69.1535 ms
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 all the dispatchers started
2025-07-18 10:51:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c all the dispatchers started
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c caught stopping signal...
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 caught stopping signal...
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 caught stopped signal...
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c caught stopped signal...
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c All dispatchers stopped
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 All dispatchers stopped
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c successfully reported itself as stopped in 1.4652 ms
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:3677844c has been stopped in total 608.6327 ms
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 successfully reported itself as stopped in 19.7043 ms
2025-07-18 10:55:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31004:0afdb319 has been stopped in total 627.5646 ms
2025-07-18 10:56:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:56:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:56:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:56:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:56:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:56:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 successfully announced in 80.893 ms
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 successfully announced in 82.2732 ms
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 all the dispatchers started
2025-07-18 10:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 all the dispatchers started
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 caught stopping signal...
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 caught stopping signal...
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 All dispatchers stopped
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 successfully reported itself as stopped in 1.9265 ms
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:25c6c820 has been stopped in total 395.7494 ms
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 All dispatchers stopped
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 successfully reported itself as stopped in 1.5839 ms
2025-07-18 10:59:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18072:2eb91574 has been stopped in total 496.4808 ms
2025-07-18 10:59:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 10:59:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 10:59:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 successfully announced in 81.7987 ms
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 successfully announced in 83.2321 ms
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 all the dispatchers started
2025-07-18 10:59:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 all the dispatchers started
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 caught stopping signal...
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 caught stopping signal...
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 All dispatchers stopped
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 successfully reported itself as stopped in 1.8726 ms
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:0728ca20 has been stopped in total 22.0584 ms
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 All dispatchers stopped
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 successfully reported itself as stopped in 1.6107 ms
2025-07-18 11:00:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17668:17939dd0 has been stopped in total 137.9285 ms
2025-07-18 11:01:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:01:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:01:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c successfully announced in 77.5129 ms
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 successfully announced in 52.0355 ms
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 all the dispatchers started
2025-07-18 11:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c all the dispatchers started
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c caught stopping signal...
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 caught stopping signal...
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c All dispatchers stopped
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c successfully reported itself as stopped in 1.4333 ms
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:2b08778c has been stopped in total 371.191 ms
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 All dispatchers stopped
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 successfully reported itself as stopped in 0.6096 ms
2025-07-18 11:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23568:a95d4a13 has been stopped in total 394.7249 ms
2025-07-18 11:02:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:02:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:02:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 successfully announced in 85.8456 ms
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 successfully announced in 84.6328 ms
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 all the dispatchers started
2025-07-18 11:02:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 all the dispatchers started
2025-07-18 11:03:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 caught stopping signal...
2025-07-18 11:03:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 caught stopping signal...
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 caught stopped signal...
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 caught stopped signal...
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 All dispatchers stopped
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 successfully reported itself as stopped in 1.6462 ms
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:d6043ee4 has been stopped in total 675.2382 ms
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 All dispatchers stopped
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 successfully reported itself as stopped in 0.7111 ms
2025-07-18 11:03:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29800:bb404f42 has been stopped in total 683.8666 ms
2025-07-18 11:03:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:03:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:03:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f successfully announced in 86.67 ms
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 successfully announced in 86.6726 ms
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 all the dispatchers started
2025-07-18 11:03:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f all the dispatchers started
2025-07-18 11:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f caught stopping signal...
2025-07-18 11:04:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 caught stopping signal...
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 caught stopped signal...
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f caught stopped signal...
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 All dispatchers stopped
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 successfully reported itself as stopped in 2.0005 ms
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:b402a107 has been stopped in total 933.2464 ms
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f All dispatchers stopped
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f successfully reported itself as stopped in 1.6957 ms
2025-07-18 11:04:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21388:2c9b771f has been stopped in total 971.0744 ms
2025-07-18 11:04:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:04:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:04:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f successfully announced in 81.868 ms
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 successfully announced in 82.0654 ms
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f all the dispatchers started
2025-07-18 11:04:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 all the dispatchers started
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f caught stopping signal...
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 caught stopping signal...
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 All dispatchers stopped
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 successfully reported itself as stopped in 1.5557 ms
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:ad8ffa86 has been stopped in total 351.1829 ms
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f All dispatchers stopped
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f successfully reported itself as stopped in 0.7258 ms
2025-07-18 11:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18076:75a98a0f has been stopped in total 364.5287 ms
2025-07-18 11:06:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:06:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:06:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 successfully announced in 86.9727 ms
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 successfully announced in 54.4967 ms
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 all the dispatchers started
2025-07-18 11:06:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 all the dispatchers started
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 caught stopping signal...
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 caught stopping signal...
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 caught stopped signal...
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 caught stopped signal...
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 All dispatchers stopped
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 successfully reported itself as stopped in 2.4999 ms
2025-07-18 11:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:afcc3f66 has been stopped in total 771.427 ms
2025-07-18 11:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 All dispatchers stopped
2025-07-18 11:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 successfully reported itself as stopped in 1.5161 ms
2025-07-18 11:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:796:49f6de32 has been stopped in total 869.3079 ms
2025-07-18 11:18:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:18:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:18:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 successfully announced in 82.482 ms
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a successfully announced in 86.0841 ms
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 all the dispatchers started
2025-07-18 11:18:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a all the dispatchers started
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a caught stopping signal...
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 caught stopping signal...
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 caught stopped signal...
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a caught stopped signal...
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 All dispatchers stopped
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 successfully reported itself as stopped in 2.3068 ms
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:9cfc0891 has been stopped in total 751.1467 ms
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a All dispatchers stopped
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a successfully reported itself as stopped in 1.3399 ms
2025-07-18 11:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4232:6af6918a has been stopped in total 759.1647 ms
2025-07-18 11:20:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:20:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:20:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 successfully announced in 80.4599 ms
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed successfully announced in 80.4591 ms
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed all the dispatchers started
2025-07-18 11:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 all the dispatchers started
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed caught stopping signal...
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 caught stopping signal...
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 caught stopped signal...
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed caught stopped signal...
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 All dispatchers stopped
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed All dispatchers stopped
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed successfully reported itself as stopped in 2.4515 ms
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:f5fe81ed has been stopped in total 667.3694 ms
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 successfully reported itself as stopped in 2.4717 ms
2025-07-18 11:24:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31336:2f72b3b4 has been stopped in total 667.3192 ms
2025-07-18 11:24:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:24:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:24:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e successfully announced in 80.9314 ms
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 successfully announced in 81.1095 ms
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e all the dispatchers started
2025-07-18 11:24:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 all the dispatchers started
2025-07-18 11:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e caught stopping signal...
2025-07-18 11:27:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 caught stopping signal...
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 caught stopped signal...
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e caught stopped signal...
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e All dispatchers stopped
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e successfully reported itself as stopped in 1.8312 ms
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:444c2d0e has been stopped in total 949.5538 ms
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 All dispatchers stopped
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 successfully reported itself as stopped in 5.6213 ms
2025-07-18 11:27:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31000:b29f7e52 has been stopped in total 1002.7612 ms
2025-07-18 11:27:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:27:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:27:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 successfully announced in 80.8963 ms
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 successfully announced in 83.6461 ms
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 all the dispatchers started
2025-07-18 11:27:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 all the dispatchers started
2025-07-18 11:33:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 caught stopping signal...
2025-07-18 11:33:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 caught stopping signal...
2025-07-18 11:33:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 caught stopped signal...
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 caught stopped signal...
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 All dispatchers stopped
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 All dispatchers stopped
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 successfully reported itself as stopped in 1.8078 ms
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:615ca963 has been stopped in total 845.8043 ms
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 successfully reported itself as stopped in 1.046 ms
2025-07-18 11:33:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24028:9b29ed89 has been stopped in total 845.8798 ms
2025-07-18 11:33:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:33:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:33:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a successfully announced in 38.275 ms
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b successfully announced in 82.2347 ms
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a all the dispatchers started
2025-07-18 11:33:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b all the dispatchers started
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b caught stopping signal...
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a caught stopping signal...
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b All dispatchers stopped
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b successfully reported itself as stopped in 6.386 ms
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:4b02524b has been stopped in total 38.1119 ms
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a caught stopped signal...
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a All dispatchers stopped
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a successfully reported itself as stopped in 1.2204 ms
2025-07-18 11:36:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29776:def5039a has been stopped in total 836.0251 ms
2025-07-18 11:36:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:36:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:36:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf successfully announced in 81.9339 ms
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d successfully announced in 81.9357 ms
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d all the dispatchers started
2025-07-18 11:36:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf all the dispatchers started
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d caught stopping signal...
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf caught stopping signal...
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf caught stopped signal...
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d caught stopped signal...
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf All dispatchers stopped
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d All dispatchers stopped
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf successfully reported itself as stopped in 2.2478 ms
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:95b768cf has been stopped in total 517.864 ms
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d successfully reported itself as stopped in 1.0959 ms
2025-07-18 11:39:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14376:61a6be6d has been stopped in total 518.2957 ms
2025-07-18 11:40:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:40:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:40:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:40:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 successfully announced in 85.1714 ms
2025-07-18 11:40:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c successfully announced in 84.9879 ms
2025-07-18 11:40:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:40:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:40:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c all the dispatchers started
2025-07-18 11:40:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 all the dispatchers started
2025-07-18 11:44:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c caught stopping signal...
2025-07-18 11:44:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 caught stopping signal...
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 All dispatchers stopped
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c All dispatchers stopped
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 successfully reported itself as stopped in 1.7074 ms
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:3efc6ee9 has been stopped in total 454.7734 ms
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c successfully reported itself as stopped in 2.4329 ms
2025-07-18 11:44:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33692:243a2a7c has been stopped in total 455.8554 ms
2025-07-18 11:44:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:44:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 successfully announced in 84.3291 ms
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c successfully announced in 84.251 ms
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c all the dispatchers started
2025-07-18 11:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 all the dispatchers started
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 caught stopping signal...
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c caught stopping signal...
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c caught stopped signal...
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 caught stopped signal...
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c All dispatchers stopped
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c successfully reported itself as stopped in 1.6612 ms
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:e76aff5c has been stopped in total 733.9332 ms
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 All dispatchers stopped
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 successfully reported itself as stopped in 1.3271 ms
2025-07-18 11:46:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24024:6e4a4ae2 has been stopped in total 861.7871 ms
2025-07-18 11:47:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:47:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:47:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 successfully announced in 78.9917 ms
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 successfully announced in 79.8256 ms
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 all the dispatchers started
2025-07-18 11:47:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 all the dispatchers started
2025-07-18 11:47:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 caught stopping signal...
2025-07-18 11:47:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 caught stopping signal...
2025-07-18 11:47:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 All dispatchers stopped
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 successfully reported itself as stopped in 1.578 ms
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:73e9e149 has been stopped in total 367.8201 ms
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 All dispatchers stopped
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 caught stopped signal...
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 successfully reported itself as stopped in 1.0614 ms
2025-07-18 11:47:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20648:3dafa014 has been stopped in total 568.0603 ms
2025-07-18 11:48:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:48:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:48:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca successfully announced in 81.7353 ms
2025-07-18 11:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a successfully announced in 81.7357 ms
2025-07-18 11:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca all the dispatchers started
2025-07-18 11:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a all the dispatchers started
2025-07-18 11:48:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a caught stopping signal...
2025-07-18 11:48:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca caught stopping signal...
2025-07-18 11:48:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca caught stopped signal...
2025-07-18 11:48:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a caught stopped signal...
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a All dispatchers stopped
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a successfully reported itself as stopped in 2.9392 ms
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:1ec4d07a has been stopped in total 943.6786 ms
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca All dispatchers stopped
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca successfully reported itself as stopped in 0.7295 ms
2025-07-18 11:48:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32428:a77899ca has been stopped in total 946.1408 ms
2025-07-18 11:48:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:48:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:48:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 successfully announced in 80.5093 ms
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 successfully announced in 81.436 ms
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 all the dispatchers started
2025-07-18 11:48:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 all the dispatchers started
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 caught stopping signal...
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 caught stopping signal...
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 All dispatchers stopped
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 successfully reported itself as stopped in 1.6326 ms
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:d15bac18 has been stopped in total 408.5929 ms
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 All dispatchers stopped
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 successfully reported itself as stopped in 1.4809 ms
2025-07-18 11:50:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22156:39d43e12 has been stopped in total 475.8623 ms
2025-07-18 11:51:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:51:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:51:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 successfully announced in 82.8602 ms
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca successfully announced in 83.2969 ms
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca all the dispatchers started
2025-07-18 11:51:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 all the dispatchers started
2025-07-18 11:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca caught stopping signal...
2025-07-18 11:51:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 caught stopping signal...
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 caught stopped signal...
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca caught stopped signal...
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca All dispatchers stopped
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca successfully reported itself as stopped in 2.1877 ms
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:a71814ca has been stopped in total 870.8592 ms
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 All dispatchers stopped
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 successfully reported itself as stopped in 1.4817 ms
2025-07-18 11:51:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14528:76688ba4 has been stopped in total 886.6148 ms
2025-07-18 11:52:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:52:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:52:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 successfully announced in 86.8741 ms
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc successfully announced in 87.7428 ms
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 all the dispatchers started
2025-07-18 11:52:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc all the dispatchers started
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc caught stopping signal...
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 caught stopping signal...
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 caught stopped signal...
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc caught stopped signal...
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 All dispatchers stopped
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 successfully reported itself as stopped in 2.2698 ms
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:e50771b4 has been stopped in total 531.6514 ms
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc All dispatchers stopped
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc successfully reported itself as stopped in 1.1911 ms
2025-07-18 11:52:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30684:432035bc has been stopped in total 572.4461 ms
2025-07-18 11:52:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:52:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:52:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:52:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 successfully announced in 85.8866 ms
2025-07-18 11:52:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d successfully announced in 87.3442 ms
2025-07-18 11:52:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:52:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d all the dispatchers started
2025-07-18 11:52:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 all the dispatchers started
2025-07-18 11:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d caught stopping signal...
2025-07-18 11:53:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 caught stopping signal...
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 All dispatchers stopped
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d All dispatchers stopped
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 successfully reported itself as stopped in 2.4292 ms
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:72a5deb4 has been stopped in total 159.4677 ms
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d successfully reported itself as stopped in 1.7722 ms
2025-07-18 11:53:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29120:6d3ccf1d has been stopped in total 160.0408 ms
2025-07-18 11:53:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:53:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:53:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 successfully announced in 80.6259 ms
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c successfully announced in 81.5221 ms
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c all the dispatchers started
2025-07-18 11:53:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 all the dispatchers started
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 caught stopping signal...
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c caught stopping signal...
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 All dispatchers stopped
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 successfully reported itself as stopped in 1.4974 ms
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a0a4c486 has been stopped in total 114.2329 ms
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c All dispatchers stopped
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c successfully reported itself as stopped in 1.3882 ms
2025-07-18 11:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27988:a1105d1c has been stopped in total 246.9974 ms
2025-07-18 11:54:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:54:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:54:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d successfully announced in 87.5637 ms
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 successfully announced in 87.5655 ms
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d all the dispatchers started
2025-07-18 11:54:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 all the dispatchers started
2025-07-18 11:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 caught stopping signal...
2025-07-18 11:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d caught stopping signal...
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d caught stopped signal...
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 caught stopped signal...
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d All dispatchers stopped
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d successfully reported itself as stopped in 2.8824 ms
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:a372e10d has been stopped in total 746.1398 ms
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 All dispatchers stopped
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 successfully reported itself as stopped in 0.8771 ms
2025-07-18 11:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27888:b4847214 has been stopped in total 758.6088 ms
2025-07-18 11:58:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 11:58:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 11:58:13 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 successfully announced in 84.4042 ms
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 successfully announced in 85.1645 ms
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 all the dispatchers started
2025-07-18 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 all the dispatchers started
2025-07-18 11:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 caught stopping signal...
2025-07-18 11:59:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 caught stopping signal...
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 All dispatchers stopped
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 All dispatchers stopped
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 successfully reported itself as stopped in 1.6422 ms
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:b57b3d44 has been stopped in total 425.159 ms
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 successfully reported itself as stopped in 1.0515 ms
2025-07-18 11:59:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34180:ceb685d9 has been stopped in total 425.473 ms
2025-07-18 13:16:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:16:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:16:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 successfully announced in 85.9585 ms
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d successfully announced in 87.372 ms
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 all the dispatchers started
2025-07-18 13:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d all the dispatchers started
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 caught stopping signal...
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d caught stopping signal...
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d All dispatchers stopped
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d successfully reported itself as stopped in 1.8759 ms
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:7269e14d has been stopped in total 59.0957 ms
2025-07-18 13:19:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 caught stopped signal...
2025-07-18 13:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 All dispatchers stopped
2025-07-18 13:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 successfully reported itself as stopped in 1.4432 ms
2025-07-18 13:19:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15536:e43e8500 has been stopped in total 928.6957 ms
2025-07-18 13:19:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:19:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:19:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a successfully announced in 79.375 ms
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f successfully announced in 79.6311 ms
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f all the dispatchers started
2025-07-18 13:19:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a all the dispatchers started
2025-07-18 13:21:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a caught stopping signal...
2025-07-18 13:21:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f caught stopping signal...
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f All dispatchers stopped
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f successfully reported itself as stopped in 2.5725 ms
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:b9ae694f has been stopped in total 185.7768 ms
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a All dispatchers stopped
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a successfully reported itself as stopped in 0.8405 ms
2025-07-18 13:21:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3520:f85f248a has been stopped in total 222.8779 ms
2025-07-18 13:22:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:22:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:22:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 successfully announced in 85.9401 ms
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 successfully announced in 86.1915 ms
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 all the dispatchers started
2025-07-18 13:22:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 all the dispatchers started
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 caught stopping signal...
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 caught stopping signal...
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 caught stopped signal...
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 caught stopped signal...
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 All dispatchers stopped
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 successfully reported itself as stopped in 1.5835 ms
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:4cddb1a0 has been stopped in total 584.02 ms
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 All dispatchers stopped
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 successfully reported itself as stopped in 0.798 ms
2025-07-18 13:23:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24504:28ce94f3 has been stopped in total 609.1086 ms
2025-07-18 13:24:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:24:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:24:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 successfully announced in 79.604 ms
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e successfully announced in 79.42 ms
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e all the dispatchers started
2025-07-18 13:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 all the dispatchers started
2025-07-18 13:25:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 caught stopping signal...
2025-07-18 13:25:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e caught stopping signal...
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e caught stopped signal...
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 caught stopped signal...
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e All dispatchers stopped
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 All dispatchers stopped
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e successfully reported itself as stopped in 1.8048 ms
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:e4ed3a8e has been stopped in total 612.7789 ms
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 successfully reported itself as stopped in 0.8691 ms
2025-07-18 13:25:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22944:1cc051c0 has been stopped in total 613.5417 ms
2025-07-18 13:25:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:25:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:25:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 successfully announced in 76.1858 ms
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 successfully announced in 86.8942 ms
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 all the dispatchers started
2025-07-18 13:25:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 all the dispatchers started
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 caught stopping signal...
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 caught stopping signal...
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 All dispatchers stopped
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 successfully reported itself as stopped in 1.6685 ms
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:98e35c03 has been stopped in total 355.2173 ms
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 All dispatchers stopped
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 successfully reported itself as stopped in 0.6657 ms
2025-07-18 13:28:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27408:c50c6c88 has been stopped in total 474.4693 ms
2025-07-18 13:28:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 13:28:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 13:28:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d successfully announced in 80.3214 ms
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e successfully announced in 81.3513 ms
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e all the dispatchers started
2025-07-18 13:28:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d all the dispatchers started
2025-07-18 13:33:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d caught stopping signal...
2025-07-18 13:33:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e caught stopping signal...
2025-07-18 13:33:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e All dispatchers stopped
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e successfully reported itself as stopped in 1.5114 ms
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:1d998d7e has been stopped in total 453.225 ms
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d caught stopped signal...
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d All dispatchers stopped
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d successfully reported itself as stopped in 0.6892 ms
2025-07-18 13:33:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23052:c9f1066d has been stopped in total 574.9472 ms
2025-07-18 14:31:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:31:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:31:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 successfully announced in 75.9616 ms
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df successfully announced in 76.3745 ms
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 all the dispatchers started
2025-07-18 14:31:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df all the dispatchers started
2025-07-18 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df caught stopping signal...
2025-07-18 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 caught stopping signal...
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 All dispatchers stopped
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df All dispatchers stopped
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df successfully reported itself as stopped in 2.2925 ms
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:6cf920df has been stopped in total 451.1167 ms
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 successfully reported itself as stopped in 2.0456 ms
2025-07-18 14:31:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14128:e3363027 has been stopped in total 450.5557 ms
2025-07-18 14:32:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:32:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:32:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:bfdb017c successfully announced in 73.61 ms
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:52a3dd0a successfully announced in 76.7589 ms
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:52a3dd0a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:bfdb017c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:bfdb017c all the dispatchers started
2025-07-18 14:32:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27788:52a3dd0a all the dispatchers started
2025-07-18 14:32:07 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this."
System.InvalidOperationException: Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this.
   at Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.OnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
2025-07-18 14:32:07 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this.
   at Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.OnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToPendingTasksWithErrorHandling(Task task, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderInExistingBatch(RenderQueueEntry renderQueueEntry)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessPendingRender()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.RenderHandle.Render(RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToPendingTasksWithErrorHandling(Task task, ComponentState owningComponentState)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-18 14:34:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:34:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:34:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 successfully announced in 70.9708 ms
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 successfully announced in 71.0859 ms
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 all the dispatchers started
2025-07-18 14:34:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 all the dispatchers started
2025-07-18 14:35:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 caught stopping signal...
2025-07-18 14:35:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 caught stopping signal...
2025-07-18 14:35:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 caught stopped signal...
2025-07-18 14:35:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 caught stopped signal...
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 All dispatchers stopped
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 All dispatchers stopped
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 successfully reported itself as stopped in 1.8627 ms
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:0f526ea1 has been stopped in total 710.4025 ms
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 successfully reported itself as stopped in 1.6132 ms
2025-07-18 14:35:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16216:44b46a02 has been stopped in total 710.9184 ms
2025-07-18 14:35:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:35:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:35:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 successfully announced in 71.0639 ms
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 successfully announced in 71.5959 ms
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 all the dispatchers started
2025-07-18 14:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 all the dispatchers started
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 caught stopping signal...
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 caught stopping signal...
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 caught stopped signal...
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 caught stopped signal...
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 All dispatchers stopped
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 All dispatchers stopped
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 successfully reported itself as stopped in 2.08 ms
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:f3bd3023 has been stopped in total 850.3462 ms
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 successfully reported itself as stopped in 2.5585 ms
2025-07-18 14:36:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:e4f79335 has been stopped in total 850.2176 ms
2025-07-18 14:36:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:36:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:36:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 successfully announced in 69.3894 ms
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 successfully announced in 72.3822 ms
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 all the dispatchers started
2025-07-18 14:36:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 all the dispatchers started
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 caught stopping signal...
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 caught stopping signal...
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 All dispatchers stopped
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 successfully reported itself as stopped in 3.5357 ms
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:9537f029 has been stopped in total 241.5812 ms
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 All dispatchers stopped
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 successfully reported itself as stopped in 1.6726 ms
2025-07-18 14:36:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20196:3216ee28 has been stopped in total 258.8109 ms
2025-07-18 14:36:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:36:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:36:53 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de successfully announced in 72.0301 ms
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b successfully announced in 72.0304 ms
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b all the dispatchers started
2025-07-18 14:36:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de all the dispatchers started
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b caught stopping signal...
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de caught stopping signal...
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b All dispatchers stopped
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de All dispatchers stopped
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de caught stopped signal...
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b successfully reported itself as stopped in 1.4824 ms
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:1ac7362b has been stopped in total 501.8417 ms
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de successfully reported itself as stopped in 12.1843 ms
2025-07-18 14:37:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19752:130ee3de has been stopped in total 513.2272 ms
2025-07-18 14:37:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:37:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:37:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:b6aabe76 successfully announced in 78.7803 ms
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:ea55599c successfully announced in 78.7807 ms
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:b6aabe76 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:ea55599c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:ea55599c all the dispatchers started
2025-07-18 14:37:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1892:b6aabe76 all the dispatchers started
2025-07-18 14:37:34 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this."
System.InvalidOperationException: Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this.
   at Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.OnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
2025-07-18 14:37:34 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this."
System.InvalidOperationException: Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this.
   at Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.OnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 14:37:34 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"nOTP7qYTCvdiCQXOehMUJRvsVTK73ESyLP8urOH3B14"'.
System.InvalidOperationException: Authorization requires a cascading parameter of type Task<AuthenticationState>. Consider using CascadingAuthenticationState to supply this.
   at Microsoft.AspNetCore.Components.Authorization.AuthorizeViewCore.OnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.ErrorBoundaryBase.Microsoft.AspNetCore.Components.IErrorBoundary.HandleException(Exception exception)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.HandleExceptionViaErrorBoundary(Exception error, ComponentState errorSourceOrNull)
2025-07-18 14:37:34 [Warning] Microsoft.AspNetCore.Components.Server.Circuits.RemoteRenderer: Unhandled exception rendering component: "Cannot access a disposed object."
System.ObjectDisposedException: Cannot access a disposed object.
   at Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.GrowBuffer(Int32 desiredCapacity)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderInExistingBatch(RenderQueueEntry renderQueueEntry)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-18 14:37:34 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Unhandled exception in circuit '"nOTP7qYTCvdiCQXOehMUJRvsVTK73ESyLP8urOH3B14"'.
System.ObjectDisposedException: Cannot access a disposed object.
   at Microsoft.AspNetCore.Components.RenderTree.ArrayBuilder`1.GrowBuffer(Int32 desiredCapacity)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderInExistingBatch(RenderQueueEntry renderQueueEntry)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
2025-07-18 14:42:33 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 14:44:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:44:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:44:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 successfully announced in 73.2975 ms
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 successfully announced in 65.8881 ms
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 all the dispatchers started
2025-07-18 14:44:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 all the dispatchers started
2025-07-18 14:45:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 caught stopping signal...
2025-07-18 14:45:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 caught stopping signal...
2025-07-18 14:45:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 caught stopped signal...
2025-07-18 14:45:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 caught stopped signal...
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 All dispatchers stopped
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 All dispatchers stopped
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 successfully reported itself as stopped in 2.187 ms
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:f1abdbd7 has been stopped in total 918.5251 ms
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 successfully reported itself as stopped in 0.8742 ms
2025-07-18 14:45:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27188:e9034012 has been stopped in total 918.9758 ms
2025-07-18 14:45:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:45:12 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:45:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c successfully announced in 74.8286 ms
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 successfully announced in 75.1031 ms
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c all the dispatchers started
2025-07-18 14:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 all the dispatchers started
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c caught stopping signal...
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 caught stopping signal...
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 caught stopped signal...
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c caught stopped signal...
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 All dispatchers stopped
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c All dispatchers stopped
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 successfully reported itself as stopped in 1.47 ms
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:5dc7ff90 has been stopped in total 690.8271 ms
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c successfully reported itself as stopped in 1.8627 ms
2025-07-18 14:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20384:37d47e5c has been stopped in total 691.5112 ms
2025-07-18 14:58:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-18 14:58:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-18 14:58:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a successfully announced in 71.6571 ms
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 successfully announced in 72.6585 ms
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 all the dispatchers started
2025-07-18 14:58:56 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-18 14:58:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a all the dispatchers started
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 caught stopping signal...
2025-07-18 15:17:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a caught stopping signal...
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a caught stopped signal...
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 caught stopped signal...
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a All dispatchers stopped
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 All dispatchers stopped
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 successfully reported itself as stopped in 7.2133 ms
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:f26a50e8 has been stopped in total 788.8655 ms
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a successfully reported itself as stopped in 8.2026 ms
2025-07-18 15:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29704:c91e640a has been stopped in total 788.8054 ms
