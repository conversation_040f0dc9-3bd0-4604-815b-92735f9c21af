2025-07-16 08:06:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:06:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:06:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 successfully announced in 117.0267 ms
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b successfully announced in 116.9142 ms
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:06:03 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b all the dispatchers started
2025-07-16 08:06:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 all the dispatchers started
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 caught stopping signal...
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b caught stopping signal...
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 All dispatchers stopped
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 successfully reported itself as stopped in 2.3266 ms
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:19c7a6a1 has been stopped in total 107.7344 ms
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b All dispatchers stopped
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b successfully reported itself as stopped in 0.983 ms
2025-07-16 08:11:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29040:1dbcdf2b has been stopped in total 299.421 ms
2025-07-16 08:11:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:11:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:11:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 successfully announced in 118.5088 ms
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a successfully announced in 116.6812 ms
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 all the dispatchers started
2025-07-16 08:11:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a all the dispatchers started
2025-07-16 08:14:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 caught stopping signal...
2025-07-16 08:14:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a caught stopping signal...
2025-07-16 08:14:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a caught stopped signal...
2025-07-16 08:14:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 caught stopped signal...
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 All dispatchers stopped
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 successfully reported itself as stopped in 2.0196 ms
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:9cba5421 has been stopped in total 880.6341 ms
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a All dispatchers stopped
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a successfully reported itself as stopped in 0.8797 ms
2025-07-16 08:14:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20436:31b2d19a has been stopped in total 884.5362 ms
2025-07-16 08:14:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:14:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:14:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d successfully announced in 108.5277 ms
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a successfully announced in 108.5757 ms
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a all the dispatchers started
2025-07-16 08:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d all the dispatchers started
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a caught stopping signal...
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d caught stopping signal...
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a All dispatchers stopped
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d All dispatchers stopped
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a successfully reported itself as stopped in 1.8756 ms
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d successfully reported itself as stopped in 1.8693 ms
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:6d32b48a has been stopped in total 298.1796 ms
2025-07-16 08:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8064:dab7343d has been stopped in total 298.0763 ms
2025-07-16 08:15:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:15:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:15:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 successfully announced in 128.8644 ms
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 successfully announced in 119.0478 ms
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 all the dispatchers started
2025-07-16 08:15:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 all the dispatchers started
2025-07-16 08:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 caught stopping signal...
2025-07-16 08:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 caught stopping signal...
2025-07-16 08:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 All dispatchers stopped
2025-07-16 08:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 successfully reported itself as stopped in 2.5347 ms
2025-07-16 08:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:757cb4c4 has been stopped in total 264.721 ms
2025-07-16 08:16:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 All dispatchers stopped
2025-07-16 08:16:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 successfully reported itself as stopped in 0.6382 ms
2025-07-16 08:16:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14332:e2d89d39 has been stopped in total 326.2522 ms
2025-07-16 08:16:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:16:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:16:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd successfully announced in 107.3724 ms
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e successfully announced in 107.3563 ms
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e all the dispatchers started
2025-07-16 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd all the dispatchers started
2025-07-16 08:38:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e caught stopping signal...
2025-07-16 08:38:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd caught stopping signal...
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e All dispatchers stopped
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e successfully reported itself as stopped in 1.8693 ms
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:5250964e has been stopped in total 421.4737 ms
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd All dispatchers stopped
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd successfully reported itself as stopped in 0.7851 ms
2025-07-16 08:38:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24384:43e54ebd has been stopped in total 574.2382 ms
2025-07-16 08:39:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:39:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:39:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee successfully announced in 110.6953 ms
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca successfully announced in 110.6931 ms
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca all the dispatchers started
2025-07-16 08:39:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee all the dispatchers started
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca caught stopping signal...
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee caught stopping signal...
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca All dispatchers stopped
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee All dispatchers stopped
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee successfully reported itself as stopped in 2.1482 ms
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:4b3b1bee has been stopped in total 238.6119 ms
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca successfully reported itself as stopped in 0.916 ms
2025-07-16 08:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32680:8bb46dca has been stopped in total 243.6657 ms
2025-07-16 08:40:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:40:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:40:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da successfully announced in 99.3713 ms
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 successfully announced in 99.3695 ms
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 all the dispatchers started
2025-07-16 08:40:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da all the dispatchers started
2025-07-16 08:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da caught stopping signal...
2025-07-16 08:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 caught stopping signal...
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da All dispatchers stopped
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 All dispatchers stopped
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 successfully reported itself as stopped in 2.4761 ms
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:a0272239 has been stopped in total 423.283 ms
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da successfully reported itself as stopped in 17.0244 ms
2025-07-16 08:41:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2372:e868c4da has been stopped in total 438.9305 ms
2025-07-16 08:41:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 08:41:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 08:41:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 successfully announced in 113.3983 ms
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 successfully announced in 110.0537 ms
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 all the dispatchers started
2025-07-16 08:41:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 all the dispatchers started
2025-07-16 09:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 caught stopping signal...
2025-07-16 09:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 caught stopping signal...
2025-07-16 09:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 All dispatchers stopped
2025-07-16 09:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 successfully reported itself as stopped in 8.1066 ms
2025-07-16 09:31:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:92f9fcd1 has been stopped in total 34.3973 ms
2025-07-16 09:31:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 caught stopped signal...
2025-07-16 09:31:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 All dispatchers stopped
2025-07-16 09:31:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 successfully reported itself as stopped in 1.0537 ms
2025-07-16 09:31:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23228:738469c2 has been stopped in total 926.2771 ms
2025-07-16 09:33:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:33:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:33:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 successfully announced in 114.3509 ms
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b successfully announced in 124.1335 ms
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 all the dispatchers started
2025-07-16 09:33:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b all the dispatchers started
2025-07-16 09:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b caught stopping signal...
2025-07-16 09:39:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 caught stopping signal...
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 All dispatchers stopped
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 successfully reported itself as stopped in 2.4479 ms
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:c60a8c85 has been stopped in total 489.6727 ms
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b caught stopped signal...
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b All dispatchers stopped
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b successfully reported itself as stopped in 0.5778 ms
2025-07-16 09:39:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3036:43cfea2b has been stopped in total 507.6367 ms
2025-07-16 09:39:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:39:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:39:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 successfully announced in 100.4614 ms
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e successfully announced in 103.8997 ms
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 all the dispatchers started
2025-07-16 09:39:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e all the dispatchers started
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e caught stopping signal...
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 caught stopping signal...
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e All dispatchers stopped
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e successfully reported itself as stopped in 2.1401 ms
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:0c416c8e has been stopped in total 317.7707 ms
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 All dispatchers stopped
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 successfully reported itself as stopped in 0.8452 ms
2025-07-16 09:41:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13384:a18e3a36 has been stopped in total 387.0532 ms
2025-07-16 09:41:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:41:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:41:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 successfully announced in 102.7279 ms
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 successfully announced in 102.7272 ms
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 all the dispatchers started
2025-07-16 09:41:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 all the dispatchers started
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 caught stopping signal...
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 caught stopping signal...
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 All dispatchers stopped
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 successfully reported itself as stopped in 1.8972 ms
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:5504e229 has been stopped in total 204.2911 ms
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 All dispatchers stopped
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 successfully reported itself as stopped in 0.7701 ms
2025-07-16 09:42:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31232:ca40a254 has been stopped in total 303.4529 ms
2025-07-16 09:42:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:42:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:42:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 successfully announced in 108.1547 ms
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee successfully announced in 108.1356 ms
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 all the dispatchers started
2025-07-16 09:42:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee all the dispatchers started
2025-07-16 09:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee caught stopping signal...
2025-07-16 09:44:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 caught stopping signal...
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 All dispatchers stopped
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 successfully reported itself as stopped in 2.5472 ms
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:62c5ebf0 has been stopped in total 463.1647 ms
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee All dispatchers stopped
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee successfully reported itself as stopped in 0.8885 ms
2025-07-16 09:44:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16236:14188cee has been stopped in total 471.6473 ms
2025-07-16 09:44:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:44:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:44:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 successfully announced in 142.352 ms
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 successfully announced in 104.6878 ms
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 all the dispatchers started
2025-07-16 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 all the dispatchers started
2025-07-16 09:45:42 [Error] HuaLingErpApp.Controller.PurchaseOrderLinesController: Failed to create purchase order line
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteReturnBigIdentityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteCommandIdentityIntoEntityAsync()
   at SqlSugar.InsertableProvider`1.ExecuteReturnEntityAsync()
   at HuaLingErpApp.Data.Repository`2.AddAndReturnAsync(TEntity entity) in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 40
   at HuaLingErpApp.Controller.PurchaseOrderLinesController.Create(PoItem model) in C:\HuaLingErpApp\HuaLingErpApp\Controller\PurchaseOrderLinesController.cs:line 72
ClientConnectionId:50452fd5-4947-4098-aaa9-2f6814a23a17
Error Number:8152,State:13,Class:16
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 caught stopping signal...
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 caught stopping signal...
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 All dispatchers stopped
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 successfully reported itself as stopped in 1.4923 ms
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:1c1a6d23 has been stopped in total 69.9148 ms
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 All dispatchers stopped
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 successfully reported itself as stopped in 0.9973 ms
2025-07-16 09:46:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18936:2709c8e5 has been stopped in total 159.0284 ms
2025-07-16 09:46:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:46:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:46:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 successfully announced in 102.9228 ms
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f successfully announced in 102.928 ms
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f all the dispatchers started
2025-07-16 09:46:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 all the dispatchers started
2025-07-16 09:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 caught stopping signal...
2025-07-16 09:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f caught stopping signal...
2025-07-16 09:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f caught stopped signal...
2025-07-16 09:47:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 caught stopped signal...
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 All dispatchers stopped
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 successfully reported itself as stopped in 1.7792 ms
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:f5e05b67 has been stopped in total 784.6024 ms
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f All dispatchers stopped
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f successfully reported itself as stopped in 0.7738 ms
2025-07-16 09:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25620:6a15595f has been stopped in total 989.2453 ms
2025-07-16 09:48:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:48:06 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:48:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 successfully announced in 118.4898 ms
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 successfully announced in 119.2534 ms
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 all the dispatchers started
2025-07-16 09:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 all the dispatchers started
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 caught stopping signal...
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 caught stopping signal...
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 All dispatchers stopped
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 successfully reported itself as stopped in 1.5601 ms
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:887537a8 has been stopped in total 317.9448 ms
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 All dispatchers stopped
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 successfully reported itself as stopped in 0.8698 ms
2025-07-16 09:48:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:60f64eb9 has been stopped in total 342.1511 ms
2025-07-16 09:48:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 09:48:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 09:48:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 successfully announced in 110.651 ms
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 successfully announced in 110.7118 ms
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 all the dispatchers started
2025-07-16 09:48:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 all the dispatchers started
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 caught stopping signal...
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 caught stopping signal...
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 All dispatchers stopped
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 successfully reported itself as stopped in 1.951 ms
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:2e5cdf33 has been stopped in total 233.9953 ms
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 All dispatchers stopped
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 successfully reported itself as stopped in 1.2358 ms
2025-07-16 10:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33324:6004da03 has been stopped in total 305.995 ms
2025-07-16 10:16:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 10:16:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:16:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 successfully announced in 130.1904 ms
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a successfully announced in 130.1794 ms
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 all the dispatchers started
2025-07-16 10:16:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a all the dispatchers started
2025-07-16 10:21:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 caught stopping signal...
2025-07-16 10:21:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a caught stopping signal...
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 All dispatchers stopped
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a All dispatchers stopped
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 caught stopped signal...
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a caught stopped signal...
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 successfully reported itself as stopped in 2.5783 ms
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:97493a16 has been stopped in total 613.0353 ms
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a successfully reported itself as stopped in 1.8173 ms
2025-07-16 10:21:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28908:52020e8a has been stopped in total 613.5985 ms
2025-07-16 10:21:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 10:21:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:21:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 successfully announced in 123.7377 ms
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c successfully announced in 113.3459 ms
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c all the dispatchers started
2025-07-16 10:21:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 all the dispatchers started
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 caught stopping signal...
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c caught stopping signal...
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c caught stopped signal...
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 caught stopped signal...
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 All dispatchers stopped
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 successfully reported itself as stopped in 2.0811 ms
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:51bbf878 has been stopped in total 808.8238 ms
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c All dispatchers stopped
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c successfully reported itself as stopped in 0.6642 ms
2025-07-16 10:23:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11860:e8c05b2c has been stopped in total 814.1608 ms
2025-07-16 10:23:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 10:23:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:23:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 successfully announced in 130.4062 ms
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed successfully announced in 130.5231 ms
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 all the dispatchers started
2025-07-16 10:23:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed all the dispatchers started
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed caught stopping signal...
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 caught stopping signal...
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 caught stopped signal...
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed caught stopped signal...
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed All dispatchers stopped
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed successfully reported itself as stopped in 1.4634 ms
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:32d4d5ed has been stopped in total 646.7604 ms
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 All dispatchers stopped
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 successfully reported itself as stopped in 0.6976 ms
2025-07-16 10:25:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1000:9efeb376 has been stopped in total 660.5356 ms
2025-07-16 10:25:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 10:25:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 10:25:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d successfully announced in 129.1359 ms
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf successfully announced in 129.1532 ms
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf all the dispatchers started
2025-07-16 10:25:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d all the dispatchers started
2025-07-16 11:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d caught stopping signal...
2025-07-16 11:38:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf caught stopping signal...
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf caught stopped signal...
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf All dispatchers stopped
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d caught stopped signal...
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf successfully reported itself as stopped in 34.7136 ms
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:c56e01bf has been stopped in total 559.2531 ms
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d All dispatchers stopped
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d successfully reported itself as stopped in 1.371 ms
2025-07-16 11:38:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31052:4e6ae85d has been stopped in total 689.2813 ms
2025-07-16 11:38:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:38:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:38:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 successfully announced in 112.9153 ms
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 successfully announced in 112.9172 ms
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 all the dispatchers started
2025-07-16 11:38:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 all the dispatchers started
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 caught stopping signal...
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 caught stopping signal...
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 All dispatchers stopped
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 successfully reported itself as stopped in 1.9631 ms
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:b4207ad3 has been stopped in total 99.6571 ms
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 All dispatchers stopped
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 successfully reported itself as stopped in 0.6935 ms
2025-07-16 11:40:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25276:0a295158 has been stopped in total 104.4515 ms
2025-07-16 11:40:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:40:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:40:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 successfully announced in 116.7703 ms
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d successfully announced in 117.4704 ms
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 all the dispatchers started
2025-07-16 11:40:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d all the dispatchers started
2025-07-16 11:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d caught stopping signal...
2025-07-16 11:40:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 caught stopping signal...
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d All dispatchers stopped
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 All dispatchers stopped
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 successfully reported itself as stopped in 2.7688 ms
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d successfully reported itself as stopped in 3.0407 ms
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:6455de17 has been stopped in total 470.9449 ms
2025-07-16 11:40:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21316:acc0675d has been stopped in total 471.212 ms
2025-07-16 11:41:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:41:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:41:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 successfully announced in 138.8031 ms
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 successfully announced in 138.6638 ms
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 all the dispatchers started
2025-07-16 11:41:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 all the dispatchers started
2025-07-16 11:43:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 caught stopping signal...
2025-07-16 11:43:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 caught stopping signal...
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 caught stopped signal...
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 caught stopped signal...
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 All dispatchers stopped
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 successfully reported itself as stopped in 1.8924 ms
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:8aa5b3f1 has been stopped in total 531.5378 ms
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 All dispatchers stopped
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 successfully reported itself as stopped in 0.8427 ms
2025-07-16 11:43:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21696:3db92fa8 has been stopped in total 727.0591 ms
2025-07-16 11:44:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:44:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:44:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce successfully announced in 124.8417 ms
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b successfully announced in 124.8131 ms
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b all the dispatchers started
2025-07-16 11:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce all the dispatchers started
2025-07-16 11:46:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce caught stopping signal...
2025-07-16 11:46:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b caught stopping signal...
2025-07-16 11:46:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b caught stopped signal...
2025-07-16 11:46:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce caught stopped signal...
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce All dispatchers stopped
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce successfully reported itself as stopped in 1.6535 ms
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:d76f84ce has been stopped in total 632.3286 ms
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b All dispatchers stopped
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b successfully reported itself as stopped in 1.3413 ms
2025-07-16 11:46:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32648:ebe7df0b has been stopped in total 730.1361 ms
2025-07-16 11:46:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:46:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:46:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 successfully announced in 115.2801 ms
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee successfully announced in 115.6176 ms
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee all the dispatchers started
2025-07-16 11:46:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 all the dispatchers started
2025-07-16 11:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 caught stopping signal...
2025-07-16 11:47:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee caught stopping signal...
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee caught stopped signal...
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 caught stopped signal...
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee All dispatchers stopped
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee successfully reported itself as stopped in 2.6937 ms
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:b0bc6eee has been stopped in total 524.002 ms
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 All dispatchers stopped
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 successfully reported itself as stopped in 0.7661 ms
2025-07-16 11:47:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23652:4743e216 has been stopped in total 530.4855 ms
2025-07-16 11:47:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:47:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:47:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 successfully announced in 110.1904 ms
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 successfully announced in 110.1999 ms
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 all the dispatchers started
2025-07-16 11:47:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 all the dispatchers started
2025-07-16 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 caught stopping signal...
2025-07-16 11:50:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 caught stopping signal...
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 caught stopped signal...
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 caught stopped signal...
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 All dispatchers stopped
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 successfully reported itself as stopped in 2.0434 ms
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:a92eb0b0 has been stopped in total 975.1348 ms
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 All dispatchers stopped
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 successfully reported itself as stopped in 0.6763 ms
2025-07-16 11:50:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16324:7e5c58b3 has been stopped in total 991.3001 ms
2025-07-16 11:50:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:50:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:50:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 successfully announced in 124.0455 ms
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 successfully announced in 124.055 ms
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 all the dispatchers started
2025-07-16 11:50:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 all the dispatchers started
2025-07-16 11:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 caught stopping signal...
2025-07-16 11:57:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 caught stopping signal...
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 caught stopped signal...
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 caught stopped signal...
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 All dispatchers stopped
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 successfully reported itself as stopped in 2.0522 ms
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:6e3372f0 has been stopped in total 678.3361 ms
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 All dispatchers stopped
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 successfully reported itself as stopped in 0.7529 ms
2025-07-16 11:57:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31688:f1e7b124 has been stopped in total 711.206 ms
2025-07-16 11:57:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 11:57:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 11:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 successfully announced in 118.7496 ms
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b successfully announced in 118.7155 ms
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b all the dispatchers started
2025-07-16 11:57:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 all the dispatchers started
2025-07-16 13:02:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b caught stopping signal...
2025-07-16 13:02:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 caught stopping signal...
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 caught stopped signal...
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b caught stopped signal...
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b All dispatchers stopped
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b successfully reported itself as stopped in 15.4709 ms
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:fd02b53b has been stopped in total 573.224 ms
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 All dispatchers stopped
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 successfully reported itself as stopped in 1.5286 ms
2025-07-16 13:02:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26364:9a4fd5e6 has been stopped in total 998.2705 ms
2025-07-16 13:02:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 13:02:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 13:02:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 successfully announced in 112.1323 ms
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 successfully announced in 113.214 ms
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 all the dispatchers started
2025-07-16 13:02:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 all the dispatchers started
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 caught stopping signal...
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 caught stopping signal...
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 All dispatchers stopped
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 All dispatchers stopped
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 successfully reported itself as stopped in 40.9651 ms
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 successfully reported itself as stopped in 19.7981 ms
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:321f8000 has been stopped in total 181.8494 ms
2025-07-16 13:56:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26632:1b116394 has been stopped in total 181.2932 ms
2025-07-16 13:56:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 13:56:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 13:56:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b successfully announced in 127.1944 ms
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 successfully announced in 127.1936 ms
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b all the dispatchers started
2025-07-16 13:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 all the dispatchers started
2025-07-16 14:06:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 caught stopping signal...
2025-07-16 14:06:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b caught stopping signal...
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b caught stopped signal...
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 caught stopped signal...
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b All dispatchers stopped
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b successfully reported itself as stopped in 3.7629 ms
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:e47bcd1b has been stopped in total 591.3407 ms
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 All dispatchers stopped
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 successfully reported itself as stopped in 1.2497 ms
2025-07-16 14:06:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25876:4299f665 has been stopped in total 738.2651 ms
2025-07-16 14:07:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 14:07:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:07:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 successfully announced in 119.1691 ms
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 successfully announced in 119.9466 ms
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 all the dispatchers started
2025-07-16 14:07:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 all the dispatchers started
2025-07-16 14:47:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 caught stopping signal...
2025-07-16 14:47:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 caught stopping signal...
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 caught stopped signal...
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 caught stopped signal...
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 All dispatchers stopped
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 All dispatchers stopped
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 successfully reported itself as stopped in 4.2257 ms
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:13ec59a3 has been stopped in total 694.7916 ms
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 successfully reported itself as stopped in 4.3279 ms
2025-07-16 14:47:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28964:4bf988c1 has been stopped in total 695.2071 ms
2025-07-16 14:47:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 14:47:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:47:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea successfully announced in 118.3861 ms
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 successfully announced in 118.6503 ms
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea all the dispatchers started
2025-07-16 14:47:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 all the dispatchers started
2025-07-16 14:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea caught stopping signal...
2025-07-16 14:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 caught stopping signal...
2025-07-16 14:48:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 caught stopped signal...
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea caught stopped signal...
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea All dispatchers stopped
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 All dispatchers stopped
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 successfully reported itself as stopped in 1.9305 ms
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea successfully reported itself as stopped in 2.4365 ms
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:85e14458 has been stopped in total 717.5759 ms
2025-07-16 14:48:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1072:d84300ea has been stopped in total 717.8049 ms
2025-07-16 14:48:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 14:48:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:48:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 successfully announced in 118.1531 ms
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 successfully announced in 118.666 ms
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 all the dispatchers started
2025-07-16 14:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 all the dispatchers started
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 caught stopping signal...
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 caught stopping signal...
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 All dispatchers stopped
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 All dispatchers stopped
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 successfully reported itself as stopped in 2.2577 ms
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:2d1bd309 has been stopped in total 196.486 ms
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 successfully reported itself as stopped in 0.9793 ms
2025-07-16 14:49:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20504:36b02849 has been stopped in total 196.3911 ms
2025-07-16 14:49:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 14:49:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 14:49:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 successfully announced in 126.0505 ms
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 successfully announced in 126.049 ms
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 all the dispatchers started
2025-07-16 14:49:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 all the dispatchers started
2025-07-16 14:49:45 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 115
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 14:50:01 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 115
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 14:50:08 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 115
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 14:50:58 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 115
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 caught stopping signal...
2025-07-16 15:06:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 caught stopping signal...
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 All dispatchers stopped
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 successfully reported itself as stopped in 5.7587 ms
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:b943c738 has been stopped in total 231.7635 ms
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 All dispatchers stopped
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 successfully reported itself as stopped in 2.2196 ms
2025-07-16 15:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29532:8e782af1 has been stopped in total 465.4129 ms
2025-07-16 15:06:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:06:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:06:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 successfully announced in 116.6567 ms
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae successfully announced in 116.6556 ms
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae all the dispatchers started
2025-07-16 15:06:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 all the dispatchers started
2025-07-16 15:06:29 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:48cc1b73-4c6e-4825-8087-0dd9241594cf
Error Number:207,State:1,Class:16
2025-07-16 15:06:29 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:48cc1b73-4c6e-4825-8087-0dd9241594cf
Error Number:207,State:1,Class:16
2025-07-16 15:06:31 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:48cc1b73-4c6e-4825-8087-0dd9241594cf
Error Number:207,State:1,Class:16
2025-07-16 15:06:31 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:48cc1b73-4c6e-4825-8087-0dd9241594cf
Error Number:207,State:1,Class:16
2025-07-16 15:06:48 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:31cd0ea8-609d-4435-906b-9788b4c4de3b
Error Number:207,State:1,Class:16
2025-07-16 15:06:57 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__195_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:0654db3e-cdcc-4c54-9cf4-aca5c4ec3d3c
Error Number:207,State:1,Class:16
2025-07-16 15:07:42 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:31cd0ea8-609d-4435-906b-9788b4c4de3b
Error Number:207,State:1,Class:16
2025-07-16 15:07:45 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:0654db3e-cdcc-4c54-9cf4-aca5c4ec3d3c
Error Number:207,State:1,Class:16
2025-07-16 15:07:48 [Error] HuaLingErpApp.Controller.UMController: Failed to retrieve units of measure list
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'RecordDate' 无效。
列名 'CreatedDate' 无效。
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at HuaLingErpApp.Data.Repository`2.GetDataAsync() in C:\HuaLingErpApp\HuaLingErpApp\Data\Repository.cs:line 28
   at HuaLingErpApp.Controller.UMController.GetAll() in C:\HuaLingErpApp\HuaLingErpApp\Controller\UMController.cs:line 28
ClientConnectionId:0654db3e-cdcc-4c54-9cf4-aca5c4ec3d3c
Error Number:207,State:1,Class:16
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae caught stopping signal...
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 caught stopping signal...
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 All dispatchers stopped
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 successfully reported itself as stopped in 2.0115 ms
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:5d2ba295 has been stopped in total 99.6425 ms
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae All dispatchers stopped
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae successfully reported itself as stopped in 0.8397 ms
2025-07-16 15:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27940:49a65cae has been stopped in total 105.045 ms
2025-07-16 15:09:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:09:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:09:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 successfully announced in 118.1054 ms
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc successfully announced in 118.0952 ms
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc all the dispatchers started
2025-07-16 15:09:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 all the dispatchers started
2025-07-16 15:20:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc caught stopping signal...
2025-07-16 15:20:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 caught stopping signal...
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 All dispatchers stopped
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 successfully reported itself as stopped in 4.0135 ms
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:247c74f3 has been stopped in total 332.8962 ms
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc caught stopped signal...
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc All dispatchers stopped
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc successfully reported itself as stopped in 1.7975 ms
2025-07-16 15:20:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32800:4a49abdc has been stopped in total 687.9648 ms
2025-07-16 15:21:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:21:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:21:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 successfully announced in 117.4778 ms
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee successfully announced in 117.3613 ms
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 all the dispatchers started
2025-07-16 15:21:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee all the dispatchers started
2025-07-16 15:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 caught stopping signal...
2025-07-16 15:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee caught stopping signal...
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee caught stopped signal...
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 caught stopped signal...
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 All dispatchers stopped
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 successfully reported itself as stopped in 1.8712 ms
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:4eeedb19 has been stopped in total 674.2258 ms
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee All dispatchers stopped
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee successfully reported itself as stopped in 0.6844 ms
2025-07-16 15:24:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19212:b3eddfee has been stopped in total 679.6457 ms
2025-07-16 15:24:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:24:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:24:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d successfully announced in 138.0142 ms
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 successfully announced in 138.529 ms
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 all the dispatchers started
2025-07-16 15:24:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d all the dispatchers started
2025-07-16 15:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 caught stopping signal...
2025-07-16 15:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d caught stopping signal...
2025-07-16 15:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d caught stopped signal...
2025-07-16 15:25:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 caught stopped signal...
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d All dispatchers stopped
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d successfully reported itself as stopped in 1.6847 ms
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:fb8aff7d has been stopped in total 719.937 ms
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 All dispatchers stopped
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 successfully reported itself as stopped in 0.6668 ms
2025-07-16 15:25:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4900:0e09b141 has been stopped in total 733.2663 ms
2025-07-16 15:26:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:26:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:26:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b successfully announced in 144.11 ms
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 successfully announced in 144.1283 ms
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 all the dispatchers started
2025-07-16 15:26:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b all the dispatchers started
2025-07-16 15:26:44 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 135
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 caught stopping signal...
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b caught stopping signal...
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 All dispatchers stopped
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 successfully reported itself as stopped in 1.8994 ms
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:98985e89 has been stopped in total 225.5149 ms
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b All dispatchers stopped
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b successfully reported itself as stopped in 1.8129 ms
2025-07-16 15:29:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24520:19b1201b has been stopped in total 321.899 ms
2025-07-16 15:29:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:29:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:29:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 successfully announced in 116.2771 ms
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff successfully announced in 116.2785 ms
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 all the dispatchers started
2025-07-16 15:29:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff all the dispatchers started
2025-07-16 15:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 caught stopping signal...
2025-07-16 15:30:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff caught stopping signal...
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff caught stopped signal...
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 caught stopped signal...
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff All dispatchers stopped
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 All dispatchers stopped
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 successfully reported itself as stopped in 0.8859 ms
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:08f9c1c7 has been stopped in total 608.7873 ms
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff successfully reported itself as stopped in 2.4973 ms
2025-07-16 15:30:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26516:17cb62ff has been stopped in total 608.6733 ms
2025-07-16 15:30:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:30:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:30:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 successfully announced in 114.8829 ms
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 successfully announced in 86.2999 ms
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 all the dispatchers started
2025-07-16 15:30:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 all the dispatchers started
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 caught stopping signal...
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 caught stopping signal...
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 caught stopped signal...
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 caught stopped signal...
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 All dispatchers stopped
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 All dispatchers stopped
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 successfully reported itself as stopped in 2.5461 ms
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 successfully reported itself as stopped in 2.5127 ms
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:59eb95c6 has been stopped in total 514.7368 ms
2025-07-16 15:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19864:232bcc26 has been stopped in total 515.0079 ms
2025-07-16 15:31:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:31:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:31:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 successfully announced in 116.032 ms
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 successfully announced in 115.7711 ms
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 all the dispatchers started
2025-07-16 15:31:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 all the dispatchers started
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 caught stopping signal...
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 caught stopping signal...
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 caught stopped signal...
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 caught stopped signal...
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 All dispatchers stopped
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 successfully reported itself as stopped in 2.0566 ms
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:0d2ce557 has been stopped in total 672.831 ms
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 All dispatchers stopped
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 successfully reported itself as stopped in 1.1373 ms
2025-07-16 15:32:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14988:f765ef67 has been stopped in total 732.1935 ms
2025-07-16 15:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:32:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:32:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 successfully announced in 115.1548 ms
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 successfully announced in 116.6567 ms
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 all the dispatchers started
2025-07-16 15:32:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 all the dispatchers started
2025-07-16 15:33:17 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:23 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:28 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:30 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:34 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:37 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:40 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:33:44 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Sequence contains no matching element"
System.InvalidOperationException: Sequence contains no matching element
   at System.Linq.ThrowHelper.ThrowNoMatchException()
   at System.Linq.Enumerable.First[TSource](IEnumerable`1 source, Func`2 predicate)
   at HuaLingErpApp.Client.Pages.Procurement.Components.PurchaseOrderLinesEditTemplate.OnItemChanged(Item item) in C:\HuaLingErpApp\HuaLingErpApp.Client\Pages\Procurement\Components\PurchaseOrderLinesEditTemplate.razor:line 133
   at BootstrapBlazor.Components.AutoFill`1.OnClickItem(TValue val)
   at BootstrapBlazor.Components.BootstrapComponentBase.CallStateHasChangedOnAsyncCompletion(Task task)
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 caught stopping signal...
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 caught stopping signal...
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 All dispatchers stopped
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 successfully reported itself as stopped in 1.7132 ms
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:9f0e8855 has been stopped in total 78.9591 ms
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 All dispatchers stopped
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 successfully reported itself as stopped in 0.6877 ms
2025-07-16 15:35:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32764:7248d091 has been stopped in total 79.9982 ms
2025-07-16 15:36:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:36:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:36:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:36:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:36:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:36:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:36:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:36:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b successfully announced in 112.8743 ms
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 successfully announced in 119.7773 ms
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 all the dispatchers started
2025-07-16 15:36:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b all the dispatchers started
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 caught stopping signal...
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b caught stopping signal...
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 All dispatchers stopped
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b All dispatchers stopped
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 successfully reported itself as stopped in 1.9477 ms
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:cd77ce92 has been stopped in total 290.8417 ms
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b successfully reported itself as stopped in 4.1974 ms
2025-07-16 15:40:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29380:de6f841b has been stopped in total 294.0503 ms
2025-07-16 15:40:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:40:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:40:25 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc successfully announced in 119.1471 ms
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca successfully announced in 119.0288 ms
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca all the dispatchers started
2025-07-16 15:40:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc all the dispatchers started
2025-07-16 15:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc caught stopping signal...
2025-07-16 15:41:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca caught stopping signal...
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca All dispatchers stopped
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc All dispatchers stopped
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca successfully reported itself as stopped in 2.8326 ms
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:238dffca has been stopped in total 339.7175 ms
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc successfully reported itself as stopped in 13.7983 ms
2025-07-16 15:41:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17536:0e526bcc has been stopped in total 350.8132 ms
2025-07-16 15:41:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:41:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:41:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b successfully announced in 121.3887 ms
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 successfully announced in 121.3712 ms
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b all the dispatchers started
2025-07-16 15:41:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 all the dispatchers started
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 caught stopping signal...
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b caught stopping signal...
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b caught stopped signal...
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 caught stopped signal...
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 All dispatchers stopped
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 successfully reported itself as stopped in 1.6411 ms
2025-07-16 15:43:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:b0426a26 has been stopped in total 882.0092 ms
2025-07-16 15:43:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b All dispatchers stopped
2025-07-16 15:43:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b successfully reported itself as stopped in 0.747 ms
2025-07-16 15:43:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9792:f704696b has been stopped in total 957.9935 ms
2025-07-16 15:43:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:43:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:43:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc successfully announced in 114.4473 ms
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a successfully announced in 85.6624 ms
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a all the dispatchers started
2025-07-16 15:43:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc all the dispatchers started
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc caught stopping signal...
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a caught stopping signal...
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc caught stopped signal...
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a caught stopped signal...
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc All dispatchers stopped
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc successfully reported itself as stopped in 1.8961 ms
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:142621cc has been stopped in total 750.5363 ms
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a All dispatchers stopped
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a successfully reported itself as stopped in 0.902 ms
2025-07-16 15:53:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26936:aad63c3a has been stopped in total 776.7061 ms
2025-07-16 15:53:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:53:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:53:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af successfully announced in 118.6279 ms
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 successfully announced in 119.7414 ms
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 all the dispatchers started
2025-07-16 15:53:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af all the dispatchers started
2025-07-16 15:58:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 caught stopping signal...
2025-07-16 15:58:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af caught stopping signal...
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af caught stopped signal...
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 caught stopped signal...
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 All dispatchers stopped
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 successfully reported itself as stopped in 2.6164 ms
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:b479c380 has been stopped in total 585.6926 ms
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af All dispatchers stopped
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af successfully reported itself as stopped in 1.6125 ms
2025-07-16 15:58:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20732:4a6650af has been stopped in total 630.4753 ms
2025-07-16 15:58:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-16 15:58:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-16 15:58:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b successfully announced in 114.4509 ms
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 successfully announced in 114.5422 ms
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b all the dispatchers started
2025-07-16 15:58:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 all the dispatchers started
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b caught stopping signal...
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 caught stopping signal...
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 caught stopped signal...
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b caught stopped signal...
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b All dispatchers stopped
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 All dispatchers stopped
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b successfully reported itself as stopped in 26.7711 ms
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:44c0290b has been stopped in total 720.0879 ms
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 successfully reported itself as stopped in 6.8407 ms
2025-07-16 16:59:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29488:f78019b1 has been stopped in total 719.1774 ms
