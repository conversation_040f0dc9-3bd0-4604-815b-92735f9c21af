2025-07-21 13:52:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:52:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 13:52:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:52:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 successfully announced in 60.2184 ms
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 successfully announced in 59.9861 ms
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 all the dispatchers started
2025-07-21 13:52:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 all the dispatchers started
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 caught stopping signal...
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 caught stopping signal...
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 All dispatchers stopped
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 All dispatchers stopped
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 successfully reported itself as stopped in 2.157 ms
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 successfully reported itself as stopped in 1.795 ms
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:014ae906 has been stopped in total 300.5993 ms
2025-07-21 13:52:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:948:43879958 has been stopped in total 300.4033 ms
2025-07-21 13:53:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:53:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 13:53:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:53:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f successfully announced in 63.7186 ms
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da successfully announced in 64.8361 ms
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f all the dispatchers started
2025-07-21 13:53:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da all the dispatchers started
2025-07-21 13:53:03 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Cannot provide a value for property 'CustomAuthStateProvider' on type 'HuaLingErpApp.Client.Layout.MainLayout'. There is no registered service of type 'HuaLingErpApp.Client.Services.CustomAuthStateProvider'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f caught stopping signal...
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da caught stopping signal...
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f All dispatchers stopped
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da All dispatchers stopped
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da successfully reported itself as stopped in 1.7631 ms
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f successfully reported itself as stopped in 1.7653 ms
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:f59038da has been stopped in total 339.7431 ms
2025-07-21 13:54:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27816:3ecf469f has been stopped in total 339.9443 ms
2025-07-21 13:54:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:54:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:54:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:54:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 13:54:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:54:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c successfully announced in 62.4608 ms
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 successfully announced in 67.8216 ms
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 all the dispatchers started
2025-07-21 13:54:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c all the dispatchers started
2025-07-21 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 caught stopping signal...
2025-07-21 13:55:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c caught stopping signal...
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c caught stopped signal...
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 caught stopped signal...
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 All dispatchers stopped
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 successfully reported itself as stopped in 2.3629 ms
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:c6fee777 has been stopped in total 824.6413 ms
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c All dispatchers stopped
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c successfully reported itself as stopped in 1.6821 ms
2025-07-21 13:55:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34500:42466e7c has been stopped in total 828.316 ms
2025-07-21 13:58:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 13:58:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 13:58:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 13:58:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 successfully announced in 63.4108 ms
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 successfully announced in 63.7329 ms
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 all the dispatchers started
2025-07-21 13:58:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 all the dispatchers started
2025-07-21 13:58:48 [Error] Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddleware: An unhandled exception has occurred while executing the request.
System.InvalidOperationException: Cannot provide a value for property 'AuthStateProvider' on type 'HuaLingErpApp.Client.Pages.Auth.Login'. There is no registered service of type 'HuaLingErpApp.Client.Services.CustomAuthStateProvider'.
   at Microsoft.AspNetCore.Components.ComponentFactory.<>c__DisplayClass9_0.<CreatePropertyInjector>g__Initialize|1(IServiceProvider serviceProvider, IComponent component)
   at Microsoft.AspNetCore.Components.ComponentFactory.InstantiateComponent(IServiceProvider serviceProvider, Type componentType, IComponentRenderMode callerSpecifiedRenderMode, Nullable`1 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.InstantiateChildComponentOnFrame(RenderTreeFrame[] frames, Int32 frameIndex, Int32 parentComponentId)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewComponentFrame(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InitializeNewSubtree(DiffContext& diffContext, Int32 frameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.InsertNewFrame(DiffContext& diffContext, Int32 newFrameIndex)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.AppendDiffEntriesForRange(DiffContext& diffContext, Int32 oldStartIndex, Int32 oldEndIndexExcl, Int32 newStartIndex, Int32 newEndIndexExcl)
   at Microsoft.AspNetCore.Components.RenderTree.RenderTreeDiffBuilder.ComputeDiff(Renderer renderer, RenderBatchBuilder batchBuilder, Int32 componentId, ArrayRange`1 oldTree, ArrayRange`1 newTree)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.RenderIntoBatch(RenderBatchBuilder batchBuilder, RenderFragment renderFragment, Exception& renderFragmentException)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.ProcessRenderQueue()
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.AddToRenderQueue(Int32 componentId, RenderFragment renderFragment)
   at Microsoft.AspNetCore.Components.ComponentBase.StateHasChanged()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.RunInitAndSetParametersAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SetDirectParameters(ParameterView parameters)
   at Microsoft.AspNetCore.Components.RenderTree.Renderer.RenderRootComponentAsync(Int32 componentId, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.HtmlRendering.Infrastructure.StaticHtmlRenderer.BeginRenderingComponent(IComponent component, ParameterView initialParameters)
   at Microsoft.AspNetCore.Components.Endpoints.EndpointHtmlRenderer.RenderEndpointComponent(HttpContext httpContext, Type rootComponentType, ParameterView parameters, Boolean waitForQuiescence)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Endpoints.RazorComponentEndpointInvoker.RenderComponentCore(HttpContext context)
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<>c.<<InvokeAsync>b__10_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Builder.ServerRazorComponentsEndpointConventionBuilderExtensions.<>c__DisplayClass1_1.<<AddInteractiveServerRenderMode>b__1>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-07-21 14:01:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 caught stopping signal...
2025-07-21 14:01:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 caught stopping signal...
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 caught stopped signal...
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 caught stopped signal...
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 All dispatchers stopped
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 All dispatchers stopped
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 successfully reported itself as stopped in 1.8034 ms
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 successfully reported itself as stopped in 1.7869 ms
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:e5948a05 has been stopped in total 849.1679 ms
2025-07-21 14:01:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33144:8a607229 has been stopped in total 849.417 ms
2025-07-21 14:01:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:01:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:01:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:01:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 successfully announced in 66.711 ms
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 successfully announced in 72.5562 ms
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 all the dispatchers started
2025-07-21 14:01:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 all the dispatchers started
2025-07-21 14:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 caught stopping signal...
2025-07-21 14:08:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 caught stopping signal...
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 caught stopped signal...
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 caught stopped signal...
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 All dispatchers stopped
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 successfully reported itself as stopped in 8.0117 ms
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:23511b98 has been stopped in total 751.5475 ms
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 All dispatchers stopped
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 successfully reported itself as stopped in 0.6357 ms
2025-07-21 14:08:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30072:ed37bba9 has been stopped in total 843.917 ms
2025-07-21 14:08:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:08:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:08:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:08:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e successfully announced in 69.5239 ms
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 successfully announced in 69.3674 ms
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 all the dispatchers started
2025-07-21 14:08:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e all the dispatchers started
2025-07-21 14:12:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e caught stopping signal...
2025-07-21 14:12:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 caught stopping signal...
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e All dispatchers stopped
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e successfully reported itself as stopped in 3.517 ms
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:b6e3ad1e has been stopped in total 103.637 ms
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 All dispatchers stopped
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 successfully reported itself as stopped in 1.0794 ms
2025-07-21 14:12:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18052:52ecfb09 has been stopped in total 105.9625 ms
2025-07-21 14:13:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:13:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:13:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:13:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 successfully announced in 61.8148 ms
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 successfully announced in 62.2395 ms
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 all the dispatchers started
2025-07-21 14:13:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 all the dispatchers started
2025-07-21 14:14:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 caught stopping signal...
2025-07-21 14:14:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 caught stopping signal...
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 caught stopped signal...
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 caught stopped signal...
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 All dispatchers stopped
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 All dispatchers stopped
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 successfully reported itself as stopped in 1.6748 ms
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:3d75be17 has been stopped in total 777.2348 ms
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 successfully reported itself as stopped in 2.1775 ms
2025-07-21 14:14:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:02736506 has been stopped in total 777.3484 ms
2025-07-21 14:14:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:14:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:14:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 successfully announced in 56.6463 ms
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:14:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc successfully announced in 1.8382 ms
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 all the dispatchers started
2025-07-21 14:14:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc all the dispatchers started
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 caught stopping signal...
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc caught stopping signal...
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 All dispatchers stopped
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 successfully reported itself as stopped in 1.5348 ms
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:0bd38988 has been stopped in total 135.2962 ms
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc All dispatchers stopped
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc successfully reported itself as stopped in 0.9878 ms
2025-07-21 14:16:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:3220:24b6d1bc has been stopped in total 137.3221 ms
2025-07-21 14:17:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:17:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:17:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:17:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d successfully announced in 64.2836 ms
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c successfully announced in 64.0451 ms
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d all the dispatchers started
2025-07-21 14:17:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c all the dispatchers started
2025-07-21 14:17:08 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at BootstrapBlazor.Components.Tab.<>c__DisplayClass194_0.<AddTabByUrl>b__2(TabItem tab)
   at System.Linq.Enumerable.TryGetFirst[TSource](IEnumerable`1 source, Func`2 predicate, Boolean& found)
   at System.Linq.Enumerable.FirstOrDefault[TSource](IEnumerable`1 source, Func`2 predicate)
   at BootstrapBlazor.Components.Tab.AddTabByUrl()
   at BootstrapBlazor.Components.Tab.OnParametersSet()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
2025-07-21 14:17:08 [Error] Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost: Location change to '"http://localhost:32148/#"' in circuit '"7ePpg13ZKMmupvWdd55nSd5lK1FLF8sN3a5k6ar5xzM"' failed.
Microsoft.AspNetCore.Components.LocationChangeException: An exception occurred while dispatching a location changed event.
 ---> System.NullReferenceException: Object reference not set to an instance of an object.
   at BootstrapBlazor.Components.Tab.<>c__DisplayClass194_0.<AddTabByUrl>b__2(TabItem tab)
   at System.Linq.Enumerable.TryGetFirst[TSource](IEnumerable`1 source, Func`2 predicate, Boolean& found)
   at System.Linq.Enumerable.FirstOrDefault[TSource](IEnumerable`1 source, Func`2 predicate)
   at BootstrapBlazor.Components.Tab.AddTabByUrl()
   at BootstrapBlazor.Components.Tab.Navigator_LocationChanged(Object sender, LocationChangedEventArgs e)
   at Microsoft.AspNetCore.Components.NavigationManager.NotifyLocationChanged(Boolean isInterceptedLink)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Components.NavigationManager.NotifyLocationChanged(Boolean isInterceptedLink)
   at Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost.<>c__DisplayClass51_0.<OnLocationChangedAsync>b__1()
   at Microsoft.AspNetCore.Components.Rendering.RendererSynchronizationContext.<InvokeAsync>g__Execute|8_0(ValueTuple`3 state)
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Components.Server.Circuits.CircuitHost.OnLocationChangedAsync(String uri, String state, Boolean intercepted)
2025-07-21 14:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c caught stopping signal...
2025-07-21 14:19:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d caught stopping signal...
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c caught stopped signal...
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d caught stopped signal...
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c All dispatchers stopped
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d All dispatchers stopped
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c successfully reported itself as stopped in 1.5817 ms
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:ae1c663c has been stopped in total 746.8078 ms
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d successfully reported itself as stopped in 0.6606 ms
2025-07-21 14:19:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25504:0913611d has been stopped in total 747.0822 ms
2025-07-21 14:19:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:19:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:19:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:19:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 successfully announced in 65.0314 ms
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 successfully announced in 64.3639 ms
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 all the dispatchers started
2025-07-21 14:19:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 all the dispatchers started
2025-07-21 14:23:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 caught stopping signal...
2025-07-21 14:23:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 caught stopping signal...
2025-07-21 14:23:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 All dispatchers stopped
2025-07-21 14:23:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 successfully reported itself as stopped in 1.485 ms
2025-07-21 14:23:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:58941ba4 has been stopped in total 86.1776 ms
2025-07-21 14:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 caught stopped signal...
2025-07-21 14:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 All dispatchers stopped
2025-07-21 14:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 successfully reported itself as stopped in 0.7767 ms
2025-07-21 14:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28680:37bd4b43 has been stopped in total 728.1587 ms
2025-07-21 14:23:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:23:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:23:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:23:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 successfully announced in 64.8721 ms
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 successfully announced in 64.9094 ms
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 all the dispatchers started
2025-07-21 14:23:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 all the dispatchers started
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 caught stopping signal...
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 caught stopping signal...
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 caught stopped signal...
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 caught stopped signal...
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 All dispatchers stopped
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 All dispatchers stopped
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 successfully reported itself as stopped in 1.8891 ms
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 successfully reported itself as stopped in 1.5282 ms
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:c0fc3548 has been stopped in total 982.5171 ms
2025-07-21 14:24:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25696:0d3c5703 has been stopped in total 982.763 ms
2025-07-21 14:24:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:24:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:24:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:24:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae successfully announced in 64.1283 ms
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 successfully announced in 64.1283 ms
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 all the dispatchers started
2025-07-21 14:24:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae all the dispatchers started
2025-07-21 14:27:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 caught stopping signal...
2025-07-21 14:27:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae caught stopping signal...
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae caught stopped signal...
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 All dispatchers stopped
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 successfully reported itself as stopped in 1.6242 ms
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:154620d0 has been stopped in total 505.1202 ms
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae All dispatchers stopped
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae successfully reported itself as stopped in 0.8735 ms
2025-07-21 14:27:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18504:a216faae has been stopped in total 515.9884 ms
2025-07-21 14:27:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:27:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:27:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:27:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 successfully announced in 61.5422 ms
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 successfully announced in 66.9441 ms
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 all the dispatchers started
2025-07-21 14:27:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 all the dispatchers started
2025-07-21 14:28:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 caught stopping signal...
2025-07-21 14:28:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 caught stopping signal...
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 caught stopped signal...
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 caught stopped signal...
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 All dispatchers stopped
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 successfully reported itself as stopped in 1.6015 ms
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:8a80cf78 has been stopped in total 911.8214 ms
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 All dispatchers stopped
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 successfully reported itself as stopped in 0.8134 ms
2025-07-21 14:28:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27052:75fd0d68 has been stopped in total 959.2151 ms
2025-07-21 14:28:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:28:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:28:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:28:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 successfully announced in 64.051 ms
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 successfully announced in 64.2561 ms
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 all the dispatchers started
2025-07-21 14:28:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 all the dispatchers started
2025-07-21 14:29:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 caught stopping signal...
2025-07-21 14:29:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 caught stopping signal...
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 caught stopped signal...
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 caught stopped signal...
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 All dispatchers stopped
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 successfully reported itself as stopped in 1.5315 ms
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:4e855fd0 has been stopped in total 750.2523 ms
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 All dispatchers stopped
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 successfully reported itself as stopped in 0.7723 ms
2025-07-21 14:29:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33688:a90345d3 has been stopped in total 752.2888 ms
2025-07-21 14:29:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:29:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:29:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:29:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 successfully announced in 65.2736 ms
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 successfully announced in 65.3634 ms
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 all the dispatchers started
2025-07-21 14:29:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 all the dispatchers started
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 caught stopping signal...
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 caught stopping signal...
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 caught stopped signal...
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 caught stopped signal...
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 All dispatchers stopped
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 successfully reported itself as stopped in 2.3896 ms
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:caaf3129 has been stopped in total 599.1018 ms
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 All dispatchers stopped
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 successfully reported itself as stopped in 0.6771 ms
2025-07-21 14:29:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16948:94b1db70 has been stopped in total 600.8005 ms
2025-07-21 14:29:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:29:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:29:56 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:29:56 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 successfully announced in 64.9237 ms
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 successfully announced in 65.2656 ms
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 all the dispatchers started
2025-07-21 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 all the dispatchers started
2025-07-21 14:38:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 caught stopping signal...
2025-07-21 14:38:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 caught stopping signal...
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 All dispatchers stopped
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 successfully reported itself as stopped in 2.5911 ms
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:52246dd3 has been stopped in total 449.2385 ms
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 All dispatchers stopped
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 successfully reported itself as stopped in 0.9379 ms
2025-07-21 14:38:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22348:2d2d9932 has been stopped in total 591.0205 ms
2025-07-21 14:39:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:39:15 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:39:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:39:16 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d successfully announced in 67.7516 ms
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c successfully announced in 67.8542 ms
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d all the dispatchers started
2025-07-21 14:39:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c all the dispatchers started
2025-07-21 14:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d caught stopping signal...
2025-07-21 14:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c caught stopping signal...
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c caught stopped signal...
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d caught stopped signal...
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d All dispatchers stopped
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d successfully reported itself as stopped in 1.5824 ms
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:4c4a435d has been stopped in total 833.0334 ms
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c All dispatchers stopped
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c successfully reported itself as stopped in 1.0376 ms
2025-07-21 14:44:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23292:ab34aa9c has been stopped in total 879.1318 ms
2025-07-21 14:51:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:51:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:51:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:51:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:51:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:51:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:51:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:51:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 successfully announced in 65.5986 ms
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 successfully announced in 65.5983 ms
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 all the dispatchers started
2025-07-21 14:51:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 all the dispatchers started
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 caught stopping signal...
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 caught stopping signal...
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 All dispatchers stopped
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 successfully reported itself as stopped in 1.9653 ms
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:d0fadd56 has been stopped in total 301.7612 ms
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 All dispatchers stopped
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 successfully reported itself as stopped in 0.7152 ms
2025-07-21 14:53:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:33592:51d04269 has been stopped in total 362.959 ms
2025-07-21 14:53:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 14:53:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 14:53:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 14:53:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 successfully announced in 62.0768 ms
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf successfully announced in 62.2724 ms
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 all the dispatchers started
2025-07-21 14:53:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf all the dispatchers started
2025-07-21 14:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 caught stopping signal...
2025-07-21 14:58:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf caught stopping signal...
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf All dispatchers stopped
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf successfully reported itself as stopped in 1.6363 ms
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:6f917eaf has been stopped in total 442.6645 ms
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 All dispatchers stopped
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 successfully reported itself as stopped in 0.8929 ms
2025-07-21 14:58:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10212:0ad4bd35 has been stopped in total 454.7345 ms
2025-07-21 15:01:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:01:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:01:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:01:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 successfully announced in 61.6419 ms
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 successfully announced in 61.8613 ms
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 all the dispatchers started
2025-07-21 15:01:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 all the dispatchers started
2025-07-21 15:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 caught stopping signal...
2025-07-21 15:13:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 caught stopping signal...
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 All dispatchers stopped
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 All dispatchers stopped
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 successfully reported itself as stopped in 1.6898 ms
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:f44368b7 has been stopped in total 482.2462 ms
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 successfully reported itself as stopped in 0.7643 ms
2025-07-21 15:13:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16864:11e95fe3 has been stopped in total 486.0663 ms
2025-07-21 15:15:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:15:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:15:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:15:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d successfully announced in 62.6213 ms
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f successfully announced in 62.9019 ms
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f all the dispatchers started
2025-07-21 15:15:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d all the dispatchers started
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d caught stopping signal...
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f caught stopping signal...
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d All dispatchers stopped
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d successfully reported itself as stopped in 1.4414 ms
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:244b956d has been stopped in total 246.14 ms
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f All dispatchers stopped
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f successfully reported itself as stopped in 0.7793 ms
2025-07-21 15:16:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34256:f1a9481f has been stopped in total 278.0701 ms
2025-07-21 15:16:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:16:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:16:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:16:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc successfully announced in 63.0298 ms
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c successfully announced in 62.8829 ms
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc all the dispatchers started
2025-07-21 15:16:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c all the dispatchers started
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc caught stopping signal...
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c caught stopping signal...
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc All dispatchers stopped
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc successfully reported itself as stopped in 1.832 ms
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:12b513fc has been stopped in total 132.8538 ms
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c All dispatchers stopped
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c successfully reported itself as stopped in 1.1548 ms
2025-07-21 15:19:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30948:d2deff5c has been stopped in total 211.6067 ms
2025-07-21 15:42:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:42:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:42:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:42:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 successfully announced in 62.6696 ms
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 successfully announced in 63.0437 ms
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 all the dispatchers started
2025-07-21 15:42:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 all the dispatchers started
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 caught stopping signal...
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 caught stopping signal...
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 caught stopped signal...
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 caught stopped signal...
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 All dispatchers stopped
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 successfully reported itself as stopped in 1.6085 ms
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 All dispatchers stopped
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:5b742478 has been stopped in total 684.0344 ms
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 successfully reported itself as stopped in 0.7126 ms
2025-07-21 15:45:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2096:3d82b1f1 has been stopped in total 685.0702 ms
2025-07-21 15:45:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:45:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:45:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 successfully announced in 61.4389 ms
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd successfully announced in 61.5741 ms
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 all the dispatchers started
2025-07-21 15:45:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd all the dispatchers started
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 caught stopping signal...
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd caught stopping signal...
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 All dispatchers stopped
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 successfully reported itself as stopped in 1.7638 ms
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:9ca9d111 has been stopped in total 62.0731 ms
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd All dispatchers stopped
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd successfully reported itself as stopped in 0.8639 ms
2025-07-21 15:46:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:36a2e5dd has been stopped in total 79.9612 ms
2025-07-21 15:46:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:46:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:46:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:46:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:46:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:46:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 successfully announced in 63.8725 ms
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b successfully announced in 64.1286 ms
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b all the dispatchers started
2025-07-21 15:46:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 all the dispatchers started
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 caught stopping signal...
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b caught stopping signal...
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b All dispatchers stopped
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 All dispatchers stopped
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b successfully reported itself as stopped in 1.7012 ms
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:31edc54b has been stopped in total 463.7586 ms
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 successfully reported itself as stopped in 0.7254 ms
2025-07-21 15:47:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9640:bc154925 has been stopped in total 464.6757 ms
2025-07-21 15:47:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:47:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:47:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:47:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b successfully announced in 63.6325 ms
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa successfully announced in 64.6126 ms
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b all the dispatchers started
2025-07-21 15:47:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa all the dispatchers started
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b caught stopping signal...
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa caught stopping signal...
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa caught stopped signal...
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b caught stopped signal...
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b All dispatchers stopped
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa All dispatchers stopped
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b successfully reported itself as stopped in 3.4423 ms
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa successfully reported itself as stopped in 2.2874 ms
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:5e00d95b has been stopped in total 789.1044 ms
2025-07-21 15:47:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12828:22a9eaaa has been stopped in total 789.0095 ms
2025-07-21 15:48:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:48:07 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:48:07 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:48:07 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a successfully announced in 62.5938 ms
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 successfully announced in 62.5839 ms
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 all the dispatchers started
2025-07-21 15:48:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a all the dispatchers started
2025-07-21 15:51:23 [Warning] Microsoft.AspNetCore.Components.Web.ErrorBoundary: Unhandled exception rendering component: "Object reference not set to an instance of an object."
System.NullReferenceException: Object reference not set to an instance of an object.
   at BootstrapBlazor.Components.Tab.<>c__DisplayClass194_0.<AddTabByUrl>b__2(TabItem tab)
   at System.Linq.Enumerable.TryGetFirst[TSource](IEnumerable`1 source, Func`2 predicate, Boolean& found)
   at System.Linq.Enumerable.FirstOrDefault[TSource](IEnumerable`1 source, Func`2 predicate)
   at BootstrapBlazor.Components.Tab.AddTabByUrl()
   at BootstrapBlazor.Components.Tab.OnParametersSet()
   at Microsoft.AspNetCore.Components.ComponentBase.CallOnParametersSetAsync()
   at Microsoft.AspNetCore.Components.ComponentBase.SetParametersAsync(ParameterView parameters)
   at Microsoft.AspNetCore.Components.Rendering.ComponentState.SupplyCombinedParameters(ParameterView directAndCascadingParameters)
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a caught stopping signal...
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 caught stopping signal...
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 caught stopped signal...
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a caught stopped signal...
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 All dispatchers stopped
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 successfully reported itself as stopped in 1.5498 ms
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:28d9ddf8 has been stopped in total 786.9258 ms
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a All dispatchers stopped
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a successfully reported itself as stopped in 0.8247 ms
2025-07-21 15:51:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15920:89ea6d5a has been stopped in total 843.2505 ms
2025-07-21 15:52:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:52:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:52:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:52:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:52:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:52:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:52:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:52:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c successfully announced in 65.2909 ms
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 successfully announced in 65.0978 ms
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c all the dispatchers started
2025-07-21 15:52:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 all the dispatchers started
2025-07-21 15:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c caught stopping signal...
2025-07-21 15:57:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 caught stopping signal...
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 caught stopped signal...
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c caught stopped signal...
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c All dispatchers stopped
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c successfully reported itself as stopped in 1.6656 ms
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:cf65929c has been stopped in total 854.3902 ms
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 All dispatchers stopped
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 successfully reported itself as stopped in 0.9412 ms
2025-07-21 15:57:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14352:49802286 has been stopped in total 897.413 ms
2025-07-21 15:57:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 15:57:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 15:57:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 15:57:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 successfully announced in 63.5428 ms
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab successfully announced in 63.7139 ms
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab all the dispatchers started
2025-07-21 15:57:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 all the dispatchers started
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 caught stopping signal...
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab caught stopping signal...
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab All dispatchers stopped
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab successfully reported itself as stopped in 1.8539 ms
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:6ffe86ab has been stopped in total 24.4257 ms
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 All dispatchers stopped
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 successfully reported itself as stopped in 0.7753 ms
2025-07-21 16:09:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13508:2da3ab77 has been stopped in total 136.6017 ms
2025-07-21 16:09:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:09:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:09:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:09:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:09:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:09:53 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:09:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:09:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 successfully announced in 68.2265 ms
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c successfully announced in 68.4756 ms
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c all the dispatchers started
2025-07-21 16:09:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 all the dispatchers started
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 caught stopping signal...
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c caught stopping signal...
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c All dispatchers stopped
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c successfully reported itself as stopped in 3.5647 ms
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:77a9aa9c has been stopped in total 324.252 ms
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 caught stopped signal...
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 All dispatchers stopped
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 successfully reported itself as stopped in 1.109 ms
2025-07-21 16:15:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34336:f10953d8 has been stopped in total 618.8882 ms
2025-07-21 16:15:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:15:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:15:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:15:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:15:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:15:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 successfully announced in 60.7427 ms
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 successfully announced in 60.9281 ms
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 all the dispatchers started
2025-07-21 16:15:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 all the dispatchers started
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 caught stopping signal...
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 caught stopping signal...
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 All dispatchers stopped
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 All dispatchers stopped
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 successfully reported itself as stopped in 1.7279 ms
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0751a5e2 has been stopped in total 440.9282 ms
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 successfully reported itself as stopped in 0.6437 ms
2025-07-21 16:25:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6724:0c9058f4 has been stopped in total 440.9249 ms
2025-07-21 16:26:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:26:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:26:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:26:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:26:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:26:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 successfully announced in 60.5089 ms
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 successfully announced in 60.6771 ms
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 all the dispatchers started
2025-07-21 16:26:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 all the dispatchers started
2025-07-21 16:35:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 caught stopping signal...
2025-07-21 16:35:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 caught stopping signal...
2025-07-21 16:35:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 All dispatchers stopped
2025-07-21 16:35:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 successfully reported itself as stopped in 1.8789 ms
2025-07-21 16:35:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ed00e743 has been stopped in total 265.9729 ms
2025-07-21 16:35:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 All dispatchers stopped
2025-07-21 16:35:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 successfully reported itself as stopped in 0.6481 ms
2025-07-21 16:35:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19772:ac5cecf6 has been stopped in total 335.2888 ms
2025-07-21 16:35:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:35:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:35:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:35:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d successfully announced in 62.2878 ms
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 successfully announced in 62.8481 ms
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d all the dispatchers started
2025-07-21 16:35:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 all the dispatchers started
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d caught stopping signal...
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 caught stopping signal...
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 All dispatchers stopped
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 successfully reported itself as stopped in 2.3398 ms
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:15dc52a1 has been stopped in total 176.143 ms
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d All dispatchers stopped
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d successfully reported itself as stopped in 0.9918 ms
2025-07-21 16:42:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14792:517fce5d has been stopped in total 212.8803 ms
2025-07-21 16:44:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:44:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:44:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:44:20 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 successfully announced in 66.8499 ms
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c successfully announced in 60.8673 ms
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c all the dispatchers started
2025-07-21 16:44:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 all the dispatchers started
2025-07-21 16:45:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 caught stopping signal...
2025-07-21 16:45:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c caught stopping signal...
2025-07-21 16:45:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c caught stopped signal...
2025-07-21 16:45:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 caught stopped signal...
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c All dispatchers stopped
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c successfully reported itself as stopped in 2.0533 ms
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:9e62bd0c has been stopped in total 955.3371 ms
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 All dispatchers stopped
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 successfully reported itself as stopped in 0.8672 ms
2025-07-21 16:45:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25108:bbacfd25 has been stopped in total 993.6909 ms
2025-07-21 16:46:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:46:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 successfully announced in 59.9212 ms
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a successfully announced in 59.782 ms
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a all the dispatchers started
2025-07-21 16:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 all the dispatchers started
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 caught stopping signal...
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a caught stopping signal...
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a caught stopped signal...
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 caught stopped signal...
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a All dispatchers stopped
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 All dispatchers stopped
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a successfully reported itself as stopped in 2.5311 ms
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:7c14a21a has been stopped in total 806.2524 ms
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 successfully reported itself as stopped in 1.7766 ms
2025-07-21 16:46:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4360:973b1348 has been stopped in total 806.5997 ms
2025-07-21 16:46:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:46:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:46:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:46:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:46:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:46:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 successfully announced in 63.5248 ms
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 successfully announced in 65.6148 ms
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 all the dispatchers started
2025-07-21 16:46:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 all the dispatchers started
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 caught stopping signal...
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 caught stopping signal...
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 All dispatchers stopped
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 successfully reported itself as stopped in 1.3633 ms
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:d60d5156 has been stopped in total 238.4969 ms
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 All dispatchers stopped
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 successfully reported itself as stopped in 0.9405 ms
2025-07-21 16:47:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19056:3a99a489 has been stopped in total 249.8249 ms
2025-07-21 16:48:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:48:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:48:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:48:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:48:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:48:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 successfully announced in 62.9649 ms
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 successfully announced in 62.7616 ms
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 all the dispatchers started
2025-07-21 16:48:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 all the dispatchers started
2025-07-21 16:48:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 caught stopping signal...
2025-07-21 16:48:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 caught stopping signal...
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 All dispatchers stopped
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 All dispatchers stopped
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 successfully reported itself as stopped in 1.4487 ms
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:36a289f1 has been stopped in total 332.7698 ms
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 successfully reported itself as stopped in 2.6736 ms
2025-07-21 16:48:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28932:67b500f4 has been stopped in total 333.1376 ms
2025-07-21 16:48:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 16:48:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 16:48:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 16:48:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 16:48:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 16:48:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d successfully announced in 63.4112 ms
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd successfully announced in 63.6845 ms
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d all the dispatchers started
2025-07-21 16:48:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd all the dispatchers started
2025-07-21 17:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd caught stopping signal...
2025-07-21 17:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d caught stopping signal...
2025-07-21 17:03:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d caught stopped signal...
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd caught stopped signal...
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d All dispatchers stopped
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd All dispatchers stopped
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d successfully reported itself as stopped in 2.2775 ms
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:037bfa2d has been stopped in total 669.6938 ms
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd successfully reported itself as stopped in 0.6727 ms
2025-07-21 17:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27624:2dafdcbd has been stopped in total 671.3415 ms
2025-07-21 17:04:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-21 17:04:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-21 17:04:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-21 17:04:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 successfully announced in 64.0231 ms
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 successfully announced in 64.2737 ms
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 all the dispatchers started
2025-07-21 17:04:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 all the dispatchers started
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 caught stopping signal...
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 caught stopping signal...
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 All dispatchers stopped
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 All dispatchers stopped
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 successfully reported itself as stopped in 2.896 ms
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:ab473631 has been stopped in total 229.8505 ms
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 successfully reported itself as stopped in 0.8654 ms
2025-07-21 17:04:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4280:17d61606 has been stopped in total 230.458 ms
