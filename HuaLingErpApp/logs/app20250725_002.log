2025-07-25 11:38:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:38:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:38:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb successfully announced in 60.0751 ms
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 successfully announced in 59.9256 ms
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 all the dispatchers started
2025-07-25 11:38:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb all the dispatchers started
2025-07-25 11:45:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb caught stopping signal...
2025-07-25 11:45:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 caught stopping signal...
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb All dispatchers stopped
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb successfully reported itself as stopped in 4.1418 ms
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:4e58cbcb has been stopped in total 324.436 ms
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 All dispatchers stopped
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 successfully reported itself as stopped in 0.7698 ms
2025-07-25 11:45:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25660:a5dd38a3 has been stopped in total 345.2578 ms
2025-07-25 11:46:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:46:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:46:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:46:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:46:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 successfully announced in 63.5728 ms
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 successfully announced in 63.3779 ms
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 all the dispatchers started
2025-07-25 11:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 all the dispatchers started
2025-07-25 11:48:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 caught stopping signal...
2025-07-25 11:48:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 caught stopping signal...
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 All dispatchers stopped
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 All dispatchers stopped
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 successfully reported itself as stopped in 1.6381 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:9e5d27d4 has been stopped in total 122.8386 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 successfully reported itself as stopped in 1.4597 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22596:aac4f901 has been stopped in total 123.9004 ms
