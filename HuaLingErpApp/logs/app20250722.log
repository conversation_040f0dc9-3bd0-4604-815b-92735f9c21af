2025-07-22 08:16:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 08:16:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 08:16:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:16:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 successfully announced in 67.5028 ms
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 successfully announced in 67.7538 ms
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 all the dispatchers started
2025-07-22 08:16:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 all the dispatchers started
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 caught stopping signal...
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 caught stopping signal...
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 All dispatchers stopped
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 successfully reported itself as stopped in 2.3889 ms
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:ff750a33 has been stopped in total 248.2791 ms
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 All dispatchers stopped
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 successfully reported itself as stopped in 1.0446 ms
2025-07-22 08:20:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32060:66b17027 has been stopped in total 294.9362 ms
2025-07-22 08:20:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 08:20:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 08:20:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 08:20:13 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 08:20:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:20:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 successfully announced in 61.2487 ms
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d successfully announced in 61.437 ms
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d all the dispatchers started
2025-07-22 08:20:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 all the dispatchers started
2025-07-22 08:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d caught stopping signal...
2025-07-22 08:24:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 caught stopping signal...
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 caught stopped signal...
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d caught stopped signal...
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d All dispatchers stopped
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d successfully reported itself as stopped in 2.4468 ms
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:52bf515d has been stopped in total 897.0331 ms
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 All dispatchers stopped
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 successfully reported itself as stopped in 1.3538 ms
2025-07-22 08:24:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12540:f841fe49 has been stopped in total 914.904 ms
2025-07-22 08:25:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 08:25:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 08:25:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:25:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d successfully announced in 62.1578 ms
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d successfully announced in 62.0647 ms
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d all the dispatchers started
2025-07-22 08:25:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d all the dispatchers started
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d caught stopping signal...
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d caught stopping signal...
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d caught stopped signal...
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d caught stopped signal...
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d All dispatchers stopped
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d successfully reported itself as stopped in 1.792 ms
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:b0baea6d has been stopped in total 544.3952 ms
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d All dispatchers stopped
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d successfully reported itself as stopped in 0.7983 ms
2025-07-22 08:44:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19112:33ed506d has been stopped in total 613.5615 ms
2025-07-22 08:45:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 08:45:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 08:45:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:45:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:9c7598c6 successfully announced in 99.6692 ms
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:0b985c6d successfully announced in 99.8564 ms
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:0b985c6d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:9c7598c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:0b985c6d all the dispatchers started
2025-07-22 08:45:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18624:9c7598c6 all the dispatchers started
2025-07-22 08:57:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 08:57:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 08:57:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 08:57:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d successfully announced in 66.4007 ms
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 successfully announced in 57.2022 ms
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 all the dispatchers started
2025-07-22 08:57:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d all the dispatchers started
2025-07-22 09:02:46 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d caught stopping signal...
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 caught stopping signal...
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d All dispatchers stopped
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 All dispatchers stopped
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d successfully reported itself as stopped in 2.1537 ms
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:ccee070d has been stopped in total 56.3877 ms
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 successfully reported itself as stopped in 0.6961 ms
2025-07-22 09:04:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31244:87e93fe3 has been stopped in total 56.2532 ms
2025-07-22 09:05:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:05:11 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:05:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:05:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:05:12 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:05:12 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 successfully announced in 66.0431 ms
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 successfully announced in 66.3307 ms
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 all the dispatchers started
2025-07-22 09:05:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 all the dispatchers started
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 caught stopping signal...
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 caught stopping signal...
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 caught stopped signal...
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 caught stopped signal...
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 All dispatchers stopped
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 All dispatchers stopped
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 successfully reported itself as stopped in 1.8397 ms
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:2ba2dfa8 has been stopped in total 677.086 ms
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 successfully reported itself as stopped in 0.8456 ms
2025-07-22 09:09:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15288:38254ab1 has been stopped in total 676.9702 ms
2025-07-22 09:10:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:10:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:10:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:10:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:10:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:10:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 successfully announced in 63.594 ms
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be successfully announced in 63.7274 ms
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 all the dispatchers started
2025-07-22 09:10:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be all the dispatchers started
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 caught stopping signal...
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be caught stopping signal...
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be caught stopped signal...
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 caught stopped signal...
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 All dispatchers stopped
2025-07-22 09:16:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be All dispatchers stopped
2025-07-22 09:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be successfully reported itself as stopped in 2.1232 ms
2025-07-22 09:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:1479f1be has been stopped in total 799.6634 ms
2025-07-22 09:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 successfully reported itself as stopped in 1.0028 ms
2025-07-22 09:16:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30904:f0f11a40 has been stopped in total 800.2994 ms
2025-07-22 09:17:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:17:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:17:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:17:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 successfully announced in 63.4369 ms
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 successfully announced in 63.715 ms
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 all the dispatchers started
2025-07-22 09:17:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 all the dispatchers started
2025-07-22 09:19:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 caught stopping signal...
2025-07-22 09:19:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 caught stopping signal...
2025-07-22 09:19:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 caught stopped signal...
2025-07-22 09:19:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 caught stopped signal...
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 All dispatchers stopped
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 All dispatchers stopped
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 successfully reported itself as stopped in 2.6263 ms
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:f24d26c1 has been stopped in total 749.5554 ms
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 successfully reported itself as stopped in 1.1325 ms
2025-07-22 09:19:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14428:fea32981 has been stopped in total 752.897 ms
2025-07-22 09:20:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:20:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:20:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:20:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:20:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:20:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:20:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:20:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f successfully announced in 62.6469 ms
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba successfully announced in 62.7781 ms
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba all the dispatchers started
2025-07-22 09:20:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f all the dispatchers started
2025-07-22 09:23:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f caught stopping signal...
2025-07-22 09:23:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba caught stopping signal...
2025-07-22 09:23:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba All dispatchers stopped
2025-07-22 09:23:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba successfully reported itself as stopped in 1.9324 ms
2025-07-22 09:23:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:982e8cba has been stopped in total 27.4738 ms
2025-07-22 09:23:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f All dispatchers stopped
2025-07-22 09:23:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f successfully reported itself as stopped in 0.9442 ms
2025-07-22 09:23:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17416:2604260f has been stopped in total 296.6356 ms
2025-07-22 09:24:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:24:02 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:24:02 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:24:02 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb successfully announced in 63.5325 ms
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 successfully announced in 63.7282 ms
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 all the dispatchers started
2025-07-22 09:24:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb all the dispatchers started
2025-07-22 09:27:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb caught stopping signal...
2025-07-22 09:27:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 caught stopping signal...
2025-07-22 09:27:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 caught stopped signal...
2025-07-22 09:27:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb caught stopped signal...
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb All dispatchers stopped
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 All dispatchers stopped
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb successfully reported itself as stopped in 2.0701 ms
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:70d492bb has been stopped in total 915.7478 ms
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 successfully reported itself as stopped in 1.3476 ms
2025-07-22 09:27:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30000:fd3661b5 has been stopped in total 915.8614 ms
2025-07-22 09:27:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:27:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:27:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:27:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:27:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:27:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 successfully announced in 66.8283 ms
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 successfully announced in 66.7968 ms
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 all the dispatchers started
2025-07-22 09:27:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 all the dispatchers started
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 caught stopping signal...
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 caught stopping signal...
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 caught stopped signal...
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 caught stopped signal...
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 All dispatchers stopped
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 successfully reported itself as stopped in 1.7532 ms
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:b3e29215 has been stopped in total 734.4681 ms
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 All dispatchers stopped
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 successfully reported itself as stopped in 0.7192 ms
2025-07-22 09:30:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15984:60a705a8 has been stopped in total 769.0835 ms
2025-07-22 09:30:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:30:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:30:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:30:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f successfully announced in 63.8659 ms
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 successfully announced in 64.1642 ms
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f all the dispatchers started
2025-07-22 09:30:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 all the dispatchers started
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 caught stopping signal...
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f caught stopping signal...
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f All dispatchers stopped
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 All dispatchers stopped
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f successfully reported itself as stopped in 2.3321 ms
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:84f0fe2f has been stopped in total 233.7186 ms
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 successfully reported itself as stopped in 1.0772 ms
2025-07-22 09:36:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29140:fe95fc49 has been stopped in total 234.0682 ms
2025-07-22 09:37:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:37:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:37:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:37:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:064f3adb successfully announced in 63.0078 ms
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:d38ea800 successfully announced in 63.3244 ms
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:d38ea800 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:064f3adb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:d38ea800 all the dispatchers started
2025-07-22 09:37:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32516:064f3adb all the dispatchers started
2025-07-22 09:38:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:38:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:38:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:38:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:70233e84 successfully announced in 66.9342 ms
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:96b2bd02 successfully announced in 66.7114 ms
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:70233e84 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:96b2bd02 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:70233e84 all the dispatchers started
2025-07-22 09:38:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:96b2bd02 all the dispatchers started
2025-07-22 09:41:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:96b2bd02 caught stopping signal...
2025-07-22 09:41:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:70233e84 caught stopping signal...
2025-07-22 09:41:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:70233e84 caught stopped signal...
2025-07-22 09:41:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34440:96b2bd02 caught stopped signal...
2025-07-22 09:41:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:41:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:41:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:41:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 successfully announced in 60.3752 ms
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 successfully announced in 60.6533 ms
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 all the dispatchers started
2025-07-22 09:41:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 all the dispatchers started
2025-07-22 09:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 caught stopping signal...
2025-07-22 09:44:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 caught stopping signal...
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 caught stopped signal...
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 caught stopped signal...
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 All dispatchers stopped
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 successfully reported itself as stopped in 2.0082 ms
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:dc102541 has been stopped in total 858.8911 ms
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 All dispatchers stopped
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 successfully reported itself as stopped in 0.8379 ms
2025-07-22 09:44:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26180:9d9ed131 has been stopped in total 911.5682 ms
2025-07-22 09:45:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:45:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:45:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:45:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e successfully announced in 63.5244 ms
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 successfully announced in 63.815 ms
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:45:22 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 all the dispatchers started
2025-07-22 09:45:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e all the dispatchers started
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e caught stopping signal...
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 caught stopping signal...
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e All dispatchers stopped
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e successfully reported itself as stopped in 3.0308 ms
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:d957160e has been stopped in total 359.6002 ms
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 All dispatchers stopped
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 successfully reported itself as stopped in 0.8822 ms
2025-07-22 09:45:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:29836:1cd27f00 has been stopped in total 450.6873 ms
2025-07-22 09:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:46:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:46:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:46:03 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c successfully announced in 57.6503 ms
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b successfully announced in 65.7833 ms
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:46:04 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b all the dispatchers started
2025-07-22 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c all the dispatchers started
2025-07-22 09:52:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b caught stopping signal...
2025-07-22 09:52:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c caught stopping signal...
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c caught stopped signal...
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b caught stopped signal...
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c All dispatchers stopped
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b All dispatchers stopped
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c successfully reported itself as stopped in 1.9994 ms
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd9dfb7c has been stopped in total 663.916 ms
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b successfully reported itself as stopped in 0.9328 ms
2025-07-22 09:52:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25416:fd74910b has been stopped in total 664.3773 ms
2025-07-22 09:53:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:53:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:53:08 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:53:08 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 successfully announced in 62.7469 ms
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 successfully announced in 63.1566 ms
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 all the dispatchers started
2025-07-22 09:53:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 all the dispatchers started
2025-07-22 09:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 caught stopping signal...
2025-07-22 09:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 caught stopping signal...
2025-07-22 09:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 caught stopped signal...
2025-07-22 09:54:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 caught stopped signal...
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 All dispatchers stopped
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 All dispatchers stopped
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 successfully reported itself as stopped in 1.4472 ms
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:0ef5e6f1 has been stopped in total 817.8695 ms
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 successfully reported itself as stopped in 0.8533 ms
2025-07-22 09:54:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15336:c7ae2f82 has been stopped in total 817.8893 ms
2025-07-22 09:54:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 09:54:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 09:54:19 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 09:54:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc successfully announced in 63.8139 ms
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e successfully announced in 63.9352 ms
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc all the dispatchers started
2025-07-22 09:54:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e all the dispatchers started
2025-07-22 10:01:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e caught stopping signal...
2025-07-22 10:01:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc caught stopping signal...
2025-07-22 10:01:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e All dispatchers stopped
2025-07-22 10:01:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e successfully reported itself as stopped in 2.7542 ms
2025-07-22 10:01:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:ed8b098e has been stopped in total 470.9581 ms
2025-07-22 10:01:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc All dispatchers stopped
2025-07-22 10:01:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc successfully reported itself as stopped in 1.0669 ms
2025-07-22 10:01:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:34496:71f180cc has been stopped in total 562.5093 ms
2025-07-22 10:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-22 10:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-22 10:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-22 10:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-22 10:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-22 10:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-22 10:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-22 10:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 successfully announced in 60.7116 ms
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d successfully announced in 60.4445 ms
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 all the dispatchers started
2025-07-22 10:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d all the dispatchers started
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d caught stopping signal...
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 caught stopping signal...
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 All dispatchers stopped
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 successfully reported itself as stopped in 2.2233 ms
2025-07-22 10:16:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:951ede44 has been stopped in total 88.5057 ms
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d caught stopped signal...
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d All dispatchers stopped
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d successfully reported itself as stopped in 1.1233 ms
2025-07-22 10:16:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17052:26a9fa6d has been stopped in total 947.1909 ms
