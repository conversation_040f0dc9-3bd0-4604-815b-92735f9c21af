2025-07-25 11:47:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:47:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:47:22 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:47:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:47:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 successfully announced in 58.8308 ms
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 successfully announced in 58.6817 ms
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 all the dispatchers started
2025-07-25 11:47:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 all the dispatchers started
2025-07-25 11:48:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 caught stopping signal...
2025-07-25 11:48:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 caught stopping signal...
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 All dispatchers stopped
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 All dispatchers stopped
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 successfully reported itself as stopped in 1.6378 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 successfully reported itself as stopped in 1.6539 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:64bd9552 has been stopped in total 420.1495 ms
2025-07-25 11:48:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31172:a189fd99 has been stopped in total 420.5819 ms
2025-07-25 11:58:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 11:58:13 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 11:58:14 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 11:58:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 11:58:14 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 successfully announced in 60.9607 ms
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 successfully announced in 61.1238 ms
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 all the dispatchers started
2025-07-25 11:58:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 all the dispatchers started
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 caught stopping signal...
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 caught stopping signal...
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 All dispatchers stopped
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 successfully reported itself as stopped in 66.7609 ms
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:b1cd4cb4 has been stopped in total 129.3957 ms
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 caught stopped signal...
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 All dispatchers stopped
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 successfully reported itself as stopped in 1.1255 ms
2025-07-25 13:34:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14732:a185fb70 has been stopped in total 900.8554 ms
2025-07-25 13:34:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 13:34:52 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 13:34:52 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 13:34:52 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 13:34:52 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 successfully announced in 63.1375 ms
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f successfully announced in 63.0272 ms
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f all the dispatchers started
2025-07-25 13:34:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 all the dispatchers started
2025-07-25 13:40:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f caught stopping signal...
2025-07-25 13:40:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 caught stopping signal...
2025-07-25 13:40:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 All dispatchers stopped
2025-07-25 13:40:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 successfully reported itself as stopped in 1.7884 ms
2025-07-25 13:40:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:429d5411 has been stopped in total 44.7423 ms
2025-07-25 13:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f caught stopped signal...
2025-07-25 13:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f All dispatchers stopped
2025-07-25 13:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f successfully reported itself as stopped in 0.8357 ms
2025-07-25 13:40:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4700:91864d3f has been stopped in total 697.2926 ms
2025-07-25 14:02:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:02:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:02:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:02:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:02:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 successfully announced in 61.0791 ms
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 successfully announced in 61.0109 ms
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 all the dispatchers started
2025-07-25 14:02:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 all the dispatchers started
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 caught stopping signal...
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 caught stopping signal...
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 All dispatchers stopped
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 successfully reported itself as stopped in 2.7553 ms
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:7bbeae28 has been stopped in total 100.4027 ms
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 All dispatchers stopped
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 successfully reported itself as stopped in 2.5747 ms
2025-07-25 14:15:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12648:5c754d49 has been stopped in total 169.1045 ms
2025-07-25 14:15:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:15:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:15:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:15:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:15:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 successfully announced in 59.3515 ms
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d successfully announced in 59.6252 ms
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 all the dispatchers started
2025-07-25 14:15:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d all the dispatchers started
2025-07-25 14:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 caught stopping signal...
2025-07-25 14:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d caught stopping signal...
2025-07-25 14:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d All dispatchers stopped
2025-07-25 14:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d successfully reported itself as stopped in 1.3611 ms
2025-07-25 14:17:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:399ff97d has been stopped in total 262.4214 ms
2025-07-25 14:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 All dispatchers stopped
2025-07-25 14:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 successfully reported itself as stopped in 0.8603 ms
2025-07-25 14:17:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30448:0f7a8952 has been stopped in total 509.2631 ms
2025-07-25 14:17:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:17:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:17:57 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:17:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:17:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:17:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:17:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:17:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:17:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f successfully announced in 64.0726 ms
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f successfully announced in 63.8938 ms
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f all the dispatchers started
2025-07-25 14:17:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f all the dispatchers started
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f caught stopping signal...
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f caught stopping signal...
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f All dispatchers stopped
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f successfully reported itself as stopped in 1.5044 ms
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:2bd9480f has been stopped in total 101.3063 ms
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f All dispatchers stopped
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f successfully reported itself as stopped in 0.6935 ms
2025-07-25 14:18:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31972:8d84dd2f has been stopped in total 118.9903 ms
2025-07-25 14:18:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:18:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:18:43 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:18:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:18:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 successfully announced in 59.3705 ms
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 successfully announced in 59.6182 ms
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 all the dispatchers started
2025-07-25 14:18:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 all the dispatchers started
2025-07-25 14:27:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 caught stopping signal...
2025-07-25 14:27:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 caught stopping signal...
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 All dispatchers stopped
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 successfully reported itself as stopped in 1.8591 ms
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:ab6f5d84 has been stopped in total 313.2959 ms
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 caught stopped signal...
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 All dispatchers stopped
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 successfully reported itself as stopped in 0.7771 ms
2025-07-25 14:27:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:23632:585b6d98 has been stopped in total 891.6217 ms
2025-07-25 14:28:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:28:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:28:16 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:28:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:28:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca successfully announced in 66.796 ms
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 successfully announced in 67.0609 ms
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca all the dispatchers started
2025-07-25 14:28:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 all the dispatchers started
2025-07-25 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 caught stopping signal...
2025-07-25 14:29:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca caught stopping signal...
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca All dispatchers stopped
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca successfully reported itself as stopped in 2.0646 ms
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:e9f146ca has been stopped in total 297.7454 ms
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 All dispatchers stopped
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 successfully reported itself as stopped in 0.8504 ms
2025-07-25 14:29:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6668:95012993 has been stopped in total 411.2244 ms
2025-07-25 14:30:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:30:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:30:10 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:30:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b successfully announced in 60.0495 ms
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c successfully announced in 60.2059 ms
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b all the dispatchers started
2025-07-25 14:30:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c all the dispatchers started
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b caught stopping signal...
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c caught stopping signal...
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c All dispatchers stopped
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c successfully reported itself as stopped in 1.47 ms
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:45ee122c has been stopped in total 265.5706 ms
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b All dispatchers stopped
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b successfully reported itself as stopped in 0.8009 ms
2025-07-25 14:31:12 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13436:8b9e9c5b has been stopped in total 327.886 ms
2025-07-25 14:31:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:31:25 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:31:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:31:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:31:26 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 successfully announced in 68.4987 ms
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 successfully announced in 68.6823 ms
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 all the dispatchers started
2025-07-25 14:31:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 all the dispatchers started
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 caught stopping signal...
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 caught stopping signal...
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 All dispatchers stopped
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 successfully reported itself as stopped in 1.8393 ms
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:97db5581 has been stopped in total 291.1389 ms
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 All dispatchers stopped
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 successfully reported itself as stopped in 0.8361 ms
2025-07-25 14:35:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:32052:2d382212 has been stopped in total 295.6962 ms
2025-07-25 14:35:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:35:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:35:14 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:35:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:35:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 successfully announced in 68.2561 ms
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d successfully announced in 68.1246 ms
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d all the dispatchers started
2025-07-25 14:35:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 all the dispatchers started
2025-07-25 14:40:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 caught stopping signal...
2025-07-25 14:40:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d caught stopping signal...
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d caught stopped signal...
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 caught stopped signal...
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d All dispatchers stopped
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d successfully reported itself as stopped in 2.165 ms
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:f7be259d has been stopped in total 523.0032 ms
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 All dispatchers stopped
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 successfully reported itself as stopped in 0.7895 ms
2025-07-25 14:40:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:652:bfd1efc3 has been stopped in total 530.8878 ms
2025-07-25 14:40:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:40:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:40:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:40:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:40:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b successfully announced in 67.2819 ms
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 successfully announced in 67.6703 ms
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 all the dispatchers started
2025-07-25 14:40:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b all the dispatchers started
2025-07-25 14:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 caught stopping signal...
2025-07-25 14:48:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b caught stopping signal...
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 All dispatchers stopped
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 successfully reported itself as stopped in 4.2564 ms
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:83951e92 has been stopped in total 353.9408 ms
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b All dispatchers stopped
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b successfully reported itself as stopped in 1.8166 ms
2025-07-25 14:48:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:31372:9cdaf54b has been stopped in total 597.3603 ms
2025-07-25 14:48:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 14:48:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 14:48:18 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 14:48:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 14:48:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 successfully announced in 62.2417 ms
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a successfully announced in 62.9085 ms
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 all the dispatchers started
2025-07-25 14:48:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a all the dispatchers started
2025-07-25 14:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a caught stopping signal...
2025-07-25 14:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 caught stopping signal...
2025-07-25 14:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 caught stopped signal...
2025-07-25 14:53:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a caught stopped signal...
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a All dispatchers stopped
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a successfully reported itself as stopped in 2.1024 ms
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:c10e9b3a has been stopped in total 923.6752 ms
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 All dispatchers stopped
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 successfully reported itself as stopped in 0.625 ms
2025-07-25 14:53:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30824:8f268f40 has been stopped in total 968.64 ms
2025-07-25 15:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:01:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:01:49 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:01:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:01:50 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:01:50 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 successfully announced in 69.2579 ms
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 successfully announced in 78.7151 ms
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 all the dispatchers started
2025-07-25 15:01:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 all the dispatchers started
2025-07-25 15:03:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 caught stopping signal...
2025-07-25 15:03:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 caught stopping signal...
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 caught stopped signal...
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 caught stopped signal...
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 All dispatchers stopped
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 All dispatchers stopped
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 successfully reported itself as stopped in 2.202 ms
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:edf6bbe2 has been stopped in total 977.3644 ms
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 successfully reported itself as stopped in 0.6525 ms
2025-07-25 15:03:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15756:61123fc1 has been stopped in total 978.0452 ms
2025-07-25 15:03:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:03:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:03:41 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:03:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:03:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 successfully announced in 63.0294 ms
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd successfully announced in 62.8132 ms
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 all the dispatchers started
2025-07-25 15:03:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd all the dispatchers started
2025-07-25 15:14:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd caught stopping signal...
2025-07-25 15:14:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 caught stopping signal...
2025-07-25 15:14:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 All dispatchers stopped
2025-07-25 15:14:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 successfully reported itself as stopped in 2.1507 ms
2025-07-25 15:14:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:c4ae6b30 has been stopped in total 233.9755 ms
2025-07-25 15:14:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd caught stopped signal...
2025-07-25 15:14:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd All dispatchers stopped
2025-07-25 15:14:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd successfully reported itself as stopped in 0.9244 ms
2025-07-25 15:14:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26116:eb84b2dd has been stopped in total 954.4541 ms
2025-07-25 15:14:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:14:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:14:59 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:14:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:14:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d successfully announced in 62.6934 ms
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 successfully announced in 62.6139 ms
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d all the dispatchers started
2025-07-25 15:14:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 all the dispatchers started
2025-07-25 15:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 caught stopping signal...
2025-07-25 15:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d caught stopping signal...
2025-07-25 15:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d caught stopped signal...
2025-07-25 15:25:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 caught stopped signal...
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d All dispatchers stopped
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 All dispatchers stopped
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d successfully reported itself as stopped in 2.1474 ms
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 successfully reported itself as stopped in 0.8973 ms
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e56300d has been stopped in total 986.1089 ms
2025-07-25 15:25:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18724:8e92fd83 has been stopped in total 986.6545 ms
2025-07-25 15:25:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:25:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:25:44 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:25:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:25:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:25:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:25:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:25:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:25:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d successfully announced in 62.9796 ms
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa successfully announced in 62.8297 ms
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d all the dispatchers started
2025-07-25 15:25:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa all the dispatchers started
2025-07-25 15:47:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d caught stopping signal...
2025-07-25 15:47:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa caught stopping signal...
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d All dispatchers stopped
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d successfully reported itself as stopped in 2.1804 ms
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:5008022d has been stopped in total 376.8511 ms
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa All dispatchers stopped
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa successfully reported itself as stopped in 0.7243 ms
2025-07-25 15:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:26264:fcd1d7fa has been stopped in total 383.4574 ms
2025-07-25 15:47:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:47:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:47:51 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:47:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:47:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 successfully announced in 62.8653 ms
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae successfully announced in 63.4599 ms
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 all the dispatchers started
2025-07-25 15:47:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae all the dispatchers started
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 caught stopping signal...
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae caught stopping signal...
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae caught stopped signal...
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 caught stopped signal...
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae All dispatchers stopped
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 All dispatchers stopped
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 successfully reported itself as stopped in 1.9349 ms
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:b5e83fb3 has been stopped in total 521.2895 ms
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae successfully reported itself as stopped in 1.0087 ms
2025-07-25 15:51:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:696126ae has been stopped in total 521.1873 ms
2025-07-25 15:51:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:51:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:51:58 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:51:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:51:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:51:59 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:51:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f successfully announced in 67.7762 ms
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff successfully announced in 68.1612 ms
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f all the dispatchers started
2025-07-25 15:51:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff all the dispatchers started
2025-07-25 15:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff caught stopping signal...
2025-07-25 15:55:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f caught stopping signal...
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f caught stopped signal...
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff caught stopped signal...
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff All dispatchers stopped
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff successfully reported itself as stopped in 1.9367 ms
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:6e6b4dff has been stopped in total 948.1948 ms
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f All dispatchers stopped
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f successfully reported itself as stopped in 0.8181 ms
2025-07-25 15:55:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:30748:7e76500f has been stopped in total 970.372 ms
2025-07-25 15:56:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 15:56:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 15:56:09 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 15:56:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 15:56:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc successfully announced in 65.2091 ms
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab successfully announced in 65.3205 ms
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab all the dispatchers started
2025-07-25 15:56:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc all the dispatchers started
2025-07-25 15:59:04 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
将截断字符串或二进制数据。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 523
ClientConnectionId:c64c8473-aa66-4fcb-b93c-b6e1950ae037
Error Number:8152,State:13,Class:16
2025-07-25 15:59:59 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 523
ClientConnectionId:833d42cd-9e67-4618-987c-868ac9dd12f2
Error Number:8152,State:13,Class:16
2025-07-25 16:00:27 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 523
ClientConnectionId:813b048c-9fee-4386-a4e3-8c7a3a1b3905
Error Number:8152,State:13,Class:16
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab caught stopping signal...
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc caught stopping signal...
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab All dispatchers stopped
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab successfully reported itself as stopped in 2.789 ms
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:920509ab has been stopped in total 358.7967 ms
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc All dispatchers stopped
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc successfully reported itself as stopped in 0.7976 ms
2025-07-25 16:05:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:24280:7cd30edc has been stopped in total 546.0205 ms
2025-07-25 16:06:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:06:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:06:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:06:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:06:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:06:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:06:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 successfully announced in 70.7374 ms
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 successfully announced in 70.5652 ms
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 all the dispatchers started
2025-07-25 16:06:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 all the dispatchers started
2025-07-25 16:06:24 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (5).xlsx"
2025-07-25 16:06:24 [Information] HuaLingErpApp.Controller.ItemController: Validation result: TotalRows=16, ValidCount=16, ErrorCount=0
2025-07-25 16:06:24 [Information] HuaLingErpApp.Controller.ItemController: Read 16 rows from Excel file
2025-07-25 16:06:24 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 541
ClientConnectionId:26677854-9ca2-4dba-bcad-5cc857970b54
Error Number:8152,State:13,Class:16
2025-07-25 16:08:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 caught stopping signal...
2025-07-25 16:08:54 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 caught stopping signal...
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 All dispatchers stopped
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 successfully reported itself as stopped in 1.8162 ms
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:2e9cf762 has been stopped in total 369.5686 ms
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 All dispatchers stopped
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 successfully reported itself as stopped in 0.8357 ms
2025-07-25 16:08:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25432:d99ccdf4 has been stopped in total 472.6373 ms
2025-07-25 16:09:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:09:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:09:09 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:09:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:09:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 successfully announced in 64.879 ms
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b successfully announced in 66.4436 ms
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b all the dispatchers started
2025-07-25 16:09:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 all the dispatchers started
2025-07-25 16:09:36 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 524
ClientConnectionId:76c62fdf-35ca-476a-a981-7c418f7a0175
Error Number:8152,State:13,Class:16
2025-07-25 16:14:19 [Error] HuaLingErpApp.Controller.ItemController: Failed to insert items to database
Microsoft.Data.SqlClient.SqlException (0x80131904): 将截断字符串或二进制数据。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemController.ImportExcelFile(IFormFile file) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemController.cs:line 524
ClientConnectionId:fe59c55c-8ae0-4451-bbac-a8c979ce2220
Error Number:8152,State:13,Class:16
2025-07-25 16:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b caught stopping signal...
2025-07-25 16:15:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 caught stopping signal...
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 caught stopped signal...
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b caught stopped signal...
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b All dispatchers stopped
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b successfully reported itself as stopped in 2.2086 ms
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:81979d8b has been stopped in total 539.127 ms
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 All dispatchers stopped
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 successfully reported itself as stopped in 0.7203 ms
2025-07-25 16:15:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11928:e03e0405 has been stopped in total 590.424 ms
2025-07-25 16:15:50 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:15:51 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:15:51 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:15:51 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:15:51 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d successfully announced in 63.9495 ms
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 successfully announced in 64.1022 ms
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d all the dispatchers started
2025-07-25 16:15:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 all the dispatchers started
2025-07-25 16:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d caught stopping signal...
2025-07-25 16:32:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 caught stopping signal...
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 caught stopped signal...
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d caught stopped signal...
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 All dispatchers stopped
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d All dispatchers stopped
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 successfully reported itself as stopped in 1.7733 ms
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:89a332b3 has been stopped in total 765.1758 ms
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d successfully reported itself as stopped in 0.7423 ms
2025-07-25 16:32:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19192:82011e0d has been stopped in total 766.3494 ms
2025-07-25 16:32:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:32:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:32:32 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:32:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:32:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 successfully announced in 59.0426 ms
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 successfully announced in 59.1859 ms
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 all the dispatchers started
2025-07-25 16:32:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 all the dispatchers started
2025-07-25 16:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 caught stopping signal...
2025-07-25 16:42:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 caught stopping signal...
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 caught stopped signal...
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 caught stopped signal...
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 All dispatchers stopped
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 All dispatchers stopped
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 successfully reported itself as stopped in 2.833 ms
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:48d8f9d8 has been stopped in total 533.1712 ms
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 successfully reported itself as stopped in 2.6084 ms
2025-07-25 16:42:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9120:02879803 has been stopped in total 537.3958 ms
2025-07-25 16:42:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:42:49 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:42:49 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:42:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:42:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc successfully announced in 88.1235 ms
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 successfully announced in 103.0243 ms
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 all the dispatchers started
2025-07-25 16:42:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc all the dispatchers started
2025-07-25 16:46:16 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate (4).xlsx"
2025-07-25 16:46:16 [Information] HuaLingErpApp.Controller.ItemController: Read 2622 rows from Excel file
2025-07-25 16:46:17 [Information] HuaLingErpApp.Controller.ItemController: Successfully imported 2606 items
2025-07-25 16:46:17 [Information] HuaLingErpApp.Controller.ItemController: Import completed: 2606 imported, 16 skipped, 0 errors
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 caught stopping signal...
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc caught stopping signal...
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc All dispatchers stopped
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc caught stopped signal...
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc successfully reported itself as stopped in 2.5739 ms
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:defe91dc has been stopped in total 503.1933 ms
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 caught stopped signal...
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 All dispatchers stopped
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 successfully reported itself as stopped in 1.4007 ms
2025-07-25 16:50:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25848:be2bbfc9 has been stopped in total 544.2182 ms
2025-07-25 16:52:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:52:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:52:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:52:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:52:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:52:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f successfully announced in 67.0782 ms
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 successfully announced in 66.9166 ms
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 all the dispatchers started
2025-07-25 16:52:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f all the dispatchers started
2025-07-25 16:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f caught stopping signal...
2025-07-25 16:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 caught stopping signal...
2025-07-25 16:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 All dispatchers stopped
2025-07-25 16:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 successfully reported itself as stopped in 1.8664 ms
2025-07-25 16:54:40 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:39415ae6 has been stopped in total 425.687 ms
2025-07-25 16:54:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f caught stopped signal...
2025-07-25 16:54:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f All dispatchers stopped
2025-07-25 16:54:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f successfully reported itself as stopped in 0.8178 ms
2025-07-25 16:54:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12940:9016791f has been stopped in total 551.8097 ms
2025-07-25 16:55:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:55:01 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:55:01 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:55:01 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:55:01 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 successfully announced in 65.1293 ms
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 successfully announced in 65.3062 ms
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 all the dispatchers started
2025-07-25 16:55:01 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 all the dispatchers started
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 caught stopping signal...
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 caught stopping signal...
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 caught stopped signal...
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 caught stopped signal...
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 All dispatchers stopped
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 successfully reported itself as stopped in 1.7385 ms
2025-07-25 16:56:26 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:05de22d2 has been stopped in total 762.2699 ms
2025-07-25 16:56:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 All dispatchers stopped
2025-07-25 16:56:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 successfully reported itself as stopped in 1.1277 ms
2025-07-25 16:56:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:8208:3055df32 has been stopped in total 931.5855 ms
2025-07-25 16:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:56:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:56:41 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:56:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:56:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 successfully announced in 65.9812 ms
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 successfully announced in 66.22 ms
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 all the dispatchers started
2025-07-25 16:56:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 all the dispatchers started
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 caught stopping signal...
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 caught stopping signal...
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 caught stopped signal...
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 caught stopped signal...
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 All dispatchers stopped
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 successfully reported itself as stopped in 1.9902 ms
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1e4c9288 has been stopped in total 750.2486 ms
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 All dispatchers stopped
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 successfully reported itself as stopped in 0.7529 ms
2025-07-25 16:57:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:25084:1d253a54 has been stopped in total 761.5177 ms
2025-07-25 16:57:43 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 16:57:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 16:57:44 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 16:57:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 16:57:44 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a successfully announced in 60.5346 ms
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 successfully announced in 65.23 ms
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 all the dispatchers started
2025-07-25 16:57:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a all the dispatchers started
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a caught stopping signal...
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 caught stopping signal...
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 All dispatchers stopped
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 successfully reported itself as stopped in 1.88 ms
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:38b49ba4 has been stopped in total 163.5121 ms
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a caught stopped signal...
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a All dispatchers stopped
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a successfully reported itself as stopped in 0.9313 ms
2025-07-25 17:00:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:27280:9e171c6a has been stopped in total 658.6945 ms
2025-07-25 17:01:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-25 17:01:00 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-25 17:01:00 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-25 17:01:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-25 17:01:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 successfully announced in 64.7006 ms
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d successfully announced in 64.8871 ms
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 all the dispatchers started
2025-07-25 17:01:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d all the dispatchers started
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d caught stopping signal...
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 caught stopping signal...
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 caught stopped signal...
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d caught stopped signal...
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 All dispatchers stopped
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 successfully reported itself as stopped in 2.6457 ms
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:9e08f2f9 has been stopped in total 738.3904 ms
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d All dispatchers stopped
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d successfully reported itself as stopped in 0.6917 ms
2025-07-25 17:01:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:28916:60aa943d has been stopped in total 747.8648 ms
