using System.Security.Claims;

namespace HuaLingErpApp.Services
{
    public interface ICurrentUserService
    {
        string GetCurrentUserName();
        int? GetCurrentUserId();
        bool IsAuthenticated();
        List<string> GetCurrentUserRoles();
    }

    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<CurrentUserService> _logger;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor, ILogger<CurrentUserService> logger)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
        }

        public string GetCurrentUserName()
        {
            try
            {
                var user = _httpContextAccessor.HttpContext?.User;
                if (user?.Identity?.IsAuthenticated == true)
                {
                    return user.Identity.Name ?? "Unknown";
                }
                return "System";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get current user name");
                return "System";
            }
        }

        public int? GetCurrentUserId()
        {
            try
            {
                var user = _httpContextAccessor.HttpContext?.User;
                if (user?.Identity?.IsAuthenticated == true)
                {
                    var userIdClaim = user.FindFirst(ClaimTypes.NameIdentifier);
                    if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                    {
                        return userId;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get current user ID");
                return null;
            }
        }

        public bool IsAuthenticated()
        {
            try
            {
                return _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to check authentication status");
                return false;
            }
        }

        public List<string> GetCurrentUserRoles()
        {
            try
            {
                var user = _httpContextAccessor.HttpContext?.User;
                if (user?.Identity?.IsAuthenticated == true)
                {
                    return user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
                }
                return new List<string>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get current user roles");
                return new List<string>();
            }
        }
    }
}
