<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BootstrapBlazor.TableExport" Version="9.2.6" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.20" />
    <PackageReference Include="Hangfire.Core" Version="1.8.20" />
    <PackageReference Include="Hangfire.SqlServer" Version="1.8.20" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
    <PackageReference Include="MiniExcel" Version="1.41.3" />
    <PackageReference Include="Serilog" Version="4.3.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.197" />

    <!-- Simple Authentication for internal use -->
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Cookies" Version="2.3.0" />

    <ProjectReference Include="..\HuaLingErpApp.Client\HuaLingErpApp.Client.csproj" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.7" />
    <ProjectReference Include="..\HuaLingErpApp.Shared\HuaLingErpApp.Shared.csproj" />
  </ItemGroup>
</Project>
