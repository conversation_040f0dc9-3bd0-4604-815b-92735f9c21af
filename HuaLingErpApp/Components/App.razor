@using System.Globalization
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <Link Href="libs/font-awesome/css/font-awesome.min.css" />
    <Link Href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" />
    <Link Href="_content/BootstrapBlazor/css/motronic.min.css" />
    <Link Href="app.css" />
    <Link Href="HuaLingErpApp.styles.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <title>Bootstrap Blazor Web App</title>
    <HeadOutlet />
</head>

<body>
    <Routes @rendermode="@(new InteractiveAutoRenderMode(prerender: false))" />
    <Script Src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"></Script>
    <script src="_framework/blazor.web.js"></script>
</body>

</html>
@code {

    protected override void OnInitialized()
    {
        // Force English culture for the entire application
        var culture = new CultureInfo("en-US");

        // Set all possible culture settings
        CultureInfo.DefaultThreadCurrentCulture = culture;
        CultureInfo.DefaultThreadCurrentUICulture = culture;
        CultureInfo.CurrentCulture = culture;
        CultureInfo.CurrentUICulture = culture;

        // Set thread cultures
        Thread.CurrentThread.CurrentCulture = culture;
        Thread.CurrentThread.CurrentUICulture = culture;
    }
}