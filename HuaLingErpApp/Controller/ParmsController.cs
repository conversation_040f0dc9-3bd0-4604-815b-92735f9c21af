using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Shared.Models;
using HuaLingErpApp.Shared;
using SqlSugar;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ParmsController : ControllerBase
    {
        private readonly ISqlSugarClient _db;
        private readonly ILogger<ParmsController> _logger;

        public ParmsController(ISqlSugarClient db, ILogger<ParmsController> logger)
        {
            _db = db;
            _logger = logger;
        }

        /// <summary>
        /// Get system parameters
        /// </summary>
        /// <returns>System parameters</returns>
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<Parms>>> GetParms()
        {
            try
            {
                var parms = await _db.Queryable<Parms>().FirstAsync();
                
                if (parms == null)
                {
                    return Ok(new HttpResponseModel<Parms>
                    {
                        IsSuccess = false,
                        Message = "No system parameters found"
                    });
                }

                return Ok(new HttpResponseModel<Parms>
                {
                    IsSuccess = true,
                    Data = parms,
                    Message = "System parameters retrieved successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving system parameters");
                return StatusCode(500, new HttpResponseModel<Parms>
                {
                    IsSuccess = false,
                    Message = $"Error retrieving system parameters: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Update system parameters
        /// </summary>
        /// <param name="id">Parameter ID</param>
        /// <param name="parms">Updated parameters</param>
        /// <returns>Update result</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<Parms>>> UpdateParms(int id, [FromBody] Parms parms)
        {
            try
            {
                if (id != parms.Id)
                {
                    return BadRequest(new HttpResponseModel<Parms>
                    {
                        IsSuccess = false,
                        Message = "ID mismatch"
                    });
                }

                // Check if the record exists
                var existingParms = await _db.Queryable<Parms>().Where(p => p.Id == id).FirstAsync();
                if (existingParms == null)
                {
                    return NotFound(new HttpResponseModel<Parms>
                    {
                        IsSuccess = false,
                        Message = "System parameters not found"
                    });
                }

                // Update audit fields
                parms.RecordDate = DateTime.Now;
                parms.ModifiedBy = User.Identity?.Name ?? "System";

                // Update the record
                var result = await _db.Updateable(parms).ExecuteCommandAsync();

                if (result > 0)
                {
                    // Retrieve the updated record
                    var updatedParms = await _db.Queryable<Parms>().Where(p => p.Id == id).FirstAsync();
                    
                    return Ok(new HttpResponseModel<Parms>
                    {
                        IsSuccess = true,
                        Data = updatedParms,
                        Message = "System parameters updated successfully"
                    });
                }
                else
                {
                    return BadRequest(new HttpResponseModel<Parms>
                    {
                        IsSuccess = false,
                        Message = "Failed to update system parameters"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating system parameters with ID: {Id}", id);
                return StatusCode(500, new HttpResponseModel<Parms>
                {
                    IsSuccess = false,
                    Message = $"Error updating system parameters: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Create initial system parameters if none exist
        /// </summary>
        /// <returns>Creation result</returns>
        [HttpPost("initialize")]
        public async Task<ActionResult<HttpResponseModel<Parms>>> InitializeParms()
        {
            try
            {
                // Check if parameters already exist
                var existingParms = await _db.Queryable<Parms>().FirstAsync();
                if (existingParms != null)
                {
                    return BadRequest(new HttpResponseModel<Parms>
                    {
                        IsSuccess = false,
                        Message = "System parameters already exist"
                    });
                }

                // Create initial parameters
                var newParms = new Parms
                {
                    BaseCurrCode = "USD",
                    CreatedDate = DateTime.Now,
                    RecordDate = DateTime.Now,
                    CreatedBy = User.Identity?.Name ?? "System",
                    ModifiedBy = User.Identity?.Name ?? "System"
                };

                var result = await _db.Insertable(newParms).ExecuteReturnEntityAsync();

                return Ok(new HttpResponseModel<Parms>
                {
                    IsSuccess = true,
                    Data = result,
                    Message = "System parameters initialized successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing system parameters");
                return StatusCode(500, new HttpResponseModel<Parms>
                {
                    IsSuccess = false,
                    Message = $"Error initializing system parameters: {ex.Message}"
                });
            }
        }
    }
}
