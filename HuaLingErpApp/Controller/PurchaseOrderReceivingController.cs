using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared;
using HuaLingErpApp.Shared.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using SqlSugar;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchaseOrderReceivingController(
        ILogger<PurchaseOrderReceivingController> logger,
        ISqlSugarClient db,
        IRepository<PoItem, int> poItemRepository,
        IRepository<PurchaseOrder, int> poRepository)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/purchaseorderreceiving/po/{poNum}
        // [HttpGet("po/{poNum}")]
        // public async Task<ActionResult<HttpResponseModel<List<PurchaseOrderReceivingDto>>>> GetByPoNumber(string poNum)
        // {
        //     try
        //     {
        //         // 获取指定采购单的行项目
        //         var poItems = await poItemRepository.QueryAsync(x => x.PoNum == poNum);
        //         
        //         // 转换为DTO
        //         var dtos = poItems.Select(item => new PurchaseOrderReceivingDto
        //         {
        //             PoNum = item.PoNum ?? string.Empty,
        //             PoLine = item.PoLine,
        //             Item = item.Item,
        //             ItemDescription = item.ItemDesc,
        //             QtyOrdered = item.QtyOrdered,
        //             QtyReceived = item.QtyReceived,
        //             QtyReceiving = 0, // 默认接收数量为0
        //             ReasonCode = null,
        //             ReasonDescription = null,
        //             Info = null,
        //             Location = item.Location,
        //             UnitOfMeasure = item.UnitOfMeasure
        //         }).ToList();
        //
        //         return HttpResponseModel<List<PurchaseOrderReceivingDto>>.Success(dtos);
        //     }
        //     catch (Exception ex)
        //     {
        //         logger.LogError(ex, $"Failed to retrieve receiving items for PO {poNum}");
        //         return StatusCode(500, 
        //             HttpResponseModel<object>.Error("An error occurred while retrieving receiving items"));
        //     }
        // }

        // POST: api/purchaseorderreceiving
        [HttpPost]
        // public async Task<ActionResult<HttpResponseModel<bool>>> PostReceiving([FromBody] List<PurchaseOrderReceivingDto> receivingItems)
        // {
        //     if (receivingItems == null || !receivingItems.Any())
        //     {
        //         return BadRequest(HttpResponseModel<object>.Error("No receiving items provided"));
        //     }
        //
        //     await _db.Ado.BeginTranAsync();
        //     try
        //     {
        //         var poNum = receivingItems.First().PoNum;
        //         
        //         // 验证所有项目是否属于同一个采购单
        //         if (receivingItems.Any(x => x.PoNum != poNum))
        //         {
        //             return BadRequest(HttpResponseModel<object>.Error("All items must belong to the same purchase order"));
        //         }
        //
        //         // 处理每个接收项目
        //         foreach (var item in receivingItems)
        //         {
        //             if (item.QtyReceiving == 0) continue; // 跳过数量为0的项目
        //
        //             // 1. 获取当前行项目
        //             var poItemQueryList = await poItemRepository.QueryAsync(x => 
        //                 x.PoNum == item.PoNum && x.PoLine == item.PoLine);
        //             var poItem = poItemQueryList.FirstOrDefault();
        //             if (poItem == null)
        //             {
        //                 return BadRequest(HttpResponseModel<object>.Error(
        //                     $"Line item {item.PoLine} not found in PO {item.PoNum}"));
        //             }
        //
        //             // 2. 更新接收数量
        //             var newReceivedQty = (poItem.QtyReceived) + item.QtyReceiving;
        //             
        //             // 验证接收数量是否合法
        //             if (newReceivedQty < 0)
        //             {
        //                 return BadRequest(HttpResponseModel<object>.Error(
        //                     $"Received quantity cannot be negative for line {item.PoLine}"));
        //             }
        //
        //             // 3. 更新采购单行项目
        //             poItem.QtyReceived = newReceivedQty;
        //             
        //
        //             // 4. 更新库位（如果需要）
        //             if (!string.IsNullOrEmpty(item.Location))
        //             {
        //                 poItem.Location = item.Location;
        //             }
        //
        //             // 5. 保存更新
        //             await poItemRepository.UpdateAsync(poItem);
        //         }
        //
        //         // 6. 更新采购单状态（可选）
        //         await UpdatePoStatus(poNum);
        //
        //         await _db.Ado.CommitTranAsync();
        //         return Ok(HttpResponseModel<bool>.Success(true, "Receiving processed successfully"));
        //     }
        //     catch (Exception ex)
        //     {
        //         await _db.Ado.RollbackTranAsync();
        //         logger.LogError(ex, "Failed to process receiving");
        //         return StatusCode(500, 
        //             HttpResponseModel<object>.Error($"An error occurred while processing receiving: {ex.Message}"));
        //     }
        // }

        // 更新采购单状态
        // private async Task UpdatePoStatus(string poNum)
        // {
        //     try
        //     {
        //         // 获取采购单的所有行项目
        //         var poItems = await poItemRepository.QueryAsync(x => x.PoNum == poNum);
        //         if (!poItems.Any()) return;
        //
        //         // 检查是否所有行项目都已完全接收
        //         bool allFullyReceived = poItems.All(x => 
        //             (x.QtyReceived) >= x.QtyOrdered);
        //
        //         // 获取采购单
        //         var poQueryList = await poRepository.QueryAsync(x => x.PoNum == poNum);
        //         var po = poQueryList.FirstOrDefault();
        //         if (po == null) return;
        //
        //         // 更新状态
        //         if (allFullyReceived)
        //         {
        //             po.Status = EnumStatus.C; // 假设有一个表示"已完成"的状态
        //         }
        //         else
        //         {
        //             po.Status = EnumStatus.P; // 假设有一个表示"部分接收"的状态
        //         }
        //
        //         await poRepository.UpdateAsync(po);
        //     }
        //     catch (Exception ex)
        //     {
        //         logger.LogError(ex, $"Failed to update PO {poNum} status");
        //         // 这里可以选择记录错误但继续执行，因为状态更新不是关键操作
        //     }
        // }

        // GET: api/purchaseorderreceiving/po-list
        [HttpGet("po-list")]
        public async Task<ActionResult<HttpResponseModel<List<PoInfoDto>>>> GetPoList()
        {
            try
            {
                // 获取所有采购单号
                var poList = await _db.Queryable<PurchaseOrder>()
                    .OrderByDescending(x => x.PoNum)
                    .Select(x => new PoInfoDto
                    {
                        PoNum = x.PoNum,
                        VendorNum = x.VendorNum,
                        PoDate = x.PoDate,
                        Status = x.Status
                    })
                    .ToListAsync();

                return HttpResponseModel<List<PoInfoDto>>.Success(poList);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve PO list");
                return StatusCode(500, 
                    HttpResponseModel<object>.Error($"An error occurred while retrieving PO list: {ex.Message}"));
            }
        }

        // POST: api/purchaseorderreceiving/process-sp
        [HttpPost("process-sp")]
        public async Task<ActionResult<HttpResponseModel<List<PurchaseOrderReceivingDto>>>> ProcessReceivingSP([FromBody] List<PurchaseOrderReceivingDto> receivingItems)
        {
            if (receivingItems == null || !receivingItems.Any())
            {
                return BadRequest(HttpResponseModel<object>.Error("No receiving items provided"));
            }

            try
            {
                var results = new List<PurchaseOrderReceivingDto>();
                
                foreach (var item in receivingItems)
                {
                    var PoNumP = new SugarParameter("@po_num", item.PoNum);
                    var PoLineP = new SugarParameter("@po_line", item.PoLine);
                    var TransDateP = new SugarParameter("@trans_date", item.TransDate);
                    var LocationP = new SugarParameter("@loc", item.Location);
                    var QtyReceivingP = new SugarParameter("@qty_receiving", item.QtyReceiving);
                    var ReasonCodeP = new SugarParameter("@reason_code", item.ReasonCode);
                    var UnitOfMeasureP = new SugarParameter("@u_m", item.UnitOfMeasure);
                    var InfoP = new SugarParameter("@info", null, true); // 设置为output
                    
                    await _db.Ado.UseStoredProcedure().GetDataTableAsync("PurchaseOrderReceivingSP", 
                        PoNumP, PoLineP, TransDateP, LocationP, QtyReceivingP, ReasonCodeP, UnitOfMeasureP, InfoP);
                    
                    // 更新 Info 字段
                    item.Info = InfoP.Value?.ToString();
                    results.Add(item);
                }

                return Ok(HttpResponseModel<List<PurchaseOrderReceivingDto>>.Success(results, "Stored procedure executed successfully"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to execute stored procedure");
                return StatusCode(500, 
                    HttpResponseModel<object>.Error($"An error occurred while executing stored procedure: {ex.Message}"));
            }
        }
    }

    public class PoInfoDto
    {
        public string? PoNum { get; set; }
        public string? VendorNum { get; set; }
        public DateTime? PoDate { get; set; }
        public EnumStatus? Status { get; set; }
    }
}