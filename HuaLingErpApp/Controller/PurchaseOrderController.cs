using Microsoft.AspNetCore.Mvc; 
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchaseOrderController(
        IRepository<PurchaseOrder, int> repository,
        ILogger<PurchaseOrderController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly IRepository<PurchaseOrder, int> _repository = repository;
        private readonly ILogger<PurchaseOrderController> _logger = logger;
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/purchaseorder
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<PurchaseOrder>>>> GetAll() {
            try {
                var items = await _repository.GetDataAsync();
                return Ok( HttpResponseModel<List<PurchaseOrder>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to retrieve purchase order list");
                return StatusCode(500,
                    HttpResponseModel<List<PurchaseOrder>>.Error($"An error occurred while retrieving purchase orders: {ex.Message}"));
            }
        }

        // GET: api/purchaseorder/5
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<PurchaseOrder>>> GetById(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<PurchaseOrder>.Error("Purchase order not found"));
                }

                return Ok(HttpResponseModel<PurchaseOrder>.Success(item));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve purchase order {id}");
                return StatusCode(500,
                    HttpResponseModel<PurchaseOrder>.Error($"An error occurred while retrieving the purchase order: {ex.Message}"));
            }
        }

        // POST: api/purchaseorder
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<PurchaseOrder>>> Create([FromBody] PurchaseOrder model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if PO number already exists
                var exists = await _repository.AnyAsync(p => p.PoNum == model.PoNum);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("PO number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await _repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<PurchaseOrder>.Success(result, "Purchase order created successfully")
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to create purchase order");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the purchase order: {ex.Message}"));
            }
        }

        // PUT: api/purchaseorder/5
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(int id, [FromBody] PurchaseOrder model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await _repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Purchase order not found"));
                }

                // Check if PO number is used by other records
                var exists = await _repository.AnyAsync(p => p.Id != id && p.PoNum == model.PoNum);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("PO number is already in use by another record"));
                }

                // Update business fields
                existing.PoNum = model.PoNum;
                existing.VendorNum = model.VendorNum;
                existing.CurrencyCode = model.CurrencyCode;
                existing.TermsCode = model.TermsCode;
                existing.TaxCode = model.TaxCode;
                existing.Contact = model.Contact;
                existing.Phone = model.Phone;
                existing.Email = model.Email;
                existing.Address = model.Address;
                existing.Status = model.Status;
                existing.TaxRate = model.TaxRate;
                existing.ExchangeRate = model.ExchangeRate;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                // CreatedBy and CreatedDate will be preserved
                var success = await _repository.UpdateAsync(existing);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update purchase order"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Purchase order updated successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to update purchase order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the purchase order: {ex.Message}"));
            }
        }

        // DELETE: api/purchaseorder/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Purchase order not found"));
                }

                var success = await _repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete purchase order"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Purchase order deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to delete purchase order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the purchase order: {ex.Message}"));
            }
        }

        // DELETE: api/purchaseorder/batch
        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }
                var table = await _repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more purchase not found"));
                }
                var success = await _repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete purchase orders"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Purchase orders deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete purchase orders in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting purchase orders: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取所有采购单号
        /// </summary>
        /// <returns>采购单号列表</returns>
        [HttpGet("ponums")]
        public async Task<ActionResult<HttpResponseModel<List<string?>>>> GetAllPoNums() {
            try {
                // 使用 Select 只查询 PoNum 字段
                var po = await _repository.GetDataAsync();

                var poNums = po.Select(x => x.PoNum)
                    .Where(poNum => !string.IsNullOrEmpty(poNum))
                    .OrderBy(poNum => poNum).ToList();
                return Ok(HttpResponseModel<List<string?>>.Success(poNums));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "An error occurred while retrieving purchase order numbers");
                return StatusCode(500,
                    HttpResponseModel<List<PurchaseOrder>>.Error($"An error occurred while retrieving purchase order numbers: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试审计功能 - 获取采购单的审计信息
        /// </summary>
        /// <param name="id">采购单ID</param>
        /// <returns>包含审计信息的采购单详情</returns>
        [HttpGet("{id}/audit")]
        public async Task<ActionResult<HttpResponseModel<object>>> GetAuditInfo(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Purchase order not found"));
                }

                var auditInfo = new {
                    Id = item.Id,
                    PoNum = item.PoNum,
                    AuditInfo = new {
                        CreatedBy = item.CreatedBy,
                        CreatedDate = item.CreatedDate,
                        ModifiedBy = item.ModifiedBy,
                        RecordDate = item.RecordDate
                    },
                    BusinessData = new {
                        VendorNum = item.VendorNum,
                        Status = item.Status,
                        Contact = item.Contact
                    }
                };

                return Ok(HttpResponseModel<object>.Success(auditInfo, "Audit information retrieved successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve audit info for purchase order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving audit information: {ex.Message}"));
            }
        }
    }
}