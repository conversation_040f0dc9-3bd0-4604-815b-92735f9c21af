using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/locations")]
    [Authorize]
    public class LocationController(
        IRepository<Location, string> repository,
        ILogger<LocationController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<Location>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<Location>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve locations list");
                return StatusCode(500,
                    HttpResponseModel<List<Location>>.Error($"An error occurred while retrieving locations: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<Location>>> GetById(string id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<Location>.Error("Location not found"));
                }
                return Ok(HttpResponseModel<Location>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve location {id}");
                return StatusCode(500,
                    HttpResponseModel<Location>.Error($"An error occurred while retrieving the location: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<Location>>> Create([FromBody] Location model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Location code already exists"));
                }

                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<Location>.Success(result, "Location created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create location");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the location: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] Location model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id)
                {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Location not found"));
                }

                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                var success = await repository.UpdateAsync(model);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the location"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the location: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Location not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the location"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the location: {ex.Message}"));
            }
        }
        
        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<string>? ids)
        {
            try
            {
                if (ids is null || ids.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No location IDs provided for batch deletion"));
                }
                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more locations not found"));
                }
                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete locations"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "locations deleted successfully"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to delete locations in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting locations in batch: {ex.Message}"));
            }
        }
    }
}