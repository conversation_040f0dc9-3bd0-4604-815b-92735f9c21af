using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PurchaseOrderLinesController(
        IRepository<PoItem, int> repository,
        ILogger<PurchaseOrderLinesController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly IRepository<PoItem, int> _repository = repository;
        private readonly ILogger<PurchaseOrderLinesController> _logger = logger;
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/purchaseorderlines
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<PoItem>>>> GetAll() {
            try {
                var items = await _repository.GetDataAsync();
                return Ok(HttpResponseModel<List<PoItem>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to retrieve purchase order lines");
                return StatusCode(500,
                    HttpResponseModel<List<PoItem>>.Error($"An error occurred while retrieving purchase order lines: {ex.Message}"));
            }
        }

        // GET: api/purchaseorderlines/5
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<PoItem>>> GetById(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<PoItem>.Error("Purchase order line not found"));
                }

                return HttpResponseModel<PoItem>.Success(item);
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve purchase order line {id}");
                return StatusCode(500,
                    HttpResponseModel<PoItem>.Error($"An error occurred while retrieving the purchase order line: {ex.Message}"));
            }
        }

        // GET: api/purchaseorderlines/byponum/PO12345
        [HttpGet("byponum/{poNum}")]
        public async Task<ActionResult<HttpResponseModel<List<PoItem>>>> GetByPoNum(string poNum)
        {
            try
            {
                var items = await _repository.QueryAsync(x => x.PoNum == poNum);
                if (items.Count == 0)
                {
                    return NotFound(HttpResponseModel<List<PoItem>>.Error($"No purchase order lines found for PO number: {poNum}"));
                }

                return Ok(HttpResponseModel<List<PoItem>>.Success(items));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to retrieve purchase order lines for PO number: {poNum}");
                return StatusCode(500,
                    HttpResponseModel<List<PoItem>>.Error($"An error occurred while retrieving purchase order lines for PO number: {poNum}"));
            }
        }

        // POST: api/purchaseorderlines
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<PoItem>>> Create([FromBody] PoItem model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if PO line already exists for this PO
                var exists = await _repository.AnyAsync(p => p.PoNum == model.PoNum && p.PoLine == model.PoLine);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("A line with this PO number and line number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await _repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<PoItem>.Success(result, "Purchase order line created successfully")
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to create purchase order line");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the purchase order line: {ex.Message}"));
            }
        }

        // PUT: api/purchaseorderlines/5
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(int id, [FromBody] PoItem model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await _repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Purchase order line not found"));
                }

                // Check if PO number and line combination is used by other records
                var exists =
                    await _repository.AnyAsync(p => p.Id != id && p.PoNum == model.PoNum && p.PoLine == model.PoLine);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("This PO number and line combination is already in use"));
                }

                // Update fields
                existing.PoNum = model.PoNum;
                existing.PoLine = model.PoLine;
                existing.Item = model.Item;
                existing.QtyOrdered = model.QtyOrdered;
                existing.QtyReceived = model.QtyReceived;
                existing.QtyVoucher = model.QtyVoucher;
                existing.UnitCost = model.UnitCost;
                existing.Status = model.Status;
                existing.DueDate = model.DueDate;
                existing.UnitCostReceived = model.UnitCostReceived;
                existing.Location = model.Location;
                existing.UnitOfMeasure = model.UnitOfMeasure;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                // CreatedBy and CreatedDate will be preserved
                var success = await _repository.UpdateAsync(existing);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update purchase order line"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Purchase order line updated successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to update purchase order line {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the purchase order line: {ex.Message}"));
            }
        }

        // DELETE: api/purchaseorderlines/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Purchase order line not found"));
                }

                // Add business logic validations here if needed
                // For example, prevent deletion if already received

                var success = await _repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete purchase order line"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Purchase order line deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to delete purchase order line {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the purchase order line: {ex.Message}"));
            }
        }

        // DELETE: api/purchaseorderlines/batch
        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No line items selected for deletion"));
                }

                // Add any business validations here before deletion
                var table = await _repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more line items not found"));
                }
                var success = await _repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete selected line items"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Selected line items deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete purchase order lines in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the line items: {ex.Message}"));
            }
        }
    }
}