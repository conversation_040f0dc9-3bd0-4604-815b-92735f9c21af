using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/matltranamt")]
    [Authorize]
    public class MatlTranAmtController(
        ILogger<MatlTranAmtController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        /// <summary>
        /// Get all material transaction amounts (read-only)
        /// </summary>
        /// <returns>List of material transaction amounts</returns>
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<MatlTranAmt>>>> GetAll()
        {
            try
            {
                var items = await _db.Queryable<MatlTranAmt>()
                    .OrderBy(x => x.TransNum)
                    .OrderBy(x => x.TransSeq)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTranAmt>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve material transaction amounts");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTranAmt>>.Error($"An error occurred while retrieving material transaction amounts: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transaction amount by composite key (read-only)
        /// </summary>
        /// <param name="transNum">Transaction number</param>
        /// <param name="transSeq">Transaction sequence</param>
        /// <returns>Material transaction amount</returns>
        [HttpGet("{transNum}/{transSeq}")]
        public async Task<ActionResult<HttpResponseModel<MatlTranAmt>>> GetByCompositeKey(int transNum, byte transSeq)
        {
            try
            {
                var item = await _db.Queryable<MatlTranAmt>()
                    .Where(x => x.TransNum == transNum && x.TransSeq == transSeq)
                    .FirstAsync();

                if (item == null)
                {
                    return NotFound(HttpResponseModel<MatlTranAmt>.Error("Material transaction amount not found"));
                }

                return Ok(HttpResponseModel<MatlTranAmt>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transaction amount with TransNum: {transNum}, TransSeq: {transSeq}");
                return StatusCode(500,
                    HttpResponseModel<MatlTranAmt>.Error($"An error occurred while retrieving the material transaction amount: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transaction amounts by transaction number (read-only)
        /// </summary>
        /// <param name="transNum">Transaction number</param>
        /// <returns>List of material transaction amounts for the transaction</returns>
        [HttpGet("bytransnum/{transNum}")]
        public async Task<ActionResult<HttpResponseModel<List<MatlTranAmt>>>> GetByTransNum(int transNum)
        {
            try
            {
                var items = await _db.Queryable<MatlTranAmt>()
                    .Where(x => x.TransNum == transNum)
                    .OrderBy(x => x.TransSeq)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTranAmt>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transaction amounts for TransNum: {transNum}");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTranAmt>>.Error($"An error occurred while retrieving material transaction amounts: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get material transaction amounts by account (read-only)
        /// </summary>
        /// <param name="acct">Account code</param>
        /// <returns>List of material transaction amounts for the account</returns>
        [HttpGet("byaccount/{acct}")]
        public async Task<ActionResult<HttpResponseModel<List<MatlTranAmt>>>> GetByAccount(string acct)
        {
            try
            {
                var items = await _db.Queryable<MatlTranAmt>()
                    .Where(x => x.Acct == acct)
                    .OrderBy(x => x.TransNum)
                    .OrderBy(x => x.TransSeq)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<MatlTranAmt>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve material transaction amounts for Account: {acct}");
                return StatusCode(500,
                    HttpResponseModel<List<MatlTranAmt>>.Error($"An error occurred while retrieving material transaction amounts: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get summary statistics (read-only)
        /// </summary>
        /// <returns>Summary statistics</returns>
        [HttpGet("summary")]
        public async Task<ActionResult<HttpResponseModel<object>>> GetSummary()
        {
            try
            {
                var totalRecords = await _db.Queryable<MatlTranAmt>().CountAsync();
                var totalAmount = await _db.Queryable<MatlTranAmt>()
                    .Where(x => x.Amt.HasValue)
                    .SumAsync(x => x.Amt.Value);
                var uniqueTransactions = await _db.Queryable<MatlTranAmt>()
                    .GroupBy(x => x.TransNum)
                    .CountAsync();
                var uniqueAccounts = await _db.Queryable<MatlTranAmt>()
                    .Where(x => !string.IsNullOrEmpty(x.Acct))
                    .GroupBy(x => x.Acct)
                    .CountAsync();

                var summary = new
                {
                    TotalRecords = totalRecords,
                    TotalAmount = totalAmount,
                    UniqueTransactions = uniqueTransactions,
                    UniqueAccounts = uniqueAccounts
                };

                return Ok(HttpResponseModel<object>.Success(summary));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve material transaction amounts summary");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving summary: {ex.Message}"));
            }
        }
    }
}
