using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/customers")]
    [Authorize]
    public class CustomerController(
        IRepository<Customer, int> repository,
        ILogger<CustomerController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<Customer>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<Customer>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve customers list");
                return StatusCode(500,
                    HttpResponseModel<List<Customer>>.Error($"An error occurred while retrieving customers: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<Customer>>> GetById(int id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<Customer>.Error("Customer not found"));
                }
                return Ok(HttpResponseModel<Customer>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve customer {id}");
                return StatusCode(500,
                    HttpResponseModel<Customer>.Error($"An error occurred while retrieving the customer: {ex.Message}"));
            }
        }

        [HttpGet("by-custnum/{custNum}")]
        public async Task<ActionResult<HttpResponseModel<Customer>>> GetByCustNum(string custNum)
        {
            try
            {
                var item = await repository.QueryAsync(c => c.CustNum == custNum);
                
                if (item == null)
                {
                    return NotFound(HttpResponseModel<Customer>.Error("Customer not found"));
                }
                return Ok(HttpResponseModel<Customer>.Success(item.First()));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve customer {custNum}");
                return StatusCode(500,
                    HttpResponseModel<Customer>.Error($"An error occurred while retrieving the customer: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<Customer>>> Create([FromBody] Customer model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if customer number already exists
                var exists = await repository.AnyAsync(p => p.CustNum == model.CustNum);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Customer number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<Customer>.Success(result, "Customer created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create customer");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the customer: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] Customer model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id)
                {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Customer not found"));
                }

                // Check if customer number already exists for other records
                var custNumExists = await repository.AnyAsync(p => p.CustNum == model.CustNum && p.Id != id);
                if (custNumExists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Customer number already exists"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the customer"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update customer {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the customer: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Customer not found"));
                }

                // Check if the customer is being used in other tables
                // Uncomment and modify as needed based on your business logic
                // var isUsed = await _db.Queryable<SalesOrder>()
                //     .AnyAsync(x => x.CustNum == existing.CustNum);
                //
                // if (isUsed)
                // {
                //     return BadRequest(HttpResponseModel<object>.Error(
                //         "Cannot delete customer as it is being used in sales orders"));
                // }

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the customer"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete customer {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the customer: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<int>? ids)
        {
            try
            {
                if (ids is null || ids.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No customer IDs provided for batch deletion"));
                }

                // Check if any of the customers are being used
                // Uncomment and modify as needed based on your business logic
                // var usedCustomers = await _db.Queryable<SalesOrder>()
                //     .InnerJoin<Customer>((so, c) => so.CustNum == c.CustNum)
                //     .Where((so, c) => ids.Contains(c.Id))
                //     .Select((so, c) => c.CustNum)
                //     .Distinct()
                //     .ToListAsync();
                //
                // if (usedCustomers.Any())
                // {
                //     return BadRequest(HttpResponseModel<object>.Error(
                //         $"Cannot delete customers as they are being used: {string.Join(", ", usedCustomers)}"));
                // }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more customers not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more customers"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to delete customers in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting customers in batch: {ex.Message}"));
            }
        }

        [HttpGet("custnums")]
        public async Task<ActionResult<HttpResponseModel<List<string>>>> GetCustomerNumbers()
        {
            try
            {
                var custNums = await _db.Queryable<Customer>()
                    .Select(c => c.CustNum)
                    .ToListAsync();
                return Ok(HttpResponseModel<List<string>>.Success(custNums));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve customer numbers");
                return StatusCode(500,
                    HttpResponseModel<List<string>>.Error($"An error occurred while retrieving customer numbers: {ex.Message}"));
            }
        }
    }
}
