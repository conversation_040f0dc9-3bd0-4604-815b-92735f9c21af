using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/units")]
    [Authorize]
    public class UMController(
        IRepository<UnitOfMeasure, string> repository,
        ILogger<UMController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<UnitOfMeasure>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<UnitOfMeasure>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve units of measure list");
                return StatusCode(500,
                    HttpResponseModel<List<UnitOfMeasure>>.Error($"An error occurred while retrieving units of measure: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<UnitOfMeasure>>> GetById(string id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<UnitOfMeasure>.Error("Unit of measure not found"));
                }
                return Ok(HttpResponseModel<UnitOfMeasure>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve unit of measure {id}");
                return StatusCode(500,
                    HttpResponseModel<UnitOfMeasure>.Error($"An error occurred while retrieving the unit of measure: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<UnitOfMeasure>>> Create([FromBody] UnitOfMeasure model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Unit of measure already exists"));
                }
                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<UnitOfMeasure>.Success(result, "Unit of measure created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create unit of measure");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the unit of measure: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] UnitOfMeasure model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id)
                {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Unit of measure not found"));
                }
                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the unit of measure"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update unit of measure {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the unit of measure: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Unit of measure not found"));
                }

                // // Check if the unit of measure is being used
                // var isUsed = await _db.Queryable<ItemLocation>()
                //     .AnyAsync(x => x.UnitOfMeasure == id);
                //
                // if (isUsed)
                // {
                //     return BadRequest(HttpResponseModel<object>.Error(
                //         "Cannot delete unit of measure as it is being used in item locations"));
                // }

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the unit of measure"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete unit of measure {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the unit of measure: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<string>? ids)
        {
            try
            {
                if (ids is null || ids.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No unit of measure IDs provided for batch deletion"));
                }

                // // Check if any of the units are being used
                // var usedUnits = await _db.Queryable<ItemLocation>()
                //     .Where(x => ids.Contains(x.UnitOfMeasure))
                //     .Select(x => x.UnitOfMeasure)
                //     .Distinct()
                //     .ToListAsync();
                //
                // if (usedUnits.Any())
                // {
                //     return BadRequest(HttpResponseModel<object>.Error(
                //         $"Cannot delete units of measure as they are being used: {string.Join(", ", usedUnits)}"));
                // }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more units of measure not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more units of measure"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to delete units of measure in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting units of measure in batch: {ex.Message}"));
            }
        }
    }
}