using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/itemlocations")]
    [Authorize]
    public class ItemLocationController(
        IRepository<ItemLocation, int> repository,
        ILogger<ItemLocationController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve item locations list");
                return StatusCode(500,
                    HttpResponseModel<List<ItemLocation>>.Error($"An error occurred while retrieving item locations: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<ItemLocation>>> GetById(int id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<ItemLocation>.Error("Item location not found"));
                }
                return Ok(HttpResponseModel<ItemLocation>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve item location {id}");
                return StatusCode(500,
                    HttpResponseModel<ItemLocation>.Error($"An error occurred while retrieving the item location: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<ItemLocation>>> Create([FromBody] ItemLocation model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if the item-location combination already exists
                var exists = await repository
                    .AnyAsync(x => x.Item == model.Item && x.Location == model.Location);
                
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Item-location combination already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<ItemLocation>.Success(result, "Item location created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create item location");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the item location: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] ItemLocation model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id)
                {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Item location not found"));
                }

                // Check if the item-location combination already exists for a different record
                var duplicate = await repository
                    .AnyAsync(x => x.Id != id && 
                                 x.Item == model.Item && 
                                 x.Location == model.Location);
                
                if (duplicate)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Item-location combination already exists"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the item location"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update item location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the item location: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Item location not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the item location"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete item location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the item location: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<int>? ids)
        {
            try
            {
                if (ids is null || !ids.Any())
                {
                    return BadRequest(HttpResponseModel<object>.Error("No item location IDs provided for batch deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
               var existingCount = table.Count;

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more item locations not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more item locations"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to delete item locations in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting item locations in batch: {ex.Message}"));
            }
        }
        
        [HttpGet("by-item/{item}")]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetByItem(string item)
        {
            try {
                var items = await repository
                    .QueryAsync(x => x.Item == item);

                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve locations for item {item}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving item locations: {ex.Message}"));
            }
        }

        [HttpGet("by-location/{location}")]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetByLocation(string location)
        {
            try {
                var items = await repository
                    .QueryAsync(x => x.Location == location);

                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve items for location {location}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving location items: {ex.Message}"));
            }
        }
    }
}