using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/unitcode1")]
    [Authorize]
    public class UnitCode1Controller(
        IRepository<UnitCode1, string> repository,
        ILogger<UnitCode1Controller> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<UnitCode1>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<UnitCode1>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve unit code 1 list");
                return StatusCode(500,
                    HttpResponseModel<List<UnitCode1>>.Error($"An error occurred while retrieving unit codes 1: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<UnitCode1>>> GetById(string id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<UnitCode1>.Error("Unit code 1 not found"));
                }

                return Ok(HttpResponseModel<UnitCode1>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve unit code 1 with ID: {id}");
                return StatusCode(500,
                    HttpResponseModel<UnitCode1>.Error($"An error occurred while retrieving the unit code 1: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<UnitCode1>>> Create([FromBody] UnitCode1 model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if unit code already exists
                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Unit code 1 already exists"));
                }

                // Audit fields will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<UnitCode1>.Success(result, "Unit code 1 created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create unit code 1");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the unit code 1: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(string id, [FromBody] UnitCode1 model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Unit code 1 not found"));
                }

                // Check if unit code is used by other records
                var exists = await repository.AnyAsync(p => p.Id != id && p.Id == model.Id);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Unit code 1 is already in use by another record"));
                }

                // Update business fields
                existing.Id = model.Id;
                existing.Name = model.Name;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(existing);
                if (!success)
                {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update unit code 1"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Unit code 1 updated successfully"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update unit code 1 with ID: {id}");
                return StatusCode(500,
                    HttpResponseModel<bool>.Error($"An error occurred while updating the unit code 1: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(string id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Unit code 1 not found"));
                }

                // Add any business validations here before deletion
                // For example, check if this unit code is referenced by other entities

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete unit code 1"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Unit code 1 deleted successfully"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete unit code 1 with ID: {id}");
                return StatusCode(500,
                    HttpResponseModel<bool>.Error($"An error occurred while deleting the unit code 1: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<string>? ids)
        {
            try
            {
                if (ids is null || ids.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No unit codes selected for deletion"));
                }

                // Add any business validations here before deletion
                var existingCount = await _db.Queryable<UnitCode1>()
                    .Where(x => ids.Contains(x.Id))
                    .CountAsync();

                if (existingCount != ids.Count)
                {
                    return NotFound(HttpResponseModel<object>.Error("One or more unit codes not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success)
                {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete selected unit codes"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, $"Successfully deleted {ids.Count} unit codes"));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to delete unit codes in batch");
                return StatusCode(500,
                    HttpResponseModel<bool>.Error($"An error occurred while deleting unit codes: {ex.Message}"));
            }
        }
    }
}
