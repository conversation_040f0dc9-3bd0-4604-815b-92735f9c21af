using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using HuaLingErpApp.Shared;
using HuaLingErpApp.Shared.Models;

namespace HuaLingErpApp.Client.Services
{
    public class CustomAuthStateProvider : AuthenticationStateProvider
    {
        private readonly ApiService _apiService;
        private readonly ILogger<CustomAuthStateProvider> _logger;
        private AuthenticationState _currentAuthState;

        public CustomAuthStateProvider(ApiService apiService, ILogger<CustomAuthStateProvider> logger)
        {
            _apiService = apiService;
            _logger = logger;
            _currentAuthState = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                // Check authentication status from server
                var response = await _apiService.GetAsync<HttpResponseModel<object>>("api/auth/status");

                if (response?.IsSuccess == true && response.Data != null)
                {
                    var statusData = System.Text.Json.JsonSerializer.Deserialize<AuthStatusData>(
                        response.Data.ToString() ?? "", 
                        new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    if (statusData?.IsAuthenticated == true && !string.IsNullOrEmpty(statusData.UserName))
                    {
                        var claims = new List<Claim>
                        {
                            new(ClaimTypes.Name, statusData.UserName)
                        };

                        // Add role claims
                        if (statusData.Roles != null)
                        {
                            foreach (var role in statusData.Roles)
                            {
                                claims.Add(new Claim(ClaimTypes.Role, role));
                            }
                        }

                        var identity = new ClaimsIdentity(claims, "cookie");
                        var user = new ClaimsPrincipal(identity);
                        _currentAuthState = new AuthenticationState(user);
                        return _currentAuthState;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get authentication status from server");
                // 如果是认证错误，清除本地状态
                if (ex.Message.Contains("401") || ex.Message.Contains("Authentication"))
                {
                    NotifyUserLogout();
                }
            }

            // Return unauthenticated state
            _currentAuthState = new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            return _currentAuthState;
        }

        public async Task<bool> LoginAsync(LoginRequest loginRequest)
        {
            try
            {
                var response = await _apiService.PostAsync("api/auth/login", loginRequest);
                
                if (response.IsSuccessStatusCode)
                {
                    // Refresh authentication state
                    var newAuthState = await GetAuthenticationStateAsync();
                    _currentAuthState = newAuthState;
                    NotifyAuthenticationStateChanged(Task.FromResult(newAuthState));
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login failed");
            }

            return false;
        }

        public async Task LogoutAsync()
        {
            try
            {
                await _apiService.PostAsync<object>("api/auth/logout", null);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Logout request failed, but continuing with local logout");
            }
            finally
            {
                // Always update local state to unauthenticated
                var anonymousUser = new ClaimsPrincipal(new ClaimsIdentity());
                _currentAuthState = new AuthenticationState(anonymousUser);
                NotifyAuthenticationStateChanged(Task.FromResult(_currentAuthState));
            }
        }

        public void NotifyUserAuthentication(string userName, List<string> roles)
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.Name, userName)
            };

            foreach (var role in roles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role));
            }

            var identity = new ClaimsIdentity(claims, "cookie");
            var user = new ClaimsPrincipal(identity);
            _currentAuthState = new AuthenticationState(user);
            NotifyAuthenticationStateChanged(Task.FromResult(_currentAuthState));
        }

        public void NotifyUserLogout()
        {
            var anonymousUser = new ClaimsPrincipal(new ClaimsIdentity());
            _currentAuthState = new AuthenticationState(anonymousUser);
            NotifyAuthenticationStateChanged(Task.FromResult(_currentAuthState));
        }
    }

    public class AuthStatusData
    {
        public bool IsAuthenticated { get; set; }
        public string? UserName { get; set; }
        public List<string>? Roles { get; set; }
    }
}
