<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
    <StaticWebAssetProjectMode>Default</StaticWebAssetProjectMode>
      <BlazorWebAssemblyLoadAllGlobalizationData>true</BlazorWebAssemblyLoadAllGlobalizationData>
  </PropertyGroup>
 
  <ItemGroup>
    <PackageReference Include="BootstrapBlazor" Version="9.8.2" />
    <PackageReference Include="BootstrapBlazor.TableExport" Version="9.2.6" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Localization" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="9.0.7" />
    <PackageReference Include="MiniExcel" Version="1.41.3" />

  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Locales\en.json" />
    <_ContentIncludedByDefault Remove="Locales\zh.json" />
    <_ContentIncludedByDefault Remove="Pages\SearchTemplates\PurchaseOrderLinesSearch.razor" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HuaLingErpApp.Shared\HuaLingErpApp.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Pages\Dialogs\" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Microsoft.AspNetCore.Localization">
      <HintPath>..\..\Program Files\dotnet\shared\Microsoft.AspNetCore.App\9.0.5\Microsoft.AspNetCore.Localization.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
