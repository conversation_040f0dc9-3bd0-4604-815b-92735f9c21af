{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "BootstrapBlazorOptions": {"ToastDelay": 4000, "MessageDelay": 4000, "SwalDelay": 4000, "EnableErrorLogger": true, "FallbackCulture": "en-US", "SupportedCultures": ["en-US"], "DefaultCultureInfo": "en-US", "TableSettings": {"CheckboxColumnWidth": 36}, "IgnoreLocalizerMissing": true, "StepSettings": {"Short": 1, "Int": 1, "Long": 1, "Float": "0.1", "Double": "0.01", "Decimal": "0.01"}}}