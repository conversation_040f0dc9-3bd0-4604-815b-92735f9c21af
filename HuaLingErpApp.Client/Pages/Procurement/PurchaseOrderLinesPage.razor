@page "/procurement/purchaseorderlines"
@page "/procurement/purchaseorderlines/{PoNum}"
@using HuaLingErpApp.Client.Pages.Procurement.Components
@using HuaLingErpApp.Client.Components
@using HuaLingErpApp.Shared
@inject MessageService MessageService
@inject ApiService Api

<SecurePage RequiredRoles="Administrator,Manager" PageName="Purchase Order Lines">
    <PageTitle>Purchase Order Lines</PageTitle>

    @if (isReady) {
    <Table TItem="PoItem"
           IsPagination="true" PageItemsSource="new int[] {20, 25,30 }"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New" EditModalTitle="Edit"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync" DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.PoNum" Text="PO Number" Sortable="true" Searchable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.PoLine" Text="Po Line" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Item" Text="Item" Sortable="true" Searchable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyOrdered" Text="Qty Ordered" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyReceived" Text="Qty Received" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyVoucher" Text="Qty Voucher" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.UnitCost" Text="Unit Cost" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.DueDate" Text="Due Date" Sortable="true" Align="Alignment.Center" FormatString="yyyy-MM-dd"/>
            <TableColumn @bind-Field="@context.UnitCostReceived" Text="Unit Cost Received" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Location" Text="Location" Sortable="true" FormatString="yyyy-MM-dd" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.UnitOfMeasure" Text="UM" Required="true" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Status" Text="Status" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
                @* <SearchTemplate Context="model"> *@
                @*     <div class="col-12 col-sm-6"> *@
                @*         <Select Items="SelectedItems" @bind-Value="@model!.Status" ShowLabel="true" *@
                @*                 DisplayText="Status"></Select> *@
                @*     </div> *@
                @* </SearchTemplate> *@
            </TableColumn>
            @* <TableColumn @bind-Field="@context.ExtendedCost" Text="ExtendedCost" Sortable="true"/> *@
        </TableColumns>
        @* <CustomerSearchTemplate> *@
        @*     @if (context is PurchaseOrderLinesSearchModel model) *@
        @*     { *@
        @*         <PurchaseOrderLinesSearch @bind-Value="@model" /> *@
        @*     } *@
        @* </CustomerSearchTemplate> *@
        <EditTemplate>
            <PurchaseOrderLinesEditTemplate Model="context" PoItems="PoItems" PoNums="PoNumItems"
                                            Items="Items" ItemLocations="ItemLocationItems"
                                            UnitOfMeasures = "UnitOfMeasureItems" PoNum=@PoNum/>
        </EditTemplate>
    </Table>
}
    </SecurePage>
@code {
    [Parameter]
    public string? PoNum { get; set; }
    
    private List<PoItem> PoItems { get; set; } = new();

    private PoItem SearchModel { get; set; } = new();

    // private ITableSearchModel CustomerSearchModel { get; set; } = new PurchaseOrderLinesSearchModel();
    private string relativePath = "api/purchaseorderlines";
    private bool isReady;

    private List<string> PoNumItems { get; set; } = new();
    private List<Item> Items { get; set; } = new();
    private List<ItemLocation> ItemLocationItems { get; set; } = new();
    private List<UnitOfMeasure> UnitOfMeasureItems { get; set; } = new();
    
    // private IEnumerable<SelectedItem> SelectedItems { get; set; } = [
    //     new SelectedItem("", "Select Status") { IsDisabled = true, Active = true },
    //     new SelectedItem("O", "Ordered"),
    //     new SelectedItem("P", "Planning"),
    //     new SelectedItem("C", "Complete")
    // ];


    protected override async Task OnParametersSetAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            if (!string.IsNullOrEmpty(PoNum)) {
                SearchModel.PoNum = PoNum;
            }

            await LoadPoNumsAsync();
            await LoadItemsAsync();
            await LoadItemsLocationsAsync();
            await LoadUnitOfMeasuresAsync();
            await LoadData();
            isReady = true;
        }
    }
    
    private async Task LoadUnitOfMeasuresAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitOfMeasure>>>("api/units");
            if (response?.IsSuccess == true && response.Data != null) {
                UnitOfMeasureItems = response.Data;
            } else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load UnitOfMeasure: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load UnitOfMeasure: {ex.Message}",
                Color = Color.Danger
            });
        }
    } 
    private async Task LoadItemsLocationsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ItemLocation>>>("api/itemlocations");
            if (response?.IsSuccess == true && response.Data != null) {
                ItemLocationItems = response.Data;
            } else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load ItemLocation: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load ItemLocation: {ex.Message}",
                Color = Color.Danger
            });
        }
    }
    
    private async Task LoadPoNumsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<string>>>("api/purchaseorder/ponums");
            if (response?.IsSuccess == true && response.Data != null) {
                PoNumItems = response.Data;
            } else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load PoNums: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load PoNums: {ex.Message}",
                Color = Color.Danger
            });
        }
    }
    
    private async Task LoadItemsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Item>>>("api/items");
            if (response?.IsSuccess == true && response.Data != null) {
                Items = response.Data;
            } else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load Items: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load Items: {ex.Message}",
                Color = Color.Danger
            });
        }
    }
    private async Task LoadData() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<PoItem>>>(relativePath);
            if (response?.IsSuccess == true) {
                PoItems = response.Data ?? [];
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load data",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load data: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<PoItem>> OnQueryAsync(QueryPageOptions options) {
        // 处理模糊搜索
        IEnumerable<PoItem> items = PoItems;
        // 处理搜索文本
        if (!string.IsNullOrEmpty(options.SearchText)) {
            items = items.Where(i =>
                (i.PoNum?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                (i.Item?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false) ||
                // (i.Status.ToString().Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                (Enum.TryParse<EnumStatus>(options.SearchText, true, out var status) && i.Status == status)
            );
        }

        // 处理PoNum筛选
        if (!string.IsNullOrEmpty(SearchModel.PoNum)) {
            items = items.Where(i => i.PoNum == SearchModel.PoNum);
        }

        // 处理Item筛选
        if (!string.IsNullOrEmpty(SearchModel.Item)) {
            items = items.Where(i => i.Item == SearchModel.Item);
        }

        // 处理Status筛选
        if (SearchModel.Status.HasValue) {
            items = items.Where(i => i.Status == SearchModel.Status.Value);
        }

        // 应用排序
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // 获取总数
        var total = items.Count();

        // 内存分页
        var result = items
            .Skip((options.PageIndex - 1) * options.PageItems)
            .Take(options.PageItems)
            .ToList();

        return Task.FromResult(new QueryData<PoItem>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<PoItem> OnAddAsync() {
        return await Task.FromResult(new PoItem {
            PoNum = !string.IsNullOrEmpty(PoNum) ? PoNum : "PN?", // 使用URL中的PoNum，如果没有则使用默认值
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(PoItem item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<PoItem> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadData();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(PoItem model) {
        model.PoNum = "";
        model.Item = "";
        model.Status = null;
        return Task.CompletedTask;
    }

}
