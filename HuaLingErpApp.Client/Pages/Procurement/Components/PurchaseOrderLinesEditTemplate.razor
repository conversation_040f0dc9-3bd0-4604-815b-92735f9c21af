@using System.ComponentModel.DataAnnotations
@using HuaLingErpApp.Shared.Models
@inherits OwningComponentBase

@if (Model is not null) {
    <EditorForm Model="Model" RowType="RowType.Inline" ItemsPerRow="2" show >
        <FieldItems>
            <!-- Hidden fields -->
            <EditorItem @bind-Field="@context.Id" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.RecordDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.ModifiedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyReceived" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyVoucher" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.UnitCostReceived"  Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.PoLine" Readonly="true" Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.Location" Readonly="true" Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyOrdered" Text="Qty Ordered"  Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.ItemDesc" Text="Description" Readonly="true" Required="false"></EditorItem>
            <EditorItem @bind-Field="@context.UnitCost" Text="Unit Cost" Required="true" Step="0.0001"></EditorItem>
            
            
            <!--  AutoFill -->
            <EditorItem @bind-Field="@context.PoNum" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedPoNum"
                                  Items="PoNums"
                                  OnGetDisplayText="@(p=>p is null ? string.Empty : $"{p}")"
                                  OnSelectedItemChanged="OnPoNumChanged"
                                  IsLikeMatch="true"
                                  DisplayText="PoNum"
                                  RequiredErrorMessage="PoNum is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for PoNum...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>
            
            <EditorItem @bind-Field="@context.Item" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedItem"
                                  Items="Items"
                                  OnGetDisplayText="@(i=>i is null ? string.Empty : $"{i.Id}")"
                                  OnSelectedItemChanged="OnItemChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Item"
                                  RequiredErrorMessage="Item is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for Item...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>
            <!-- Dropdown -->
            <EditorItem @bind-Field="@context.UnitOfMeasure" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.UnitOfMeasure" Items="UnitOfMeasureItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select UnitOfMeasure..." >
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>
        </FieldItems>
    </EditorForm>
}

@code {
    [Parameter] [Required] public PoItem? Model { get; set; }
    [Parameter] [Required] public List<PoItem?> PoItems { get; set; } = [];
    [Parameter] [Required] public List<string> PoNums { get; set; } = [];
    [Parameter] [Required] public List<Item> Items { get; set; } = [];
    [Parameter] [Required] public List<ItemLocation> ItemLocations { get; set; } = [];
    [Parameter] [Required] public List<UnitOfMeasure> UnitOfMeasures { get; set; } = [];
    [Parameter]  public string? PoNum { get; set; }
    

    private string? SelectedPoNum { get; set; }
    private Item? SelectedItem { get; set; }
    
    private IEnumerable<SelectedItem> UnitOfMeasureItems => UnitOfMeasures
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));
    
    protected override void OnInitialized() {
        if (PoNum is not null) {
            SelectedPoNum = PoNum;
        }

        base.OnInitializedAsync();
    }

    protected override void OnParametersSet() {
        base.OnParametersSet();

        // Initialize AutoFill values based on Model
        if (Model is not null) {
            // Initialize 
            if (!string.IsNullOrEmpty(Model.PoNum)) {
                SelectedPoNum = PoNums.FirstOrDefault(p => p == Model.PoNum);
            }
            if (!string.IsNullOrEmpty(Model.Item)) {
                SelectedItem = Items.FirstOrDefault(p => p.Id == Model.Item);
                Model.ItemDesc = SelectedItem!.Description;
            }
        }
    }

    private Task OnPoNumChanged(string? poNum) {
        if (poNum is null || Model is null) return Task.CompletedTask;

        Model.PoNum = poNum;
        // 转换为 short 类型，先判断是否有值再转换
        int? maxPoLine = PoItems
            .Where(p => p?.PoNum == poNum)
            .MaxBy(p => p?.PoLine)?.PoLine;
        if (maxPoLine.HasValue) {
            Model.PoLine = (short)(maxPoLine.Value + 1);
        }
        else {
            Model.PoLine = 1; // 如果没有找到任何匹配项，则从 1 开始
        }

        // Update SelectedVendor
        SelectedPoNum = poNum;
        return Task.CompletedTask;
    }
    private Task OnItemChanged(Item? item) {
        if (item is null || Model is null) return Task.CompletedTask;

        Model.Item = item.Id;
        Model.Location = ItemLocations.First(i => i.Item == item.Id).Location;
        Model.ItemDesc = item.Description;
        SelectedItem = item;
        return Task.CompletedTask;
    }

}
