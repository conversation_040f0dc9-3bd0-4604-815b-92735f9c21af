@page "/finance/parameters"
@using HuaLingErpApp.Shared.Models
@using HuaLingErpApp.Shared
@using HuaLingErpApp.Client.Components
@inject ApiService Api
@inject MessageService MessageService

<SecurePage RequiredRoles="Administrator,Manager" PageName="System Parameters">
    <PageTitle>System Parameters</PageTitle>

@if (_isLoading)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 50vh;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2">Loading parameters...</div>
        </div>
    </div>
}
else if (_parameters != null)
{
    <Card>
        <HeaderTemplate>
            <h5 class="mb-0">
                <i class="fas fa-cog me-2"></i>
                System Parameters
            </h5>
        </HeaderTemplate>
        <BodyTemplate>
            <Tab>
                <TabItem Text="General" Icon="fas fa-globe">
                    <div class="row g-3 align-items-center">
                        <div class="col-auto">
                            <h6 class="mb-0">Base Currency Code</h6>
                        </div>
                        <div class="col-md-2">
                            <BootstrapInput @bind-Value="_parameters.BaseCurrCode"
                                            DisplayText="Base Currency Code"
                                            PlaceHolder="e.g., USD, EUR, CNY"
                                            maxlength="3" />
                        </div>
                    </div>
                </TabItem>

                <TabItem Text="Purchase" Icon="fas fa-shopping-cart">
                    <!-- First Row: Payable Account and Purchase Account -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">Payable Account</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <BootstrapInput @bind-Value="_parameters.PayableAcct"
                                                        DisplayText="Account"
                                                        PlaceHolder="Account code"
                                                        maxlength="8" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PayableAcctUnit1"
                                                        DisplayText="Unit 1"
                                                        PlaceHolder="Unit code 1"
                                                        maxlength="4"/>
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PayableAcctUnit2"
                                                        DisplayText="Unit 2"
                                                        PlaceHolder="Unit code 2"
                                                        maxlength="4" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">Purchase Account</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <BootstrapInput @bind-Value="_parameters.PurAcct"
                                                        DisplayText="Account"
                                                        PlaceHolder="Account code"
                                                        maxlength="8" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurAcctUnit1"
                                                        DisplayText="Unit 1"
                                                        PlaceHolder="Unit code 1"
                                                        maxlength="4" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurAcctUnit2"
                                                        DisplayText="Unit 2"
                                                        PlaceHolder="Unit code 2"
                                                        maxlength="4" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Second Row: Purchase Miscellaneous Account and Input Tax Account -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">PurMisc Account</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <BootstrapInput @bind-Value="_parameters.PurMiscAcct"
                                                        DisplayText="Account"
                                                        PlaceHolder="Account code"
                                                        maxlength="8" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurMiscAcctUnit1"
                                                        DisplayText="Unit 1"
                                                        PlaceHolder="Unit code 1"
                                                        maxlength="4" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurMiscAcctUnit2"
                                                        DisplayText="Unit 2"
                                                        PlaceHolder="Unit code 2"
                                                        maxlength="4" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">Input Tax Account</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <BootstrapInput @bind-Value="_parameters.InputTaxAcct"
                                                        DisplayText="Account"
                                                        PlaceHolder="Account code"
                                                        maxlength="8" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.InputTaxAcctUnit1"
                                                        DisplayText="Unit 1"
                                                        PlaceHolder="Unit code 1"
                                                        maxlength="4" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.InputTaxAcctUnit2"
                                                        DisplayText="Unit 2"
                                                        PlaceHolder="Unit code 2"
                                                        maxlength="4" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Third Row: Purchase Expense Account and Purchase Order Settings -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">PurExpense Account</h6>
                                    </div>
                                    <div class="col-md-4">
                                        <BootstrapInput @bind-Value="_parameters.PurExpenseAcct"
                                                        DisplayText="Account"
                                                        PlaceHolder="Account code"
                                                        maxlength="8" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurExpenseAcctUnit1"
                                                        DisplayText="Unit 1"
                                                        PlaceHolder="Unit code 1"
                                                        maxlength="4" />
                                    </div>
                                    <div class="col-md-2">
                                        <BootstrapInput @bind-Value="_parameters.PurExpenseAcctUnit2"
                                                        DisplayText="Unit 2"
                                                        PlaceHolder="Unit code 2"
                                                        maxlength="4" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="row g-3 align-items-center">
                                    <div class="col-3">
                                        <h6 class="mb-0">PO Settings</h6>
                                    </div>
                                    <div class="col-md-6">
                                        <BootstrapInput @bind-Value="_parameters.PoOrderPrefix"
                                                        DisplayText="Order Prefix"
                                                        PlaceHolder="e.g., PO"
                                                        maxlength="10" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </TabItem>

                <TabItem Text="COGS" Icon="fas fa-calculator">
                    
                        <div class="row g-3 align-items-center">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="row g-3 align-items-center">
                                        <div class="col-2">
                                            <h6 class="mb-0">COGS Account</h6>
                                        </div>
                                        <div class="col-md-4">
                                            <BootstrapInput @bind-Value="_parameters.CgsAcct"
                                                            DisplayText="Account"
                                                            PlaceHolder="Account code"
                                                            maxlength="8" />
                                        </div>
                                        <div class="col-md-2">
                                            <BootstrapInput @bind-Value="_parameters.CgsAcctUnit1"
                                                            DisplayText="Unit 1"
                                                            PlaceHolder="Unit code 1"
                                                            maxlength="4" />
                                        </div>
                                        <div class="col-md-2">
                                            <BootstrapInput @bind-Value="_parameters.CgsAcctUnit2"
                                                            DisplayText="Unit 2"
                                                            PlaceHolder="Unit code 2"
                                                            maxlength="4" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </TabItem>
            </Tab>
        </BodyTemplate>
        
    
        <FooterTemplate>
            <div class="text-center">
                <Button Color="Color.Primary" 
                        Size="Size.Large"
                        Icon="fas fa-save"
                        OnClick="@SaveParameters"
                        IsDisabled="_isSaving">
                    @if (_isSaving)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <span>Saving...</span>
                    }
                    else
                    {
                        <span>Save Parameters</span>
                    }
                </Button>
            </div>
        </FooterTemplate>
    </Card>
}
</SecurePage>

@code {
    private Parms? _parameters;
    private bool _isLoading = true;
    private bool _isSaving = false;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadParameters();
        }
    }

    private async Task LoadParameters()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var response = await Api.GetAsync<HttpResponseModel<Parms>>("api/parms");

            if (response?.IsSuccess == true && response.Data != null)
            {
                _parameters = response.Data;
            }
            else
            {
                _parameters = null;
                if (response?.Message != null && !response.Message.Contains("No system parameters found"))
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = response.Message,
                        Color = Color.Warning
                    });
                }
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Error loading parameters: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SaveParameters()
    {
        if (_parameters == null) return;

        try
        {
            _isSaving = true;
            StateHasChanged();

            var response = await Api.PutAsync($"api/parms/{_parameters.Id}", _parameters);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<Parms>>(content);

                if (result?.IsSuccess == true && result.Data != null)
                {
                    _parameters = result.Data;

                    await MessageService.Show(new MessageOption
                    {
                        Content = "Parameters saved successfully",
                        Color = Color.Success
                    });
                }
                else
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = result?.Message ?? "Failed to save parameters",
                        Color = Color.Danger
                    });
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                await MessageService.Show(new MessageOption
                {
                    Content = errorResponse?.Message ?? "Failed to save parameters",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Error saving parameters: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally
        {
            _isSaving = false;
            StateHasChanged();
        }
    }

    private async Task InitializeParameters()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var response = await Api.PostAsync("api/parms/initialize", new { });

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<Parms>>(content);

                if (result?.IsSuccess == true && result.Data != null)
                {
                    _parameters = result.Data;

                    await MessageService.Show(new MessageOption
                    {
                        Content = "Parameters initialized successfully",
                        Color = Color.Success
                    });
                }
                else
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = result?.Message ?? "Failed to initialize parameters",
                        Color = Color.Danger
                    });
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                await MessageService.Show(new MessageOption
                {
                    Content = errorResponse?.Message ?? "Failed to initialize parameters",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Error initializing parameters: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }
}
