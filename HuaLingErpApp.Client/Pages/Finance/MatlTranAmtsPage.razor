@page "/finance/matltranamt"
@using Console = System.Console

<SecurePage RequiredRoles="Administrator,Manager,User" PageName="Material Transaction Amounts">
@if (isReady) {
    
    <!-- Data Table -->
    <Table TItem="MatlTranAmt"
           IsPagination="true" PageItemsSource="[20, 25, 30, 50]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" ShowExtendButtons="false"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           ShowSkeleton="true"
           ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false"
           IsMultipleSelect="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.TransNum" Text="Trans Number" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.TransSeq" Text="Trans Seq" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Amt" Text="Amount" Sortable="true" 
                         Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Acct" Text="Account" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.AcctUnit1" Text="Account Unit 1" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.AcctUnit2" Text="Account Unit 2" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.IncludeInInventoryBalCalc" Text="Include in Inventory Calc" Sortable="true"
                         Align="Alignment.Center">
                <Template Context="value">
                    @switch (value.Value) {
                        case true:
                            <i class="fas fa-check text-success"></i>
                            break;
                        case false:
                            <i class="fas fa-times text-danger"></i>
                            break;
                        default:
                            <span class="text-muted">-</span>
                            break;
                    }
                </Template>
            </TableColumn>
        </TableColumns>
    </Table>
}
</SecurePage>

@code {
    private List<MatlTranAmt> Items { get; set; } = new();
    private MatlTranAmt SearchModel { get; set; } = new();

    private string relativePath = "api/matltranamt";
    private bool isReady = false;

    protected override async Task OnInitializedAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadData();
        }
        catch (Exception ex) {
            // Only show an error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadData() {
        var response = await Api.GetAsync<HttpResponseModel<List<MatlTranAmt>>>(relativePath);
        if (response?.IsSuccess == true && response.Data != null) {
            Items = response.Data;
        }
        else {
            await MessageService.Show(new MessageOption {
                Content = response?.Message ?? "Failed to load material transaction amounts",
                Color = Color.Danger
            });
        }
    }
    
    private Task<QueryData<MatlTranAmt>> OnQueryAsync(QueryPageOptions options) {
        IEnumerable<MatlTranAmt> items = Items;
        if (!string.IsNullOrEmpty(options.SearchText))
        {
            items = items.Where(i => 
                (i.TransNum.ToString()?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                || 
                (i.TransSeq.ToString()?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                || 
                (i.Acct?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
        }
        // Apply search
        if (SearchModel.TransNum > 0) {
            items = items.Where(i => i.TransNum == SearchModel.TransNum);
        }
        if (SearchModel.TransSeq > 0) {
            items = items.Where(i => i.TransSeq == SearchModel.TransSeq);
        }
        if (!string.IsNullOrEmpty(SearchModel.Acct)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Acct) && i.Acct.Contains(SearchModel.Acct, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.AcctUnit1)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.AcctUnit1) && i.AcctUnit1.Contains(SearchModel.AcctUnit1, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.AcctUnit2)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.AcctUnit2) && i.AcctUnit2.Contains(SearchModel.AcctUnit2, StringComparison.OrdinalIgnoreCase));
        }

        // Apply sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<MatlTranAmt>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private Task OnResetSearchAsync(MatlTranAmt searchModel) {
        searchModel.TransNum = 0;
        searchModel.TransSeq = 0;
        searchModel.Acct = "";
        searchModel.AcctUnit1 = "";
        searchModel.AcctUnit2 = "";
        return Task.CompletedTask;
    }
    
}
