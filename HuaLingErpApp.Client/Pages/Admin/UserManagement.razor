@page "/admin/users"
@using HuaLingErpApp.Shared.Models
@using HuaLingErpApp.Client.Components
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Console = System.Console
@attribute [Authorize(Roles = "Administrator")]
@inject ApiService Api
@inject MessageService MessageService
@inject DialogService DialogService

<SecurePage RequiredRoles="Administrator" PageName="User Management">
    <PageTitle>User Management - HuaLing ERP</PageTitle>

    @if (isReady) {
    <Table TItem="UserInfo"
           IsPagination="true" PageItemsSource="new int[] { 10, 20, 50 }"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Create New User" EditModalTitle="Edit User"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true" ShowSkeleton="true">

        <TableColumns>
            <!-- Username Column -->
            <TableColumn @bind-Field="@context.UserName" Required="true" Text="Username" Sortable="true" Filterable="true"
                         Width="120">
            </TableColumn>

            <!-- Display Name Column -->
            <TableColumn @bind-Field="@context.DisplayName" Text="Display Name" Sortable="true" Filterable="true"
                         Width="150">
                <Template Context="columnContext">
                    @(columnContext.Value ?? "-")
                </Template>
            </TableColumn>

            <!-- Email Column -->
            <TableColumn @bind-Field="@context.Email" Text="Email" Sortable="true" Filterable="true"
                         Width="200" Required="true">
            </TableColumn>

            <!-- Phone Number Column -->
            <TableColumn @bind-Field="@context.PhoneNumber" Text="Phone" Sortable="false" Filterable="false"
                         Width="120">
                <Template Context="columnContext">
                    @(columnContext.Value ?? "-")
                </Template>
            </TableColumn>

            <!-- Roles Column -->
            <TableColumn @bind-Field="@context.Roles" Text="Roles" Required="true" Sortable="false" Filterable="false"
                         Width="200">
                <Template Context="columnContext">
                    @if (columnContext.Value?.Any() == true) {
                        @foreach (var role in columnContext.Value) {
                            <Tag Color="Color.Primary" class="me-1">@role</Tag>
                        }
                    }
                    else {
                        <span class="text-muted">No roles</span>
                    }
                </Template>
                
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <MultiSelect TValue="List<string>" @bind-Value="@_.Roles" 
                                     ShowRequired="true" Items="@RoleItems"
                                     ShowSearch="true"
                                     IsPopover="true"
                                     PlaceHolder="Select Roles..." >
                        </MultiSelect>
                    </div>
                </EditTemplate>
            </TableColumn>

            <!-- Last Login Column -->
            <TableColumn @bind-Field="@context.LastLoginTime" Text="Last Login" IsVisibleWhenEdit="false" IsVisibleWhenAdd="false"
                         Sortable="true" Filterable="false"
                         Width="150">
                <Template Context="columnContext">
                    @(columnContext.Value?.ToString("yyyy-MM-dd HH:mm") ?? "Never")
                </Template>
            </TableColumn>
            
            <TableColumn @bind-Field="@context.Password"
                         IsRequiredWhenAdd="true" IsVisibleWhenAdd="true"
                         IsRequiredWhenEdit="false" IsVisibleWhenEdit="false"
                         Width="150" Visible="false"  >
                <EditTemplate Context="item">
                    <div class="col-12 col-sm-6 col-md-6">
                    <BootstrapPassword ShowRequired="true" @bind-Value="@item.Password"
                                       PlaceHolder="Enter password..."/>
                    </div>
                </EditTemplate>
            </TableColumn>
            
            <TableColumn @bind-Field="@context.Id" Text="Actions" Width="120" Sortable="false" Filterable="false"
                         IsRequiredWhenAdd="false" IsVisibleWhenAdd="false"
                         IsRequiredWhenEdit="false" IsVisibleWhenEdit="false">
                <Template Context="columnContext">
                    <Button Size="Size.ExtraSmall" Color="Color.Warning" 
                            OnClick="@(() => ShowResetPasswordDialog(columnContext.Row))">
                        Reset Password
                    </Button>
                </Template>
            </TableColumn>
            
        </TableColumns>

        <SearchTemplate>
            <GroupBox Title="Search Criteria">
                <div class="row g-3 form-inline">
                    <div class="col-12 col-sm-6">
                        <BootstrapInput @bind-Value="@context.UserName" PlaceHolder="Username" maxlength="50" />
                    </div>
                    <div class="col-12 col-sm-6">
                        <BootstrapInput @bind-Value="@context.Email" PlaceHolder="Email" maxlength="100" />
                    </div>
                </div>
            </GroupBox>
        </SearchTemplate>
    </Table>
}
</SecurePage>
@code {
        private List<UserInfo> users = new();
        private List<Role> allRoles = new();
        private bool isReady = false;
        private UserInfo SearchModel = new();
        private string relativePath = "api/users";

        private IEnumerable<SelectedItem> RoleItems => allRoles
            .Select(c => new SelectedItem(c.Name, $"{c.Name}"));

        // Status items for search dropdown
        private readonly IEnumerable<SelectedItem> StatusItems = new SelectedItem[] {
            new("", "All Status"),
            new("true", "Active"),
            new("false", "Inactive")
        };

        protected override async Task OnInitializedAsync() {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await LoadUsersAsync();
                await LoadRolesAsync();
                isReady = true;
            }

        }

        private async Task LoadUsersAsync() {
            try {
                var response = await Api.GetAsync<HttpResponseModel<List<UserInfo>>>(relativePath);
                if (response?.IsSuccess == true && response.Data != null) {
                    users = response.Data;
                }
                else {
                    await MessageService.Show(new MessageOption {
                        Content = $"Failed to load UserInfo: {response?.Message ?? "Unknown error"}",
                        Color = Color.Danger
                    });
                }
            }
            catch (Exception ex) {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load UserInfo: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }

        private async Task LoadRolesAsync() {
            try {
                var response = await Api.GetAsync<HttpResponseModel<List<Role>>>($"{relativePath}/roles");
                if (response?.IsSuccess == true && response.Data != null) {
                    allRoles = response.Data;
                }
                else {
                    await MessageService.Show(new MessageOption {
                        Content = $"Failed to load Roles: {response?.Message ?? "Unknown error"}",
                        Color = Color.Danger
                    });
                }
            }
            catch (Exception ex) {
                await MessageService.Show(new MessageOption() {
                    Content = "Failed to load roles: " + ex.Message,
                    Icon = "fa-solid fa-exclamation-triangle",
                    Color = Color.Danger
                });
            }
        }


        // Table event handlers
        private Task<QueryData<UserInfo>> OnQueryAsync(QueryPageOptions options) {
            // 处理模糊搜索
            IEnumerable<UserInfo> items = users;
            // 处理搜索文本
            if (!string.IsNullOrEmpty(options.SearchText)) {
                items = items.Where(i =>
                    (i.UserName.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                    ||
                    (i.DisplayName?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                    ||
                    (i.Email.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                    ||
                    (i.PhoneNumber?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                    ||
                    (i.Roles?.FindAll(r => r.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)).Any() ?? false)

                );
            }


            if (!string.IsNullOrEmpty(SearchModel.UserName)) {
                items = items.Where(i => i.UserName == SearchModel.UserName);
            }


            if (!string.IsNullOrEmpty(SearchModel.DisplayName)) {
                items = items.Where(i => i.DisplayName == SearchModel.DisplayName);
            }

            if (!string.IsNullOrEmpty(SearchModel.Email)) {
                items = items.Where(i => i.Email == SearchModel.Email);
            }

            if (!string.IsNullOrEmpty(SearchModel.PhoneNumber)) {
                items = items.Where(i => i.PhoneNumber == SearchModel.PhoneNumber);
            }


            if (SearchModel.Roles.Any()) {
                items = items.Where(i => i.Roles.Any(r => SearchModel.Roles.Contains(r)));
            }

            // 应用排序
            var isSorted = false;
            if (!string.IsNullOrEmpty(options.SortName)) {
                items = items.Sort(options.SortName, options.SortOrder);
                isSorted = true;
            }

            // 获取总数
            var total = items.Count();

            // 内存分页
            var result = items
                .Skip((options.PageIndex - 1) * options.PageItems)
                .Take(options.PageItems)
                .ToList();

            return Task.FromResult(new QueryData<UserInfo>() {
                Items = items,
                TotalCount = total,
                IsSorted = isSorted,
                IsFiltered = options.Filters.Count > 0,
                IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
                IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
            });
        }

        private Task OnResetSearchAsync(UserInfo model) {
            model.UserName = "";
            model.DisplayName = "";
            model.Email = "";
            model.PhoneNumber = "";
            model.Roles = null;
            return Task.CompletedTask;
        }

        private async Task<UserInfo> OnAddAsync() {
            return await Task.FromResult(new UserInfo() {
            });
        }

        private async Task<bool> OnSaveAsync(UserInfo item, ItemChangedType changedType) {
            try {
                if (changedType == ItemChangedType.Add) {
                    var response = await Api.PostAsync(relativePath, new CreateUserRequest() {
                        Password = item.Password,
                        UserName = item.UserName,
                        DisplayName = item.DisplayName,
                        Email = item.Email,
                        PhoneNumber = item.PhoneNumber,
                        RoleIds = item.Roles.Select(r => allRoles.Find(x => x.Name == r)?.Id ?? 0).ToList()
                    });
                    if (response.IsSuccessStatusCode) {
                        await MessageService.Show(new MessageOption {
                            Content = "Added successfully",
                            Color = Color.Success
                        });
                        await LoadUsersAsync();
                        return true;
                    }
                    else {
                        // 解析错误响应
                        var errorContent = await response.Content.ReadAsStringAsync();
                        var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                        await MessageService.Show(new MessageOption {
                            Content = errorResponse?.Message ?? "Add failed: " + response.ReasonPhrase,
                            Color = Color.Danger
                        });
                    }
                }
                else {
                    // 调试：检查角色数据
                    var roleIds = item.Roles?.Select(r => allRoles.Find(x => x.Name == r)?.Id ?? 0).ToList() ?? new List<int>();
                    Console.WriteLine(roleIds);

                    var response = await Api.PutAsync($"{relativePath}/{item.Id}", new UpdateUserRequest {
                        Id = item.Id,
                        Email = item.Email,
                        DisplayName = item.DisplayName,
                        PhoneNumber = item.PhoneNumber,
                        IsActive = true,
                        RoleIds = roleIds
                    });
                    if (response.IsSuccessStatusCode) {
                        await MessageService.Show(new MessageOption {
                            Content = "Updated successfully",
                            Color = Color.Success
                        });
                        await LoadUsersAsync();
                        return true;
                    }
                    else {
                        // 解析错误响应
                        var errorContent = await response.Content.ReadAsStringAsync();
                        var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                        await MessageService.Show(new MessageOption {
                            Content = errorResponse?.Message ?? "Update failed: " + response.ReasonPhrase,
                            Color = Color.Danger
                        });
                    }
                }
            }
            catch (Exception ex) {
                await MessageService.Show(new MessageOption {
                    Content = $"Save failed: {ex.Message}",
                    Color = Color.Danger
                });
                return false;
            }

            return false;
        }

        private async Task<bool> OnDeleteAsync(IEnumerable<UserInfo> items) {
            try {
                var itemList = items.ToList();
                if (!itemList.Any()) {
                    await MessageService.Show(new MessageOption {
                        Content = "Please select items to delete",
                        Color = Color.Warning
                    });
                    return false;
                }

                var ids = itemList.Select(i => i.Id).ToList();
                HttpResponseMessage response;

                if (ids.Count == 1) {
                    // Single delete
                    response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
                }
                else {
                    // Batch delete
                    response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
                }

                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Deleted successfully",
                        Color = Color.Success
                    });

                    // Reload data
                    await LoadUsersAsync();
                    return true;
                }
                else {
                    var error = await response.Content.ReadFromJsonAsync<HttpResponseModel<object>>();
                    await MessageService.Show(new MessageOption {
                        Content = $"Delete failed: {error.Message ?? response.ReasonPhrase}",
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            catch (Exception ex) {
                await MessageService.Show(new MessageOption {
                    Content = $"Delete failed: {ex.Message}",
                    Color = Color.Danger
                });
                return false;
            }
        }

        private async Task ShowResetPasswordDialog(UserInfo user) {
            var resetRequest = new AdminResetPasswordRequest { UserId = user.Id };

            // 生成所有编辑项
            var items = Utility.GenerateEditorItems<AdminResetPasswordRequest>();

            // 隐藏 UserId 字段
            var userIdItem = items.First(i => i.GetFieldName() == nameof(AdminResetPasswordRequest.UserId));
            userIdItem.Ignore = true;

            // 设置密码字段为 BootstrapPassword 组件
            var newPasswordItem = items.First(i => i.GetFieldName() == nameof(AdminResetPasswordRequest.NewPassword));
            newPasswordItem.ComponentType = typeof(BootstrapPassword);

            var confirmPasswordItem = items.First(i => i.GetFieldName() == nameof(AdminResetPasswordRequest.ConfirmPassword));
            confirmPasswordItem.ComponentType = typeof(BootstrapPassword);

            var option = new EditDialogOption<AdminResetPasswordRequest>() {
                Title = $"Reset Password for {user.UserName}",
                Model = resetRequest,
                Items = items,
                RowType = RowType.Inline,
                ItemsPerRow = 1,
                ShowLoading = true,
                ItemChangedType = ItemChangedType.Update,
                Size = Size.Large,
                OnEditAsync = async context => {
                    var response = await Api.PostAsync($"{relativePath}/{user.Id}/reset-password", resetRequest);
                    if (response.IsSuccessStatusCode) {
                        await MessageService.Show(new MessageOption {
                            Content = "Password reset successfully",
                            Color = Color.Success
                        });
                        return true;
                    }
                    else {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                        await MessageService.Show(new MessageOption {
                            Content = errorResponse?.Message ?? "Failed to reset password",
                            Color = Color.Danger
                        });
                        return false;
                    }
                }
            };

            await DialogService.ShowEditDialog(option);
        }

    }

