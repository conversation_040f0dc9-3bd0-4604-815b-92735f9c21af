@using System.ComponentModel.DataAnnotations
@using HuaLingErpApp.Shared.Models
@inherits OwningComponentBase

@if (Model is not null) {
    <EditorForm Model="Model" RowType="RowType.Inline" ItemsPerRow="2" show >
        <FieldItems>
            <!-- Hidden fields -->
            <EditorItem @bind-Field="@context.Id" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.RecordDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.ModifiedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyShipped" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyInvoiced" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.UnitCostShipped"  Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CoLine" Readonly="true" Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.Location" Readonly="true" Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.QtyOrdered" Text="Qty Ordered"  Required="true"></EditorItem>
            <EditorItem @bind-Field="@context.ItemDesc" Text="Description" Readonly="true" Required="false"></EditorItem>
            <EditorItem @bind-Field="@context.UnitPrice" Text="Unit Price" Required="true" Step="0.0001"></EditorItem>
            <EditorItem @bind-Field="@context.DueDate" Text="Due Date" Required="false"></EditorItem>
            
            <!--  AutoFill -->
            <EditorItem @bind-Field="@context.CoNum" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedCoNum"
                                  Items="CoNums"
                                  OnGetDisplayText="@(p=>p is null ? string.Empty : $"{p}")"
                                  OnSelectedItemChanged="OnCoNumChanged"
                                  IsLikeMatch="true"
                                  DisplayText="CoNum"
                                  RequiredErrorMessage="CoNum is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for CoNum...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>
            
            <EditorItem @bind-Field="@context.Item" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedItem"
                                  Items="Items"
                                  OnGetDisplayText="@(i=>i is null ? string.Empty : $"{i.Id}")"
                                  OnSelectedItemChanged="OnItemChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Item"
                                  RequiredErrorMessage="Item is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for Item...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>
            <!-- Dropdown -->
            <EditorItem @bind-Field="@context.UnitOfMeasure" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.UnitOfMeasure" Items="UnitOfMeasureItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select UnitOfMeasure..." >
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>
        </FieldItems>
    </EditorForm>
}

@code {
    [Parameter] [Required] public CoItem? Model { get; set; }
    [Parameter] [Required] public List<CoItem?> CoItems { get; set; } = [];
    [Parameter] [Required] public List<string> CoNums { get; set; } = [];
    [Parameter] [Required] public List<Item> Items { get; set; } = [];
    [Parameter] [Required] public List<ItemLocation> ItemLocations { get; set; } = [];
    [Parameter] [Required] public List<UnitOfMeasure> UnitOfMeasures { get; set; } = [];
    [Parameter]  public string? CoNum { get; set; }
    

    private string? SelectedCoNum { get; set; }
    private Item? SelectedItem { get; set; }
    
    private IEnumerable<SelectedItem> UnitOfMeasureItems => UnitOfMeasures
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));
    
    protected override void OnInitialized() {
        if (CoNum is not null) {
            SelectedCoNum = CoNum;
        }

        base.OnInitializedAsync();
    }

    protected override void OnParametersSet() {
        base.OnParametersSet();

        // Initialize AutoFill values based on Model
        if (Model is not null) {
            // Initialize 
            if (!string.IsNullOrEmpty(Model.CoNum)) {
                SelectedCoNum = CoNums.FirstOrDefault(p => p == Model.CoNum);
            }
            if (!string.IsNullOrEmpty(Model.Item)) {
                SelectedItem = Items.FirstOrDefault(p => p.Id == Model.Item);
                if (SelectedItem != null) {
                    Model.ItemDesc = SelectedItem.Description;
                }
            }
        }
    }

    private Task OnCoNumChanged(string? coNum) {
        if (coNum is null || Model is null) return Task.CompletedTask;

        Model.CoNum = coNum;
        // 转换为 short 类型，先判断是否有值再转换
        int? maxCoLine = CoItems
            .Where(p => p?.CoNum == coNum)
            .MaxBy(p => p?.CoLine)?.CoLine;
        if (maxCoLine.HasValue) {
            Model.CoLine = (short)(maxCoLine.Value + 1);
        }
        else {
            Model.CoLine = 1; // 如果没有找到任何匹配项，则从 1 开始
        }

        // Update SelectedCoNum
        SelectedCoNum = coNum;
        return Task.CompletedTask;
    }
    
    private Task OnItemChanged(Item? item) {
        if (item is null || Model is null) return Task.CompletedTask;

        Model.Item = item.Id;
        var itemLocation = ItemLocations.FirstOrDefault(i => i.Item == item.Id);
        if (itemLocation != null) {
            Model.Location = itemLocation.Location;
        }
        Model.ItemDesc = item.Description;
        SelectedItem = item;
        return Task.CompletedTask;
    }

}
