@page "/login"
@using HuaLingErpApp.Client.Services
@using HuaLingErpApp.Shared.Models
@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject MessageService MessageService
@inject IJSRuntime JSRuntime
@inject ApiService ApiService

<PageTitle>Login - HuaLing ERP</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <img src="favicon.png" alt="Logo" class="login-logo" />
            <h2>HuaLing ERP System</h2>
            <p>Please sign in to your account</p>
        </div>

        <EditForm Model="@loginRequest" OnValidSubmit="@HandleLogin" FormName="LoginForm" class="login-form">
            <DataAnnotationsValidator />
            
            <div class="form-group">
                <label for="username">Username</label>
                <InputText id="username" @bind-Value="loginRequest.UserName" 
                          class="form-control" placeholder="Enter your username" 
                          autocomplete="username" />
                <ValidationMessage For="@(() => loginRequest.UserName)" />
            </div>

            <div class="form-group">
                <label for="password">Password</label>
                <InputText id="password" @bind-Value="loginRequest.Password" 
                          type="password" class="form-control" placeholder="Enter your password"
                          autocomplete="current-password" />
                <ValidationMessage For="@(() => loginRequest.Password)" />
            </div>

            <div class="form-group form-check">
                <InputCheckbox id="rememberMe" @bind-Value="loginRequest.RememberMe" class="form-check-input" />
                <label for="rememberMe" class="form-check-label">Remember me for 7 days</label>
            </div>

            <button type="submit" class="btn btn-primary btn-block" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                    <span>Signing in...</span>
                }
                else
                {
                    <span>Sign In</span>
                }
            </button>
        </EditForm>

        <div class="login-footer">
            <p class="text-muted">
                Default admin credentials:<br />
                Username: <strong>admin</strong><br />
                Password: <strong>Admin123!</strong>
            </p>
            @if (!string.IsNullOrEmpty(returnUrl))
            {
                <p class="text-info mt-2">
                    <small>After login, you will be redirected to: <strong>@returnUrl</strong></small>
                </p>
            }
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-logo {
        width: 60px;
        height: 60px;
        margin-bottom: 20px;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .login-header p {
        color: #666;
        margin-bottom: 0;
    }

    .login-form {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #333;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
    }

    .form-check {
        display: flex;
        align-items: center;
    }

    .form-check-input {
        margin-right: 8px;
    }

    .btn-block {
        width: 100%;
        padding: 12px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }

    .btn-primary:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-primary:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .login-footer {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .login-footer p {
        margin: 0;
        font-size: 12px;
        line-height: 1.5;
    }

    .validation-message {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
    }
</style>

@code {
    [SupplyParameterFromForm]
    private LoginRequest loginRequest { get; set; } = new();
    private bool isLoading = false;
    private string? returnUrl;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // 通过 API 验证真实的认证状态，而不是仅依赖客户端状态
            var response = await ApiService.GetAsync<HttpResponseModel<object>>("api/auth/status");
            
            if (response?.IsSuccess == true)
            {
                var statusData = System.Text.Json.JsonSerializer.Deserialize<AuthStatusData>(
                    response.Data?.ToString() ?? "", 
                    new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (statusData?.IsAuthenticated == true)
                {
                    // 真正已认证，重定向到首页
                    Navigation.NavigateTo("/", true);
                    return;
                }
            }
        }
        catch (Exception)
        {
            // API 调用失败，说明认证有问题，继续显示登录页面
        }

        // 获取 returnUrl
        var uri = new Uri(Navigation.Uri);
        var query = uri.Query;

        if (!string.IsNullOrEmpty(query))
        {
            var queryParams = query.TrimStart('?').Split('&');
            foreach (var param in queryParams)
            {
                var keyValue = param.Split('=');
                if (keyValue.Length == 2 && keyValue[0].Equals("ReturnUrl", StringComparison.OrdinalIgnoreCase))
                {
                    returnUrl = Uri.UnescapeDataString(keyValue[1]);

                    // Validate returnUrl (only allow relative URLs)
                    if (!string.IsNullOrEmpty(returnUrl))
                    {
                        // Ensure it starts with /
                        if (!returnUrl.StartsWith("/"))
                        {
                            returnUrl = "/" + returnUrl;
                        }

                        // Don't redirect to login page itself
                        if (returnUrl.StartsWith("/login"))
                        {
                            returnUrl = null;
                        }
                    }
                    break;
                }
            }
        }
    }

    private async Task HandleLogin()
    {
        if (isLoading) return;

        isLoading = true;
        try
        {
            var response = await ApiService.PostAsync("api/auth/login", loginRequest);

            if (response.IsSuccessStatusCode)
            {
                await MessageService.Show(new MessageOption()
                {
                    Content = "Login successful! Redirecting...",
                    Icon = "fa-solid fa-check-circle",
                    Color = Color.Success
                });

                // Small delay to show the success message
                await Task.Delay(500);

                // Redirect to returnUrl if available, otherwise go to home page
                var redirectUrl = !string.IsNullOrEmpty(returnUrl) ? returnUrl : "/";
                Navigation.NavigateTo(redirectUrl, true);
            }
            else
            {
                await MessageService.Show(new MessageOption()
                {
                    Content = "Login failed. Please check your username and password.",
                    Icon = "fa-solid fa-exclamation-triangle",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption()
            {
                Content = "An error occurred during login. Please try again.",
                Icon = "fa-solid fa-exclamation-triangle",
                Color = Color.Danger
            });
        }
        finally
        {
            isLoading = false;
        }
    }
}
