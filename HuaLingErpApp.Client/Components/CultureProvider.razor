@using System.Globalization
@implements IDisposable

@ChildContent

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    
    private CultureInfo originalCulture = CultureInfo.CurrentCulture;
    private CultureInfo originalUICulture = CultureInfo.CurrentUICulture;

    protected override void OnInitialized()
    {
        // Force English culture
        var englishCulture = new CultureInfo("en-US");
        
        // Store original cultures
        originalCulture = CultureInfo.CurrentCulture;
        originalUICulture = CultureInfo.CurrentUICulture;
        
        // Set English culture
        CultureInfo.CurrentCulture = englishCulture;
        CultureInfo.CurrentUICulture = englishCulture;
        
        // Also set thread cultures
        Thread.CurrentThread.CurrentCulture = englishCulture;
        Thread.CurrentThread.CurrentUICulture = englishCulture;
        
        // Set default cultures
        CultureInfo.DefaultThreadCurrentCulture = englishCulture;
        CultureInfo.DefaultThreadCurrentUICulture = englishCulture;
    }

    public void Dispose()
    {
        // Restore original cultures if needed
        // CultureInfo.CurrentCulture = originalCulture;
        // CultureInfo.CurrentUICulture = originalUICulture;
    }
}
