@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

@if (isAuthenticated)
{
    <AuthorizeView Roles="@RequiredRoles">
        <Authorized>
            @ChildContent
        </Authorized>
        <NotAuthorized>
            <div class="d-flex justify-content-center align-items-center" style="height: 60vh;">
                <div class="text-center">
                    <div class="alert alert-warning" role="alert" style="max-width: 500px;">
                        <h4 class="alert-heading">
                            <i class="fa-solid fa-lock"></i> Access Denied
                        </h4>
                        <p>You don't have permission to access @PageName.</p>
                        <p class="mb-0">This page requires <strong>@RequiredRoles</strong> role.</p>
                        <hr>
                        <button class="btn btn-primary" @onclick="@((() => Navigation.NavigateTo("/")))">
                            <i class="fa-solid fa-home"></i> Go to Dashboard
                        </button>
                    </div>
                </div>
            </div>
        </NotAuthorized>
    </AuthorizeView>
}
else if (isLoading)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Checking authentication...</p>
        </div>
    </div>
}
else if (showAuthRequired)
{
    @* RequireAuth 的认证提示界面 *@
    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
        <div class="text-center">
            <div class="alert alert-warning" role="alert" style="max-width: 500px;">
                <h4 class="alert-heading">
                    <i class="fa-solid fa-lock"></i> Authentication Required
                </h4>
                <p>You need to log in to access this page.</p>
                <hr>
                <div class="d-flex justify-content-between align-items-center">
                    <button class="btn btn-primary" @onclick="HandleRedirectToLogin" disabled="@isRedirecting">
                        <i class="fa-solid fa-sign-in-alt"></i>
                        @if (isRedirecting)
                        {
                            <span>Redirecting...</span>
                        }
                        else
                        {
                            <span>Go to Login</span>
                        }
                    </button>
                    <small class="text-muted">
                        Auto redirect in <strong>@countdown</strong> seconds
                    </small>
                </div>
                <div class="progress mt-2" style="height: 4px;">
                    <div class="progress-bar" role="progressbar"
                         style="width: @((3 - countdown + 1) * 33.33)%">
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter] public string RequiredRoles { get; set; } = "";
    [Parameter] public string PageName { get; set; } = "this page";
    [Parameter] public RenderFragment? ChildContent { get; set; }

    // RequireAuth 的逻辑
    private bool isAuthenticated = false;
    private bool isLoading = true;
    private bool showAuthRequired = false;
    private bool isRedirecting = false;
    private int countdown = 3;
    private System.Threading.Timer? countdownTimer;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            isAuthenticated = authState.User.Identity?.IsAuthenticated ?? false;

            if (!isAuthenticated)
            {
                var currentUri = Navigation.ToBaseRelativePath(Navigation.Uri);
                if (currentUri != "login" && !currentUri.StartsWith("login"))
                {
                    showAuthRequired = true;
                    StartCountdown();
                }
            }
        }
        catch (Exception)
        {
            isAuthenticated = false;
            showAuthRequired = true;
            StartCountdown();
        }
        finally
        {
            isLoading = false;
        }
    }

    private void StartCountdown()
    {
        countdownTimer = new System.Threading.Timer(OnCountdownTick, null,
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    private async void OnCountdownTick(object? state)
    {
        if (isRedirecting) return;
        countdown--;
        if (countdown <= 0)
        {
            await InvokeAsync(async () => await HandleRedirectToLogin());
        }
        else
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task HandleRedirectToLogin()
    {
        if (isRedirecting) return;
        isRedirecting = true;
        StateHasChanged();

        try
        {
            var currentUri = Navigation.ToBaseRelativePath(Navigation.Uri);
            if (currentUri != "login" && !currentUri.StartsWith("login"))
            {
                var returnUrl = Uri.EscapeDataString(currentUri);
                var loginUrl = $"/login?ReturnUrl={returnUrl}";
                await JSRuntime.InvokeVoidAsync("window.location.href", loginUrl);
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("window.location.href", "/login");
            }
        }
        catch
        {
            var currentUri = Navigation.ToBaseRelativePath(Navigation.Uri);
            if (currentUri != "login" && !currentUri.StartsWith("login"))
            {
                var returnUrl = Uri.EscapeDataString(currentUri);
                Navigation.NavigateTo($"/login?ReturnUrl={returnUrl}", forceLoad: true);
            }
            else
            {
                Navigation.NavigateTo("/login", forceLoad: true);
            }
        }
    }

    public void Dispose()
    {
        countdownTimer?.Dispose();
    }
}